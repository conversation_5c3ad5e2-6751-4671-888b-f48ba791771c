@media print {
    @page {
        size: A4 portrait;

        @top-left {
            content: element(headerTitleRunning);
            color: #02588B;
            width: 100%;
        }
    }

    th {
        text-align: left;
    }

    .header-title {
        font-weight: bold;
        position: running(headerTitleRunning);
    }

    .header-title .header-title-upper {
        font-size: 16px;
    }

    .header-title .header-title-lower {
        font-size: 13px;
    }

    .header-title .header-divider {
        width: 100%;
        height: 2px;
        background: #DEE8F2;
    }

    .pagedjs_page {
        position: relative;
    }

    .pagedjs_page:before {
        content: "仅供合格投资者参考，不得转载或给第三方传阅";
        white-space: pre;
        text-align: center;
        position: absolute;
        z-index: 9999;
        color: rgba(0, 0, 0, 0.05);
        font-size: 36px;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) rotate(-30deg);
        pointer-events: none;
    }
}

body {
    font-size: 14px;
    font-family: 'Source Han Sans CN', 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

h1, h2, h3, strong {
    color: #02588B;
}

h1 {
    font-size: 28px;
    font-weight: bold;
    padding-bottom: 4px;
    margin-bottom: 12px;
    border-bottom: 2px solid #DEE8F2;
}

h2 {
    font-size: 20px;
    font-weight: bold;
    margin-top: 16px;
    border-bottom: 2px solid #DEE8F2;
}

h3 {
    font-size: 18px;
    font-weight: bold;
    margin-top: 16px;
    display: flex;
    align-items: center;
}

h3::after {
    content: "";
    flex: 1;
    height: 2px;
    background: #DEE8F2;
    margin-left: 4px;
}

.week-report-header {
    font-weight: bold;
    font-size: 16px;
    margin-top: 16px;
    background: #DEE8F2;
    color: #02588B;
    padding: 1px 6px;
}

.week-report-chart-title {
    background: #DEE8F2;
    color: #02588B;
    padding: 1px 3px;
    margin-top: 12px;
}

.week-report-notice {
    color: #888;
}

.week-report-notice p {
    text-indent: 0;
}

.week-report-notice p:last-child {
    margin-bottom: 0;
}

p {
    text-indent: 2em;
}

table {
    font-size: 14px;
    border-collapse: collapse;
}

table, th, td {
    /* border-top: 1px solid #DEE8F2; */
    border-bottom: 1px solid #DEE8F2;
    color: #02588B;
}

th, td {
    padding: 1px 3px;
}

.basic-info {
    display: flex;
}

.basic-info > table {
    flex: 1;
}

.basic-info > table:first-child {
    margin-right: 4px;
}

.basic-info > table > tbody > tr:first-child {
    background: #DEE8F2;
}

.basic-info >table > tbody > tr > *:nth-child(odd) {
    font-weight: bold;
    width: 90px;
}

.extend-items-table {
    width: 100%;
}

.extend-items-table tr > *:first-child {
    font-weight: bold;
}

.performance-table {
    width: 100%;
    break-inside: avoid;
    font-size: 12px;
}

.performance-table td {
    letter-spacing: -1px;
}

.performance-table th, .performance-table .year-divide > td, .performance-table .performance-year {
    border-bottom-width: 2px;
    border-bottom-color: #abbddf;
}

.week-extend-items-table {
    width: 100%;
}

.week-extend-items-table th {
    font-weight: bold;
}

.table-group table:not(:last-child) {
    margin-bottom: 1.5em;
}

.performance-notify {
    font-weight: bold;
    color: #C00000;
}

.indexes-table {
    width: 100%;
    margin-bottom: 2em;
}

.indexes-table > tbody > tr:first-child {
    background: #DEE8F2;
}

.market-chart-notice {
    display: flex;
    color: #02588B;
}

.market-chart-notice :last-child {
    flex: 1;
}

.market-comment {
    margin-top: 1em;
    margin-bottom: 2em;
    color: #02588B;
}

.common-notice {
    background: #F2F2F2;
    padding: 6px 10px;
}

.common-notice ol {
    padding-left: 2em;
    margin-bottom: 0;
}

.appendix {
    break-inside: avoid;
}

.appendix h4 {
    background: #DEE8F2;
    color: #02588B;
    padding: 6px 10px;
    font-weight: bold;
    margin-bottom: 0.5em;
}

.appendix .words-explain {
    border: 1px solid black;
    padding: 6px 10px 0 0;
    margin-bottom: 0;
    line-height: 18px;
}

.appendix strong {
    color: #02588B !important;
}

.appendix p {
    margin-bottom: 0.5em;
    line-height: 18px;
}
