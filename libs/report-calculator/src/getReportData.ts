import { Dayjs as Moment } from 'dayjs';
import { findLast, meanBy, sum } from 'lodash';
import { SomeNetValue } from './NetValue';
import tradeDateUtils from './tradeDateUtils';

export interface BaseReportData {
    date: Moment;
    netValue: number;
    backfill: boolean;
    beforeHigh: number;
    beforeHighDate: Moment;
    beforeLow: number;
    beforeLowDate: Moment;
    return: number;
    yearReturn: number;
    seasonReturn: number;
    monthReturn: number;
    weekReturn: number;
    dayReturn: number;
}

export interface ExtendReportData {
    // 最大回撤
    maxRetracement: number;
    // 年化收益
    annualizedReturn: number;
    // 年化标准差
    annualizedStd: number;
    // 年化下半标准差
    annualizedHalfStd: number;
    sharpe: number;
    sortino: number;
    doNotShow: boolean;
}

/**
 * 获取之后所使用的date utils
 * @param dates 交易日列表
 * @param startDate 建仓日
 */
function getUtils(dates: Moment[], startDate: Moment) {
    return tradeDateUtils(dates, startDate.subtract(1, 'day'));
}

/**
 * 获取报告基础信息
 * @param utils 交易日相关的utils
 * @param startDate 建仓日
 * @param endDate 结束日期
 * @param netValues 按日期递增排序的净值
 * @returns data: 基础信息列表，从建仓日开始每个交易日一条数据，一直到netValues中最后一条的日期，不一定和netValues一一对应
 *          initialValue: 建仓前一日净值
 */
function getBaseReportData(
    utils: ReturnType<typeof tradeDateUtils>,
    startDate: Moment,
    endDate: Moment,
    netValues: SomeNetValue[],
): { data: BaseReportData[]; initialValue: number } {
    if (!netValues || netValues.length <= 0) {
        throw '无净值数据';
    }

    if (startDate.isBefore(netValues[0].date)) {
        throw '建仓日无净值';
    }

    // 把netValues之间的空隙都填满
    const netValueDict: Record<string, { netValue: number; backfill: boolean }> = {};
    let curNetValueIdx = 0;
    for (let d = netValues[0].date; !d.isAfter(endDate); d = d.add(1, 'day')) {
        if (curNetValueIdx < netValues.length && !d.isBefore(netValues[curNetValueIdx].date)) {
            curNetValueIdx += 1;
        }
        netValueDict[d.format('YYYYMMDD')] = {
            netValue: netValues[curNetValueIdx - 1].netValue,
            backfill: d.format('YYYYMMDD') != netValues[curNetValueIdx - 1].date.format('YYYYMMDD'),
        };
    }
    // 第一日前一天净值设为1，防止一些边界情况。
    netValueDict[netValues[0].date.subtract(1, 'day').format('YYYYMMDD')] = {
        netValue: 1.0,
        backfill: true,
    };

    const netValueOf = (day: Moment) => netValueDict[day.format('YYYYMMDD')];

    const result: BaseReportData[] = [];
    const beforeStartDate = startDate.subtract(1, 'day');
    const initialValue = netValueOf(beforeStartDate);
    let beforeHigh = initialValue;
    let beforeHighDate = beforeStartDate;
    let beforeLow = initialValue;
    let beforeLowDate = beforeStartDate;

    for (let d = startDate; !d.isAfter(endDate); d = d.add(1, 'day')) {
        if (utils.isValid(d)) {
            const currentValue = netValueOf(d);
            if (currentValue.netValue >= beforeHigh.netValue) {
                beforeHigh = currentValue;
                beforeHighDate = d;
            }
            if (currentValue.netValue <= beforeLow.netValue) {
                beforeLow = currentValue;
                beforeLowDate = d;
            }
            result.push({
                date: d,
                netValue: currentValue.netValue,
                backfill: currentValue.backfill,
                beforeHigh: beforeHigh.netValue,
                beforeHighDate,
                beforeLow: beforeLow.netValue,
                beforeLowDate,
                return: currentValue.netValue / initialValue.netValue - 1,
                yearReturn: currentValue.netValue / netValueOf(utils.lastYear(d)).netValue - 1,
                seasonReturn: currentValue.netValue / netValueOf(utils.lastSeason(d)).netValue - 1,
                monthReturn: currentValue.netValue / netValueOf(utils.lastMonth(d)).netValue - 1,
                weekReturn: currentValue.netValue / netValueOf(utils.lastWeek(d)).netValue - 1,
                dayReturn: currentValue.netValue / netValueOf(utils.validBefore(d.subtract(1, 'day'))).netValue - 1,
            });
        }
    }
    return { data: result, initialValue: initialValue.netValue };
}

type SequenceType = 'day' | 'week' | 'month';

const seqReturnType: Record<SequenceType, keyof BaseReportData> = {
    day: 'dayReturn',
    week: 'weekReturn',
    month: 'monthReturn',
};

const annualizeParam = {
    day: 252,
    week: 52,
    month: 12,
};

function stdBy<T>(data: T[], key: keyof T) {
    if (data.length <= 1) return 0;
    const avg = meanBy(data, key);
    return Math.sqrt(sum(data.map(x => Math.pow((x[key] as any) - avg, 2))) / (data.length - 1));
}

/**
 * 获取报告其他信息
 * @param utils 交易日相关的utils
 * @param startDate 建仓日
 * @param baseReportData 报告基础信息, getBaseReportData的返回结果
 * @param initialValue 建仓前一日净值, getBaseReportData的返回结果
 * @param sequenceType 序列类型，日序列/周序列/月序列
 */
function getExtendReportData(
    utils: ReturnType<typeof tradeDateUtils>,
    startDate: Moment,
    baseReportData: { data: BaseReportData[]; initialValue: number },
    sequenceType: 'day' | 'week' | 'month',
    sharpeRf: number,
    sortinoMar: number,
): ExtendReportData {
    // 根据序列类型进行筛选
    const seq: BaseReportData[] = [];
    if (sequenceType === 'day') {
        for (let x of baseReportData.data) {
            if (utils.isValid(x.date)) {
                seq.push(x);
            }
        }
    } else if (sequenceType === 'week') {
        for (let x of baseReportData.data) {
            if (utils.lastWeek(x.date.add(1, 'week')).isSame(x.date)) {
                seq.push(x);
            }
        }
        const last = baseReportData.data[baseReportData.data.length - 1];
        if (last !== seq[seq.length - 1]) {
            seq.push(last);
        }
    } else if (sequenceType === 'month') {
        for (let x of baseReportData.data) {
            if (utils.lastMonth(x.date.add(1, 'month')).isSame(x.date)) {
                seq.push(x);
            }
        }
        const last = baseReportData.data[baseReportData.data.length - 1];
        if (last !== seq[seq.length - 1]) {
            seq.push(last);
        }
    }

    if (seq.length === 0) {
        return {
            maxRetracement: 0,
            annualizedReturn: 0,
            annualizedStd: 0,
            annualizedHalfStd: 0,
            sharpe: 0,
            sortino: 0,
            doNotShow: true,
        };
    }

    // 最大回撤
    let maxRetracement = 0;
    let maxValue = baseReportData.initialValue;
    for (let x of seq) {
        const retracementNow = (maxValue - x.netValue) / maxValue;
        if (retracementNow > maxRetracement) {
            maxRetracement = retracementNow;
        }
        if (x.netValue > maxValue) {
            maxValue = x.netValue;
        }
    }

    const returnTypeKey = seqReturnType[sequenceType];

    let annualizedReturn = 0;
    if (sequenceType === 'day') {
        // 日度年化收益率
        annualizedReturn =
            Math.pow(seq[seq.length - 1].netValue / baseReportData.initialValue, annualizeParam.day / seq.length) - 1;
    } else {
        // 周度月度年化收益率
        annualizedReturn = Math.pow(1 + meanBy(seq, returnTypeKey), annualizeParam[sequenceType]) - 1;
    }

    // 年化标准差
    const annualizedStd = stdBy(seq, returnTypeKey) * Math.sqrt(annualizeParam[sequenceType]);
    // 年化下半标准差
    const annualizedHalfStd =
        stdBy(
            seq.filter(x => (x[returnTypeKey] as number) < sortinoMar / annualizeParam[sequenceType]),
            returnTypeKey,
        ) * Math.sqrt(annualizeParam[sequenceType]);

    const sharpe = annualizedStd ? (annualizedReturn - sharpeRf) / annualizedStd : null;

    const sortino = annualizedHalfStd ? (annualizedReturn - sortinoMar) / annualizedHalfStd : null;

    return {
        maxRetracement,
        annualizedReturn,
        annualizedStd,
        annualizedHalfStd,
        sharpe,
        sortino,
        doNotShow:
            startDate.add(183, 'day').isAfter(baseReportData.data[baseReportData.data.length - 1].date) ||
            annualizedReturn < 0,
    };
}

/**
 * 获取指数净值
 * @param indexList 指数序列
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @param initialValue 开仓前一日基金净值
 */
function getIndexValues(
    indexList: SomeNetValue[],
    startDate: Moment,
    endDate: Moment,
    initialValue: number,
): SomeNetValue[] {
    const indexInitial = findLast(indexList, x => x.date.isBefore(startDate));
    return indexList
        .filter(x => !x.date.isBefore(startDate) && !x.date.isAfter(endDate))
        .map(x => ({
            date: x.date,
            netValue: (x.netValue / indexInitial.netValue) * initialValue,
        }));
}

export default {
    getUtils,
    getBaseReportData,
    getExtendReportData,
    getIndexValues,
};
