import moment, { Dayjs as Moment } from 'dayjs';
import 'dayjs/plugin/weekOfYear';

// 输入：从小到大排序的所有交易日，及产品建仓日
export default function(dates: Moment[], minDate?: Moment) {
    const datesHash: Record<string, boolean> = {};

    // 年月 -> 该月交易日数组，由小到大排列
    const tradeDayByMonth: Record<string, Moment[]> = {};

    // 分周的交易日
    const tradeDayByWeek: Moment[][] = [];

    for (let x of dates) {
        datesHash[x.format('YYYYMMDD')] = true;
        const ym = x.format('YYYYMM');
        if (!tradeDayByMonth[ym]) tradeDayByMonth[ym] = [];
        tradeDayByMonth[ym].push(x);
    }
    let lastDay: Moment = null;
    for (let x of dates) {
        if (!lastDay || lastDay.week() !== x.week()) {
            tradeDayByWeek.push([]);
        }
        tradeDayByWeek[tradeDayByWeek.length - 1].push(x);
        lastDay = x;
    }

    for (let i in tradeDayByMonth)
        tradeDayByMonth[i].sort((a, b) => a.format('YYYYMMDD').localeCompare(b.format('YYYYMMDD')));

    const isValid = (d: Moment) => !!datesHash[d.format('YYYYMMDD')];

    // 之前（含）最后一个交易日
    const validBefore = (d: Moment) => {
        for (let m = d; m.isAfter(minDate || '2019-01-02'); m = m.subtract(1, 'day')) {
            if (datesHash[m.format('YYYYMMDD')]) {
                return m;
            }
        }
        return minDate || moment('2019-01-02');
    };

    // 之后（含）第一个交易日
    const validAfter = (d: Moment) => {
        // 超过交易日列表最后一个交易日，返回null
        if (d.isAfter(dates[dates.length - 1])) {
            return null;
        }
        for (let m = d; !m.isAfter(dates[dates.length - 1]); m = m.add(1, 'day')) {
            if (datesHash[m.format('YYYYMMDD')]) {
                return m;
            }
        }
    };

    return {
        current: () => validBefore(moment()),
        isValid,
        validBefore,
        validAfter,
        lastWeek: (date: Moment) => validBefore(date.startOf('week').subtract(1, 'day')),
        lastMonth: (date: Moment) => validBefore(date.startOf('month').subtract(1, 'day')),
        lastSeason: (date: Moment) =>
            validBefore(moment(`${date.year()}-${Math.floor(date.month() / 3) * 3 + 1}-01`).subtract(1, 'day')),
        lastYear: (date: Moment) => validBefore(date.startOf('year').subtract(1, 'day')),
        // 某日之后（不含）第一个“本周最后交易日”
        weeklyLastAfter: (date: Moment) => {
            let v0 = validAfter(date.add(1, 'day'));
            if (!v0) {
                return null;
            }
            let v1 = validAfter(v0.add(1, 'day'));
            while (v1 && v0.week() === v1.week()) {
                v0 = v1;
                v1 = validAfter(v1.add(1, 'day'));
            }
            return v0;
        },
        // 某日之后（不含）第一个“本月最后交易日”
        monthlyLastAfter: (date: Moment) => {
            let v0 = validAfter(date.add(1, 'day'));
            if (!v0) {
                return null;
            }
            let v1 = validAfter(v0.add(1, 'day'));
            while (v1 && v0.month() === v1.month()) {
                v0 = v1;
                v1 = validAfter(v1.add(1, 'day'));
            }
            return v0;
        },
        // 获取某月的开放日列表
        // getListOfMonth(year: number, month: number) {
        //     return (
        //         tradeDayByMonth[
        //             moment(new Date(year, month - 1)).format('YYYYMM')
        //         ] || []
        //     );
        // },
        getListOfMonth(year: number, month: number) {
            return tradeDayByMonth[moment(new Date(year, month)).format('YYYYMM')] || [];
        },
        // 获取某年分周的交易日，为完整周的所有日期，可能包含上一年最后几天及下一年头几天的交易日
        getListOfWeek(year: number) {
            const result: Moment[][] = [];
            for (let x of tradeDayByWeek) {
                if (x[x.length - 1].year() === year) {
                    result.push(x);
                }
                if (x[0].year() > year) {
                    break;
                }
            }
            return result;
        },
        // 区间内交易日个数
        getDateCountInRange(start: Moment, end: Moment) {
            let result = 0;
            for (let x of dates) {
                if (!x.isBefore(start) && !x.isAfter(end)) {
                    result++;
                }
            }
            return result;
        },
    };
}
