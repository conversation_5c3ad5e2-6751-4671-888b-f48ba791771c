import moment, { Dayjs } from 'dayjs';
import { NetValues, SomeNetValue } from './NetValue';

// 计算复权净值
export default function getCompoundValues(data: NetValues[], startDate: Dayjs): SomeNetValue[] {
    if (!data || data.length == 0) {
        return [];
    }
    const result: SomeNetValue[] = [];
    let currentCompound = data[0].netValue;
    result.push({ date: moment(data[0].date), netValue: currentCompound });
    let flag = false;
    for (let i = 1; i < data.length; ++i) {
        const now = data[i];
        const before = data[i - 1];
        const date = moment(now.date);
        if (date.isBefore(startDate)) {
            currentCompound = now.cumNetValue;
            result.push({ date, netValue: currentCompound });
        } else {
            // 含分红的当日净值
            const valueWithDividend = now.cumNetValue - (before.cumNetValue - before.netValue);
            currentCompound = currentCompound * (valueWithDividend / before.netValue);
            if (isNaN(currentCompound) || !isFinite(currentCompound)) {
                if (!flag) {
                    currentCompound = now.cumNetValue;
                } else {
                    throw '净值序列包含0';
                }
            } else {
                flag = true;
            }
            result.push({ date, netValue: currentCompound });
        }
    }

    return result;
}
