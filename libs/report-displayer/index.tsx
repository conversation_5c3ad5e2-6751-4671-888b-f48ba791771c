import { findLast, maxBy, minBy } from 'lodash';
import moment, { Dayjs as Moment } from 'dayjs';
import React, { useMemo } from 'react';
import classnames from 'classnames';
import ShadowView from 'shadow-view';
import { Line } from '@ant-design/charts';
import timePretty from '@antv/scale/src/tick-method/time-pretty';
import { NetValues, SomeNetValue, getReportData, getCompoundValues } from 'report-calculator';

export interface DisplayerProps {
    // 交易日列表，按日期从小到大排序
    tradeDates: Moment[];
    // 产品名称
    name: string;
    // 产品类型
    type: string;
    // 建仓日
    startDate: Moment;
    // 净值序列
    netValuesList: NetValues[];
    // 报告日期
    reportDate: Moment;
    // 对标指数序列
    indexList?: SomeNetValue[];

    sharpeRf: number;

    sortinoMar: number;

    printMode?: boolean;

    throwWhenValueNotFound?: boolean;
}

type ReportItemProps = React.HTMLAttributes<HTMLDivElement> & {
    title: React.ReactNode;
    value: React.ReactNode;
};

type ExcessAnalysis = { netValue: number; index: number; excess: number };

const rowHeight = 26;

const displayerCss = (
    <style>{`
        .sandbox {
            all: initial;
            font-family: 'Source Han Sans CN', 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            font-size: 12px;
        }

        style {
            display: none;
        }

        div {
            display: block;
        }

        .wrapper {
            width: 794px;
        }

        .page {
            height: 1123px;
            padding: 108px 96px;
            box-sizing: border-box;
            background: #F2F2F2;
            position: relative;
        }

        .item {
            display: flex;
            height: ${rowHeight}px;
            align-items: center;
        }

        .item-title,.item-value,.padding-item {
            padding: 0 4px;
        }

        .item-title {
            flex: 1;
            text-align: left;
        }

        .item-value {
            flex: 1;
            text-align: center;
        }

        .title {
            height: ${rowHeight * 2}px;
            display: flex;
            margin-bottom: ${rowHeight}px;
        }

        .title-name {
            background: rgb(227, 111, 111);
            color: white;
            text-align: center;
            font-weight: bold;
            flex: 1;
        }

        .main-title .title-name {
            line-height: ${rowHeight * 2}px;
            font-size: 18px;
        }

        .title-content {
            background: #808080;
            color: white;
            text-align: center;
            flex: 1;
        }

        .title-content .item-title, .title-content .item-value {
            color: white;
            font-weight: bold;
            text-align: center;
        }

        .subtitle {
            height: ${rowHeight}px;
            line-height: ${rowHeight}px;
            margin-bottom: 0;
        }

        .subtitle .title-name {
            line-height: ${rowHeight}px;
        }

        .two-row, .four-row {
            display: flex;
            flex-wrap: wrap;
        }

        .two-row > div {
            width: 50%;
        }

        .four-row > div {
            width: 25%;
            box-sizing: border-box;
        }

        .important {
            font-weight: bold;
            background: rgb(255, 225, 228);
        }

        .divider {
            width: 100% !important;
            font-weight: bold;
            height: ${rowHeight - 4}px;
            line-height: ${rowHeight - 4}px;
            border-top: 2px solid rgb(99, 37, 35);
            border-bottom: 2px solid rgb(99, 37, 35);
            background: rgb(255, 225, 228);
            display: flex;
        }
        
        .divider > div {
            flex: 1;
            text-align: center;
        }

        .chart-area {
            width: 100%;
            height: 360px;
            margin-top: ${rowHeight}px;
        }

        .chart-inner-area {
            height: calc(100% - 32px);
        }

        .chart-title {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            color: rgb(89, 89, 89);
            margin-bottom: 8px;
        }

        .footer {
            color: red;
            font-weight: bold;
            position: absolute;
            right: 32px;
            bottom: 20px;
        }

        .flex-center {
            justify-content: center;
        }
    `}</style>
);

function percentPipe(val: number, colorize?: boolean) {
    if (val == null) return '';
    const result = (val * 100).toFixed(2) + '%';
    if (colorize) {
        return (
            <span
                style={{
                    fontWeight: 'bold',
                    color: val >= 0 ? 'rgb(156, 0, 6)' : 'rgb(0, 176, 80)',
                }}
            >
                {result}
            </span>
        );
    } else {
        return result;
    }
}

export function alignDate<T1 extends SomeNetValue, T2 extends SomeNetValue>(a: T1[], b: T2[]) {
    const result: [T1, T2][] = [];
    for (let i = 0, j = 0; i < a.length && j < b.length; ) {
        if (a[i].date.format('YYYYMMDD') === b[j].date.format('YYYYMMDD')) {
            result.push([a[i], b[j]]);
            i += 1;
            j += 1;
        } else if (a[i].date.isBefore(b[j].date)) {
            i += 1;
        } else {
            j += 1;
        }
    }
    return result;
}

const Footer = () => {
    return <div className="footer">*仅供合格投资者参考，不得通过任何公开渠道进行公开宣传</div>;
};

const ReportItem = (props: ReportItemProps) => {
    const { title, value, className, ...otherProps } = props;
    return (
        <div className={classnames(className, 'item')} {...otherProps}>
            <div className="item-title">{title}</div>
            <div className="item-value">{value}</div>
        </div>
    );
};

const ReportDisplayer: React.FunctionComponent<DisplayerProps> = props => {
    const {
        tradeDates,
        name,
        type,
        startDate,
        netValuesList,
        reportDate,
        indexList,
        sharpeRf,
        sortinoMar,
        printMode,
        throwWhenValueNotFound,
    } = props;

    const unitNetValues = useMemo<SomeNetValue[]>(
        () =>
            netValuesList.map(x => ({
                date: moment(x.date),
                netValue: x.netValue,
            })),
        [netValuesList],
    );
    const cumNetValues = useMemo<SomeNetValue[]>(
        () =>
            netValuesList.map(x => ({
                date: moment(x.date),
                netValue: x.cumNetValue,
            })),
        [netValuesList],
    );
    const compoundValues = useMemo<SomeNetValue[]>(() => getCompoundValues(netValuesList, startDate), [netValuesList]);
    const dateUtils = useMemo(() => getReportData.getUtils(tradeDates, startDate.add(1, 'day')), [
        tradeDates,
        startDate,
    ]);
    /* const unitBaseReport = useMemo(
        () => getReportData.getBaseReportData(dateUtils, startDate, reportDate, unitNetValues),
        [tradeDates, startDate, reportDate, netValuesList]
    ); */
    /* const cumBaseReport = useMemo(
        () => getReportData.getBaseReportData(dateUtils, startDate, reportDate, cumNetValues),
        [tradeDates, startDate, reportDate, netValuesList]
    ); */
    const compoundBaseReport = useMemo(
        () => getReportData.getBaseReportData(dateUtils, startDate.add(1, 'day'), reportDate, compoundValues),
        [tradeDates, startDate, reportDate, netValuesList],
    );

    if (compoundBaseReport.initialValue === 0) {
        throw '存在净值为0的日期';
    }

    const extendReport = useMemo(() => {
        const daily = getReportData.getExtendReportData(
            dateUtils,
            startDate.add(1, 'day'),
            compoundBaseReport,
            'day',
            sharpeRf,
            sortinoMar,
        );
        const weekly = getReportData.getExtendReportData(
            dateUtils,
            startDate.add(1, 'day'),
            compoundBaseReport,
            'week',
            sharpeRf,
            sortinoMar,
        );
        return { daily, weekly };
    }, [tradeDates, startDate, reportDate, netValuesList, type, sharpeRf, sortinoMar]);

    const indexValues = useMemo(
        () =>
            indexList
                ? getReportData.getIndexValues(
                      indexList,
                      startDate.add(1, 'day'),
                      reportDate,
                      compoundBaseReport.initialValue,
                  )
                : [],
        [tradeDates, startDate, reportDate, netValuesList, indexList],
    );

    const indexBaseReport = useMemo(
        () => getReportData.getBaseReportData(dateUtils, startDate.add(1, 'day'), reportDate, indexList),
        [indexValues],
    );

    const reportDateStr = reportDate.format('YYYYMMDD');
    const currentNetValue = netValuesList.find(x => x.date === reportDate.format('YYYY-MM-DD'));
    const currentUnitValue = unitNetValues.find(x => x.date.format('YYYYMMDD') === reportDateStr)?.netValue;
    const currentCumValue = cumNetValues.find(x => x.date.format('YYYYMMDD') === reportDateStr)?.netValue;
    const currentCumBaseReport = compoundBaseReport.data.find(x => x.date.format('YYYYMMDD') === reportDateStr);
    const currentIndexBaseReport = indexBaseReport.data.find(x => x.date.format('YYYYMMDD') === reportDateStr);
    const yesterdayCumBaseReport = findLast(compoundBaseReport.data, x => x.date.isBefore(reportDate.startOf('date')));
    const reportLastWeek = dateUtils.lastWeek(reportDate).format('YYYYMMDD');
    const lastWeekCumBaseReport = compoundBaseReport.data.find(x => x.date.format('YYYYMMDD') === reportLastWeek);

    if (
        throwWhenValueNotFound &&
        (currentCumValue == null || currentCumBaseReport == null || currentUnitValue == null)
    ) {
        throw '当日净值未找到';
    }

    const excessAnalysis: ExcessAnalysis[] = [];
    if (type.endsWith('指增')) {
        excessAnalysis.push(
            {
                netValue: currentCumBaseReport.return,
                index: currentIndexBaseReport.return,
                excess: 0,
            },
            {
                netValue: currentCumBaseReport.yearReturn,
                index: currentIndexBaseReport.yearReturn,
                excess: 0,
            },
            {
                netValue: currentCumBaseReport.seasonReturn,
                index: currentIndexBaseReport.seasonReturn,
                excess: 0,
            },
            {
                netValue: currentCumBaseReport.monthReturn,
                index: currentIndexBaseReport.monthReturn,
                excess: 0,
            },
            {
                netValue: currentCumBaseReport.weekReturn,
                index: currentIndexBaseReport.weekReturn,
                excess: 0,
            },
            {
                netValue: currentCumBaseReport.dayReturn,
                index: currentIndexBaseReport.dayReturn,
                excess: 0,
            },
        );
        for (let x of excessAnalysis) {
            x.excess = x.netValue - x.index;
        }
    }

    const indexType = type.endsWith('指增') ? type.replace('指增', '指数') : '中证500指数';

    const startDateStr = startDate.format('YYYY/MM/DD');

    const chartData = [
        {
            date: startDateStr,
            type: '复权净值',
            value: 1,
        },
        ...compoundBaseReport.data
            .filter(x => !x.backfill)
            .map(x => ({
                date: x.date.format('YYYY/MM/DD'),
                type: '复权净值',
                value: x.netValue,
            })),
        {
            date: startDateStr,
            type: indexType,
            value: 1,
        },
        ...indexValues.map(x => ({
            date: x.date.format('YYYY/MM/DD'),
            type: indexType,
            value: x.netValue,
        })),
    ];

    const minVal = minBy(chartData, 'value').value;
    const maxVal = maxBy(chartData, 'value').value;

    const chartConfig = {
        animation: !printMode as any,
        renderer: printMode ? ('svg' as 'svg') : ('canvas' as 'canvas'),
        padding: 'auto' as 'auto',
        data: chartData,
        xField: 'date',
        yField: 'value',
        xAxis: {
            type: 'time',
            tickMethod: (cfg: any) => {
                const result = timePretty(cfg);
                return result.slice(1, result.length - 1);
            },
        },
        yAxis: {
            min: Math.floor((minVal - 0.05) * 10) / 10,
            max: Math.ceil((maxVal + 0.05) * 10) / 10,
        },
        legend: { position: 'bottom' as 'bottom' },
        seriesField: 'type',
        responsive: true,
        color: d => (d.type === '复权净值' ? 'rgb(192, 0, 0)' : 'rgb(0, 112, 192)'),
    };

    const excessChartConfig = {
        animation: !printMode as any,
        renderer: printMode ? ('svg' as 'svg') : ('canvas' as 'canvas'),
        padding: 'auto' as 'auto',
        xField: 'date',
        yField: 'value',
        xAxis: {
            type: 'time',
            tickMethod: (cfg: any) => {
                const result = timePretty(cfg);
                return result.slice(1, result.length - 1);
            },
        },
        yAxis: {
            min: 0.9,
        },
        legend: { position: 'bottom' as 'bottom' },
        seriesField: 'type',
        responsive: true,
        color: 'rgb(192, 0, 0)',
    };

    return (
        <div className="sandbox">
            {displayerCss}
            <div className="wrapper">
                <div className="page">
                    <div className="title main-title">
                        <div className="title-name">{name}</div>
                        <div className="title-content">
                            <ReportItem title="策略类型" value={type} />
                            <ReportItem title="更新日期" value={reportDate.format('YYYY-MM-DD')} />
                        </div>
                    </div>
                    <div className="title subtitle">
                        <div className="title-name">产品净值分析</div>
                        <div className="title-content">
                            <ReportItem title="首次建仓日" value={startDate.format('YYYY-MM-DD')} />
                        </div>
                    </div>
                    <div className="two-row">
                        <ReportItem title="单位净值" value={currentUnitValue?.toFixed(4)} className="important" />
                        <ReportItem title="累计净值" value={currentCumValue?.toFixed(4)} className="important" />
                        <ReportItem
                            title="复权净值"
                            value={currentCumBaseReport?.netValue?.toFixed(4)}
                            className="important"
                        />
                        <ReportItem
                            title="累计单位分红"
                            value={
                                currentCumValue && currentUnitValue
                                    ? (currentCumValue - currentUnitValue).toFixed(4)
                                    : null
                            }
                            className="important"
                        />
                        <ReportItem title="前高" value={currentCumBaseReport?.beforeHigh?.toFixed(4)} />
                        <ReportItem title="前低" value={currentCumBaseReport?.beforeLow?.toFixed(4)} />
                        <ReportItem
                            title="前高日期"
                            value={currentCumBaseReport?.beforeHighDate?.format('YYYY-MM-DD')}
                        />
                        <ReportItem
                            title="前低日期"
                            value={currentCumBaseReport?.beforeLowDate?.format('YYYY-MM-DD')}
                        />
                        <ReportItem
                            title="建仓以来收益率（%）"
                            value={percentPipe(currentCumBaseReport?.return, true)}
                        />
                        <ReportItem
                            title="当年收益率（%）"
                            value={percentPipe(currentCumBaseReport?.yearReturn, true)}
                        />
                        <ReportItem
                            title="当季收益率（%）"
                            value={percentPipe(currentCumBaseReport?.seasonReturn, true)}
                        />
                        <ReportItem
                            title="当月收益率（%）"
                            value={percentPipe(currentCumBaseReport?.monthReturn, true)}
                        />
                        <div className="divider">
                            <div
                                style={{
                                    borderRight: '1px solid rgb(99, 37, 35)',
                                }}
                            >
                                日级别
                            </div>
                            <div
                                style={{
                                    borderLeft: '1px solid rgb(99, 37, 35)',
                                }}
                            >
                                周级别
                            </div>
                        </div>
                        <div
                            style={{
                                borderRight: '1px solid rgb(99, 37, 35)',
                                boxSizing: 'border-box',
                            }}
                        >
                            <ReportItem
                                title="昨日收益率（%）"
                                value={percentPipe(yesterdayCumBaseReport?.dayReturn, true) || '-'}
                            />
                            <ReportItem
                                title="今日收益率（%）"
                                value={percentPipe(currentCumBaseReport?.dayReturn, true)}
                            />
                            <ReportItem
                                title="年化收益率（%）"
                                value={
                                    printMode && extendReport.daily.doNotShow
                                        ? '-'
                                        : percentPipe(extendReport.daily.annualizedReturn)
                                }
                            />
                            <ReportItem
                                title="年化标准差（%）"
                                value={
                                    printMode && extendReport.daily.doNotShow
                                        ? '-'
                                        : percentPipe(extendReport.daily.annualizedStd)
                                }
                            />
                            <ReportItem
                                title="Sharpe比率"
                                value={
                                    printMode && extendReport.daily.doNotShow
                                        ? '-'
                                        : extendReport.daily.sharpe?.toFixed(2)
                                }
                            />
                            <ReportItem
                                title="年化下半标准差（%）"
                                value={
                                    printMode && extendReport.daily.doNotShow
                                        ? '-'
                                        : percentPipe(extendReport.daily.annualizedHalfStd)
                                }
                            />
                            <ReportItem
                                title="Sortino比率"
                                value={
                                    printMode && extendReport.daily.doNotShow
                                        ? '-'
                                        : extendReport.daily.sortino?.toFixed(2) || '-'
                                }
                            />
                            <ReportItem title="最大回撤（%）" value={percentPipe(extendReport.daily.maxRetracement)} />
                        </div>
                        <div
                            style={{
                                borderLeft: '1px solid rgb(99, 37, 35)',
                                boxSizing: 'border-box',
                            }}
                        >
                            <ReportItem
                                title="上周收益率（%）"
                                value={percentPipe(lastWeekCumBaseReport?.weekReturn, true) || '-'}
                            />
                            <ReportItem
                                title="本周收益率（%）"
                                value={percentPipe(currentCumBaseReport?.weekReturn, true)}
                            />
                            <ReportItem
                                title="年化收益率（%）"
                                value={
                                    printMode && extendReport.daily.doNotShow
                                        ? '-'
                                        : percentPipe(extendReport.weekly.annualizedReturn)
                                }
                            />
                            <ReportItem
                                title="年化标准差（%）"
                                value={
                                    printMode && extendReport.daily.doNotShow
                                        ? '-'
                                        : percentPipe(extendReport.weekly.annualizedStd)
                                }
                            />
                            <ReportItem
                                title="Sharpe比率"
                                value={
                                    printMode && extendReport.daily.doNotShow
                                        ? '-'
                                        : extendReport.weekly.sharpe?.toFixed(2)
                                }
                            />
                            <ReportItem
                                title="年化下半标准差（%）"
                                value={
                                    printMode && extendReport.daily.doNotShow
                                        ? '-'
                                        : percentPipe(extendReport.weekly.annualizedHalfStd)
                                }
                            />
                            <ReportItem
                                title="Sortino比率"
                                value={
                                    printMode && extendReport.daily.doNotShow
                                        ? '-'
                                        : extendReport.weekly.sortino?.toFixed(2) || '-'
                                }
                            />
                            <ReportItem title="最大回撤（%）" value={percentPipe(extendReport.weekly.maxRetracement)} />
                        </div>
                    </div>
                    <div className="chart-area">
                        <div className="chart-title">产品复权净值与对应指数走势图</div>
                        <div className="chart-inner-area">
                            <Line {...chartConfig} />
                        </div>
                    </div>
                    <Footer />
                </div>
                {type.endsWith('指增') && (
                    <div className="page">
                        <div className="title subtitle">
                            <div className="title-name">产品超额分析</div>
                            <div className="title-content">
                                <ReportItem title="首次建仓日" value={startDate.format('YYYY-MM-DD')} />
                            </div>
                        </div>
                        <div className="four-row">
                            <div>
                                <div className="item important flex-center">时间范围</div>
                                <div className="item item-title">首次建仓以来</div>
                                <div className="item item-title">当年</div>
                                <div className="item item-title">当季</div>
                                <div className="item item-title">当月</div>
                                <div className="item item-title">本周</div>
                                <div className="item item-title">今日</div>
                            </div>
                            <div>
                                <div className="item important flex-center">产品收益率（%）</div>
                                {excessAnalysis.map((x, idx) => (
                                    <div className="item flex-center" key={idx}>
                                        {percentPipe(x.netValue, true)}
                                    </div>
                                ))}
                            </div>
                            <div>
                                <div className="item important flex-center">指数收益率（%）</div>
                                {excessAnalysis.map((x, idx) => (
                                    <div className="item flex-center" key={idx}>
                                        {percentPipe(x.index, true)}
                                    </div>
                                ))}
                            </div>
                            <div>
                                <div className="item important flex-center">超额收益率（%）</div>
                                {excessAnalysis.map((x, idx) => (
                                    <div className="item flex-center" key={idx}>
                                        {percentPipe(x.excess, true)}
                                    </div>
                                ))}
                            </div>
                        </div>
                        <div className="chart-area">
                            <div className="chart-title">产品超额净值走势图</div>
                            <div className="chart-inner-area">
                                <Line
                                    data={[
                                        {
                                            date: startDateStr,
                                            type: '超额净值',
                                            value: 1,
                                        },
                                        ...alignDate(compoundBaseReport.data, indexValues)
                                            .filter(x => !x[0].backfill)
                                            .map(([compound, index]) => ({
                                                date: index.date.format('YYYY/MM/DD'),
                                                type: '超额净值',
                                                value: compound.netValue / index.netValue,
                                            })),
                                    ]}
                                    {...excessChartConfig}
                                />
                            </div>
                        </div>
                        <div style={{ marginTop: 10 }}>
                            <div className="item important flex-center">近1年月度超额收益一览表</div>
                            <div
                                style={{
                                    height: 2,
                                    background: 'rgb(99, 37, 35)',
                                }}
                            ></div>
                            <div className="four-row">
                                <div className="item important flex-center">时间范围</div>
                                <div className="item important flex-center">产品收益率（%）</div>
                                <div className="item important flex-center">指数收益率（%）</div>
                                <div className="item important flex-center">超额收益率（%）</div>
                                {(() => {
                                    const excessByMonth: Record<string, ExcessAnalysis> = {};
                                    let count = 0;
                                    for (let x of [...compoundBaseReport.data].reverse()) {
                                        const text = x.date.format('YYYY年M月');
                                        if (!excessByMonth[text]) {
                                            excessByMonth[text] = {
                                                netValue: x.monthReturn,
                                                index: null,
                                                excess: null,
                                            };
                                            count += 1;
                                            if (count === 12) break;
                                        }
                                    }
                                    for (let x of [...indexBaseReport.data].reverse()) {
                                        const text = x.date.format('YYYY年M月');
                                        if (excessByMonth[text]) {
                                            if (excessByMonth[text].index == null) {
                                                excessByMonth[text].index = x.monthReturn;
                                                excessByMonth[text].excess =
                                                    excessByMonth[text].netValue - x.monthReturn;
                                            }
                                        } else {
                                            break;
                                        }
                                    }
                                    return Object.entries(excessByMonth)
                                        .reverse()
                                        .map(([text, excess]) => (
                                            <React.Fragment key={text}>
                                                <div className="item padding-item">{text}</div>
                                                <div className="item flex-center">
                                                    {percentPipe(excess.netValue, true)}
                                                </div>
                                                <div className="item flex-center">
                                                    {percentPipe(excess.index, true)}
                                                </div>
                                                <div className="item flex-center">
                                                    {percentPipe(excess.excess, true)}
                                                </div>
                                            </React.Fragment>
                                        ));
                                })()}
                            </div>
                        </div>
                        <Footer />
                    </div>
                )}
            </div>
        </div>
    );
};

export default function(props: DisplayerProps) {
    return (
        <ShadowView>
            <ReportDisplayer {...props} />
        </ShadowView>
    );
}
