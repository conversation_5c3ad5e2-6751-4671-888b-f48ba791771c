import React, { useState } from 'react';
import ReactDOM from 'react-dom';

const ShadowContent: React.FunctionComponent<{ root: HTMLElement }> = props => {
    const { root, children } = props;
    return ReactDOM.createPortal(children, root);
};

const ShadowView: React.FunctionComponent<React.HTMLAttributes<HTMLDivElement>> = props => {
    const { children, ...otherProps } = props;
    const [root, setRoot] = useState(null);
    const attachShadow = (el: HTMLDivElement) => {
        if (el && !el.shadowRoot) {
            el.attachShadow({ mode: 'open' });
            setRoot(el.shadowRoot);
        }
    };

    return (
        <div {...otherProps} ref={attachShadow}>
            {root && <ShadowContent root={root} children={children} />}
        </div>
    );
};

export default ShadowView;

export function shadowed<T>(C: React.ComponentType<T>) {
    return (props: T) => (
        <ShadowView>
            <C {...props} />
        </ShadowView>
    );
}
