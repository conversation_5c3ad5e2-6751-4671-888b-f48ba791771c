import React, { useEffect } from 'react';
import { Form, Input, Button, Checkbox } from 'antd';
import request from 'umi-request';
import useAccount from '@/hooks/useAccount';
import styles from '../account.less';
import conf from '@/conf';
import { history } from 'umi';

const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
};
const tailLayout = {
    wrapperCol: { offset: 6, span: 18 },
};

export default () => {
    const { login, loading } = useAccount.useContainer();

    useEffect(() => {
        (async () => {
            try {
                await request('/account/info', { prefix: conf.serviceUrl });
                history.replace('/');
            } catch {}
        })();
    }, []);

    const onSubmit = val => {
        login(val.username, val.password);
    };

    const title = process.env.MODE ? (
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
            <h2>
                <span>{process.env.MODE === 'ipolot' && '打新系统'}</span>
                <span>{process.env.MODE === 'operation' && '运营系统'}</span>
                <span>登录</span>
            </h2>
        </div>
    ) : null;

    return (
        <div className={styles.wrapper}>
            <div className={styles.formWrapper}>
                {title}
                <Form {...layout} name="basic" initialValues={{ remember: true }} onFinish={onSubmit}>
                    <Form.Item label="用户名" name="username" rules={[{ required: true, message: '请输入用户名!' }]}>
                        <Input />
                    </Form.Item>

                    <Form.Item label="密码" name="password" rules={[{ required: true, message: '请输入密码!' }]}>
                        <Input.Password />
                    </Form.Item>

                    <Form.Item {...tailLayout} name="remember" valuePropName="checked">
                        <Checkbox>记住我</Checkbox>
                    </Form.Item>

                    <Form.Item {...tailLayout}>
                        <Button type="primary" htmlType="submit" loading={loading.login}>
                            登录
                        </Button>
                    </Form.Item>
                </Form>
            </div>
        </div>
    );
};
