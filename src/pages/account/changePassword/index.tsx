import React from 'react';
import { Form, Input, Button } from 'antd';
import useAccount from '@/hooks/useAccount';
import styles from '../account.less';

const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
};
const tailLayout = {
    wrapperCol: { offset: 6, span: 18 },
};

export default () => {
    const { changePassword, loading } = useAccount.useContainer();

    const onSubmit = (val) => {
        changePassword(val.oldPwd, val.newPwd);
    };

    return (
        <div className={styles.wrapper}>
            <div className={styles.formWrapper}>
                <Form
                    {...layout}
                    name="basic"
                    initialValues={{ remember: true }}
                    onFinish={onSubmit}
                >
                    <Form.Item
                        label="旧密码"
                        name="oldPwd"
                        rules={[{ required: true, message: '请输入密码!' }]}
                    >
                        <Input.Password />
                    </Form.Item>

                    <Form.Item
                        label="新密码"
                        name="newPwd"
                        rules={[{ required: true, message: '请输入密码!' }]}
                    >
                        <Input.Password />
                    </Form.Item>

                    <Form.Item
                        label="确认新密码"
                        name="newPwdDup"
                        dependencies={['newPwd']}
                        hasFeedback
                        rules={[
                            { required: true, message: '请确认新密码!' },
                            ({ getFieldValue }) => ({
                                validator(rule, value) {
                                    if (!value || getFieldValue('newPwd') === value)
                                        return Promise.resolve();
                                    else
                                        return Promise.reject('两次密码输入不一致');
                                }
                            })
                        ]}
                    >
                        <Input.Password />
                    </Form.Item>

                    <Form.Item {...tailLayout}>
                        <Button type="primary" htmlType="submit" loading={!!loading.changePassword}>提交</Button>
                    </Form.Item>
                </Form>
            </div>
        </div>
    )
}
