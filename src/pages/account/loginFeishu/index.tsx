import conf from '@/conf';
import React, { useEffect } from 'react';
import { history } from 'umi';
import request from 'umi-request';
import styles from '../account.less';
import './index.less';

const getFeishuUrl = () => {
    const baseUrl = 'https://passport.feishu.cn/suite/passport/oauth/authorize';
    const clientId = 'cli_a4845e4de83bd00e';
    const redirectUri = encodeURIComponent(conf.feishuRedirectUri);
    return `${baseUrl}?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code`;
};

export default () => {
    useEffect(() => {
        (async () => {
            try {
                await request('/account/info', { prefix: conf.serviceUrl });
                history.replace('/');
            } catch {}
        })();
    }, []);

    useEffect(() => {
        const goto = getFeishuUrl();
        const qrlogin = window.QRLogin({ id: 'qr-container', goto, width: '300', height: '300' });

        const handleMessage = (event: MessageEvent<string>) => {
            const { data, origin } = event;
            if (!qrlogin.matchOrigin(origin)) return;

            location.href = `${goto}&tmp_code=${data}`;
        };

        addEventListener('message', handleMessage);

        return () => removeEventListener('message', handleMessage);
    }, []);

    useEffect(() => {
        if (!navigator.userAgent.includes('Lark')) return;

        const searchParams = new URLSearchParams(location.search);
        const stale = searchParams.get('stale');
        if (stale === '1') return;

        location.href = getFeishuUrl();
    }, []);

    return (
        <div className={styles.wrapper}>
            <div className={styles.formWrapper} style={{ width: 'unset', paddingBottom: 32 }}>
                <div style={{ display: 'flex', justifyContent: 'center' }}>
                    <strong style={{ fontSize: '20px' }}>飞书扫描二维码登录</strong>
                </div>
                <div id="qr-container" />
                <div style={{ display: 'flex', justifyContent: 'center' }}>
                    <span>
                        或使用用户名密码<a onClick={() => history.push('/account/login')}>登录</a>
                    </span>
                </div>
            </div>
        </div>
    );
};
