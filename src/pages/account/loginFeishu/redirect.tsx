import conf from '@/conf';
import useAccount from '@/hooks/useAccount';
import React, { useEffect } from 'react';
import { history } from 'umi';
import request from 'umi-request';

export default () => {
    const { loginFeishu } = useAccount.useContainer();

    useEffect(() => {
        (async () => {
            try {
                await request('/account/info', { prefix: conf.serviceUrl });
                history.replace('/');
            } catch {
                const searchParams = new URLSearchParams(location.search);
                const code = searchParams.get('code');
                if (!code) return;

                await loginFeishu(code);
            }
        })();
    }, []);

    return (
        <div
            style={{
                position: 'absolute',
                top: 0,
                right: 0,
                bottom: 0,
                left: 0,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
            }}
        >
            飞书登录中...
        </div>
    );
};
