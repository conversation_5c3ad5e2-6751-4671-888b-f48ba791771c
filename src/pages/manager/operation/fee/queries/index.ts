import { PaginationRequest } from '@/models/common/PaginationRequest';
import useSWR from 'swr';
import { PortfolioFeeFilter } from '../models';
import service from '../service';

export const getFeeList = (params: { pagination: PaginationRequest; filter?: PortfolioFeeFilter }) => {
    return useSWR(['fee', params], async ([_, params]) => {
        return await service.getFeeList(params);
    });
};

export const getPositionList = (params: { pagination: PaginationRequest; filter?: PortfolioFeeFilter }) => {
    return useSWR(['position', params], async ([_, params]) => {
        return await service.getPositionList(params);
    });
};
