import { CustomResponse } from '@/models/common/CustomResponse';
import { PaginationRequest } from '@/models/common/PaginationRequest';
import service from '@/utils/service';
import { PortfolioFee, PortfolioFeeFilter } from '../models';

const prefix = '/operation/portfolio/fee';

class PortfolioFeeService {
    getFeeList({ pagination, filter }: { pagination: PaginationRequest; filter?: PortfolioFeeFilter }) {
        return service.get<CustomResponse<PortfolioFee[]>>(`${prefix}/fee`, {
            params: { ...pagination, ...(filter || {}) },
        });
    }

    async getFeeExcel(filter: PortfolioFeeFilter) {
        const response = await fetch(`/api/${prefix}/fee_excel`, {
            method: 'post',
            body: JSON.stringify(filter),
            headers: { 'Content-Type': 'application/json' },
        });

        return await response.blob();
    }

    getPositionList({ pagination, filter }: { pagination: PaginationRequest; filter?: PortfolioFeeFilter }) {
        return service.get<CustomResponse<PortfolioFee[]>>(`${prefix}/position`, {
            params: { ...pagination, ...(filter || {}) },
        });
    }

    async getPositionExcel(filter: PortfolioFeeFilter) {
        const response = await fetch(`/api/${prefix}/position_excel`, {
            method: 'post',
            body: JSON.stringify(filter),
            headers: { 'Content-Type': 'application/json' },
        });

        return await response.blob();
    }
}

export default new PortfolioFeeService();
