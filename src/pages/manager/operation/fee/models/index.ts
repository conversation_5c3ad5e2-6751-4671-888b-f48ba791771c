import { QUnitType } from 'dayjs';

export type Period = 'DAY' | 'MONTH' | 'QUARTER' | 'YEAR';

export const periodTransform: Record<Period, string> = {
    DAY: '日',
    MONTH: '月',
    QUARTER: '季',
    YEAR: '年',
};

export const periodDayjsUnitTransform: Record<Period, QUnitType> = {
    DAY: 'day',
    MONTH: 'month',
    QUARTER: 'quarter',
    YEAR: 'year',
};

export type PortfolioFee = {
    custodian?: string;
    portfolioId?: number;
    portfolioName?: string;
    portfolioNo?: string;
    date?: string;
    managementFee?: number;
    custodianFee?: number;
    outsourceFee?: number;
    salesServiceFee?: number;
    carriedInterest?: number;
    vat?: number;
};

export type PortfolioPosition = {
    custodian?: string;
    portfolioId?: number;
    portfolioName?: string;
    portfolioNo?: string;
    date?: string;
    ipo?: boolean;
    depositAmount?: number;
    liability?: number;
    assetNet?: number;
    portfolioManagerId?: number;
    portfolioManagerName?: string;
};

export type PortfolioFeeFilter = {
    portfolioId?: number;
    period: Period;
    startDate: string;
    endDate: string;
};
