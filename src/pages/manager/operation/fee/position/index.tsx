import { usePortfolioOutlinedEnum } from '@/queries/portfolio';
import { downloadBlob } from '@/utils';
import { usePagination } from '@/utils/antdTable';
import { Prefetch } from '@/utils/swr';
import { CheckCircleTwoTone, CloseCircleFilled } from '@ant-design/icons';
import { ProColumnType, ProTable } from '@ant-design/pro-components';
import { FormInstance } from 'antd/es/form';
import dayjs from 'dayjs';
import { MD5 } from 'object-hash';
import React, { useRef, useState } from 'react';
import ExportButton from '../../portfolio/clientManage/ExportButton';
import { Period, PortfolioFeeFilter, PortfolioPosition, periodDayjsUnitTransform, periodTransform } from '../models';
import { getPositionList } from '../queries';
import service from '../service';

const defaultPeriod: Period = 'QUARTER';
const defaultStartDate = dayjs().subtract(3, 'month');
const defaultEndDate = dayjs();

export default () => {
    const formRef = useRef<FormInstance>();
    const { pagination, paginationConfig, setPage } = usePagination({ showSizeChanger: true });
    const [filter, setFilter] = useState<PortfolioFeeFilter>({
        period: defaultPeriod,
        startDate: defaultStartDate.format('YYYY-MM-DD'),
        endDate: defaultEndDate.format('YYYY-MM-DD'),
    });

    const query = getPositionList({ pagination, filter });
    const { isLoading } = query;
    const { data, amount } = query.data || {};

    const [nameEnum, noEnum] = usePortfolioOutlinedEnum();

    const tableColumns: ProColumnType<PortfolioPosition>[] = [
        { dataIndex: 'custodian', title: '托管' },
        { dataIndex: 'portfolioName', title: '产品名称' },
        { dataIndex: 'portfolioNo', title: '产品代码' },
        {
            dataIndex: 'ipo',
            title: '是否打新',
            render: (_, { ipo }) =>
                ipo ? (
                    <CheckCircleTwoTone twoToneColor="#52c41a" />
                ) : (
                    <CloseCircleFilled style={{ color: 'lightgrey' }} />
                ),
        },
        { dataIndex: 'date', title: '业务日期' },
        { dataIndex: 'depositAmount', title: '银行账户余额', valueType: 'digit' },
        { dataIndex: 'liability', title: '负债', valueType: 'digit' },
        { dataIndex: 'assetNet', title: '资产净值', valueType: 'digit' },
        { dataIndex: 'portfolioManagerName', title: '运营负责人' },
    ];

    for (const column of tableColumns) {
        column.hideInSearch = true;
    }

    const onPortfolioFieldChange = (id: number) => {
        formRef.current.setFields([
            { name: 'portfolioNameId', value: id },
            { name: 'portfolioNoId', value: id },
        ]);
    };

    const searchColumns: ProColumnType<PortfolioPosition>[] = [
        {
            key: 'portfolioNameId',
            title: '产品名称',
            valueType: 'select',
            valueEnum: nameEnum,
            fieldProps: { showSearch: true, onChange: onPortfolioFieldChange },
            colSize: 2,
        },
        {
            key: 'portfolioNoId',
            title: '产品代码',
            valueType: 'select',
            valueEnum: noEnum,
            fieldProps: { showSearch: true, onChange: onPortfolioFieldChange },
            colSize: 2,
        },
        {
            key: 'period',
            title: '查询周期',
            valueType: 'radio',
            valueEnum: periodTransform,
            fieldProps: { allowClear: false },
        },
        {
            key: 'startDate',
            title: '开始日期',
            valueType: 'date',
            fieldProps: { allowClear: false },
        },
        {
            key: 'endDate',
            title: '结束日期',
            valueType: 'date',
            fieldProps: { allowClear: false },
        },
    ];

    for (const column of searchColumns) {
        column.hideInTable = true;
    }

    const columns = tableColumns.concat(searchColumns);

    const extractFilter = () => {
        const { portfolioNameId, portfolioNoId, period, startDate, endDate } = formRef.current.getFieldsValue();

        return {
            portfolioId: portfolioNameId || portfolioNoId,
            startDate: startDate?.format('YYYY-MM-DD'),
            endDate: endDate?.format('YYYY-MM-DD'),
            period,
        };
    };

    const exportExcel = async () => {
        const filter = extractFilter();
        if (!filter.portfolioId) {
            const unit = periodDayjsUnitTransform[filter.period];
            const startDate = dayjs(filter.startDate);
            const endDate = dayjs(filter.endDate);
            const diff = endDate.diff(startDate, unit);
            if (diff > 50) {
                throw new Error('请求导出数据过多，未筛选产品时请确保(“结束日期” - “开始日期”) ÷ “查询周期” ≤ 50');
            }
        }
        downloadBlob(await service.getPositionExcel(filter), '产品托管户头寸管理.xlsx');
    };

    return (
        <React.Fragment>
            <ProTable
                headerTitle="产品托管户头寸管理"
                formRef={formRef}
                dataSource={data}
                columns={columns}
                loading={isLoading}
                scroll={{ x: 'max-content' }}
                search={{ defaultCollapsed: false }}
                pagination={{ total: amount, ...paginationConfig }}
                rowKey={({ portfolioId, date }) => MD5({ portfolioId, date })}
                toolBarRender={() => [<ExportButton key="export" onExport={exportExcel} />]}
                form={{
                    initialValues: { period: defaultPeriod, startDate: defaultStartDate, endDate: defaultEndDate },
                    submitter: {
                        onSubmit: () => {
                            setFilter(extractFilter());
                            setPage(0);
                        },
                    },
                }}
            />
            <Prefetch
                query={() => getPositionList({ pagination: { ...pagination, page: pagination.page + 1 }, filter })}
            />
        </React.Fragment>
    );
};
