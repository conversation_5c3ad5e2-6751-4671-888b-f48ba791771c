import { SearchItemProps, searchContext } from '@/utils/fieldProperty';
import { Form, Select } from 'antd';
import React, { useContext } from 'react';
import { FileArchive } from '../models/FileArchive';
import useFileArchiveTypes from './useFileArchiveTypes';

export default (props: SearchItemProps) => {
    const { label, name, ...otherProps } = props;
    const { getForm, triggerSearch, setSearchParams } = useContext(searchContext);

    const fileArchiveTypes = useFileArchiveTypes();

    const onClear = () => {
        const form = getForm();
        if (!form) return;

        form.setFieldValue(name, undefined);
        setSearchParams(form.getFieldsValue());

        triggerSearch();
    };

    return (
        <Form.Item name={name} {...otherProps}>
            <Select
                allowClear
                placeholder="文件类型"
                options={fileArchiveTypes?.map(x => ({ label: x.name, value: x.id }))}
                onSelect={triggerSearch}
                onClear={onClear}
            />
        </Form.Item>
    );
};

export const onFileArchiveTypeSearch = (record: FileArchive, search: number) => {
    return record.type === search;
};
