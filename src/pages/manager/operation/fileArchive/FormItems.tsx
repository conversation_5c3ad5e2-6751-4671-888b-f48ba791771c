import { FormItemSetter, useFormData, useParentForm } from '@/components/MyForm';
import { Form } from 'antd';
import React from 'react';
import { FileArchive, fileArchiveFields } from '../models/FileArchive';

export default function FormItems(props: { inProcess?: boolean }) {
    const { inProcess } = props;
    const { formItems } = fileArchiveFields;
    const { data } = useFormData<FileArchive>();
    const form = useParentForm();
    return (
        <>
            {formItems.type()}
            {inProcess ? (
                data?.type == 1 ? (
                    formItems.portfolioName()
                ) : null
            ) : (
                <Form.Item noStyle shouldUpdate={(prev, cur) => prev.type !== cur.type}>
                    {() => (form.getFieldValue('type') == '1' ? formItems.portfolioId() : null)}
                </Form.Item>
            )}
            {formItems.filename()}
            <FormItemSetter readonly={false}>{formItems.file()}</FormItemSetter>
            {!inProcess && formItems.assigneeId()}
            {formItems.remark()}
        </>
    );
}
