import HistoryList from '@/components/ControlledForm/HistoryList';
import { CrudTable, useCrudTableAction } from '@/components/CrudTable';
import { DrawerForm, FormTrigger } from '@/components/TriggerableForm';
import wfService from '@/services/worflow.service';
import { mapAntdTable } from '@/utils/fieldProperty';
import { useGlobalStateSetter } from '@/utils/globalStateTree';
import { HistoryOutlined, NodeIndexOutlined } from '@ant-design/icons';
import { Button, Drawer, Image, Modal, Tag } from 'antd';
import React, { useEffect, useState } from 'react';
import { FileArchive, fileArchiveFields, fileArchiveStateEnum } from '../models/FileArchive';
import fileArchiveService from '../services/fileArchive.service';
import portfolioService from '../services/portfolio.service';
import FormItems from './FormItems';
import useFileArchiveTypes from './useFileArchiveTypes';

const stateColorTransform = {
    PROCESSING: 'green',
    APPROVED: 'blue',
    REJECTED: 'red',
};

export default function() {
    const actionRef = useCrudTableAction();
    const [visitDiagramId, setVisitDiagramId] = useState<string>(null);
    const [visitHistoryId, setVisitHistoryId] = useState<string>(null);

    const fileArchiveTypes = useFileArchiveTypes();

    const columns = mapAntdTable<FileArchive>(fileArchiveFields, [
        {
            key: 'state',
            title: '状态',
            render: (dom, record) => (
                <Tag color={stateColorTransform[record.state]}>{fileArchiveStateEnum[record.state]}</Tag>
            ),
        },
        {
            dataIndex: 'type',
            title: '文件类型',
            render: (_, record) => fileArchiveTypes?.find(({ id }) => id === record.type)?.name,
        },
        { dataIndex: 'initiatorName' },
        { dataIndex: 'portfolioName' },
        { dataIndex: 'filename' },
        { dataIndex: 'file' },
        { dataIndex: 'storagePlace' },
        {
            title: '详情',
            key: 'content',
            valueType: 'option',
            doNotExport: true,
            render: (_, record) => [
                record.open ? (
                    <a key="diagram" onClick={() => setVisitDiagramId(record.instanceId)}>
                        <NodeIndexOutlined />
                    </a>
                ) : null,
                record.open || record.processComplete ? (
                    <a key="history" onClick={() => setVisitHistoryId(record.instanceId)}>
                        <HistoryOutlined />
                    </a>
                ) : null,
            ],
        },
    ]);

    const [setGlobalState] = useGlobalStateSetter();
    useEffect(() => {
        portfolioService.getOutlinedList().then(response => {
            const dict = {};
            for (let x of response.data) {
                dict[x.id] = x;
            }
            setGlobalState('portfolioOutlinedList', { dict, list: response.data });
        });
    }, []);

    return (
        <>
            <DrawerForm
                name="fileArchiveStartForm"
                createTitle="申请文件归档"
                editTitle=""
                formContents={<FormItems />}
                width={500}
                labelWidth={100}
                onCreate={fileArchiveService.startProcess}
                afterCreate={data => actionRef.create(data)}
            >
                <CrudTable
                    actionRef={actionRef}
                    title="文件归档"
                    fields={fileArchiveFields}
                    service={fileArchiveService}
                    recordInsertAt="last"
                    search={false}
                    rowKey="id"
                    columns={columns}
                    scroll={{ x: 'max-content' }}
                    toolBarRender={() => [
                        <FormTrigger key="create">
                            <Button type="primary">申请</Button>
                        </FormTrigger>,
                    ]}
                    simpleSearches={
                        <>
                            {fileArchiveFields.searchItems.state({})}
                            {fileArchiveFields.searchItems.type({})}
                            {fileArchiveFields.searchItems.initiatorName({})}
                            {fileArchiveFields.searchItems.portfolioName({})}
                            {fileArchiveFields.searchItems.filename({})}
                        </>
                    }
                    newVersion
                />
            </DrawerForm>
            <Drawer
                visible={visitHistoryId != null}
                onClose={() => setVisitHistoryId(null)}
                title="流程记录"
                width={360}
            >
                {visitHistoryId && <HistoryList processId={visitHistoryId} />}
            </Drawer>
            <Modal
                width={800}
                visible={visitDiagramId != null}
                onCancel={() => setVisitDiagramId(null)}
                onOk={() => setVisitDiagramId(null)}
                title="流程图"
            >
                {visitDiagramId && <Image src={wfService.getDiagramUrl(visitDiagramId)} preview={false} />}
            </Modal>
        </>
    );
}
