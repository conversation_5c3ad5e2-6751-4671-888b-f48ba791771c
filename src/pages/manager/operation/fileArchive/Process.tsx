import { rejectSubmitButton, WfSubmitButton, WfWorkingFormOperation } from '@/components/ControlledForm';
import { getWorkflowForm, InnerWorkflowProps } from '@/components/MessageCenter';
import { FormItemSetter, MyForm } from '@/components/MyForm';
import React from 'react';
import { fileArchiveFields } from '../models/FileArchive';
import fileArchiveService from '../services/fileArchive.service';
import FormItems from './FormItems';

function WorkflowForm(props: InnerWorkflowProps) {
    const { taskId, title, afterSubmit, hideTitle, onCancel } = props;
    const { formItems } = fileArchiveFields;

    return (
        <MyForm name="fileArchiveProcessingForm">
            {!hideTitle && <h2>{title}</h2>}
            <FormItemSetter readonly>
                <FormItems inProcess />
            </FormItemSetter>
            {formItems.storagePlace()}
            <WfWorkingFormOperation taskId={taskId} afterSubmit={afterSubmit} onCancel={onCancel}>
                {rejectSubmitButton()}
                <WfSubmitButton
                    type="primary"
                    complete={formData => fileArchiveService.archive(taskId, formData.file, formData.storagePlace)}
                >
                    归档
                </WfSubmitButton>
            </WfWorkingFormOperation>
        </MyForm>
    );
}

export default getWorkflowForm(async taskId => {
    const req = await fileArchiveService.findByTaskId(taskId);
    return req.data;
}, WorkflowForm);
