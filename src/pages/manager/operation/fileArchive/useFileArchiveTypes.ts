import { CustomResponse } from '@/models/common/CustomResponse';
import service from '@/utils/service';
import useSWR from 'swr';
import { FileArchiveType } from '../models/FileArchive';

export const fileArchiveTypeQueryKey = 'fileArchiveTypes';

export default () => {
    const request = useSWR(fileArchiveTypeQueryKey, async () => {
        return await service.get<CustomResponse<FileArchiveType[]>>('/operation/file_archive/type');
    });

    return request.data?.data || [];
};
