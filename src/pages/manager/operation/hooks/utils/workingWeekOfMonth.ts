//e:`2023-1`
import dayjs, { Dayjs } from 'dayjs';
interface ListItem {
    day: Dayjs;
    isMonth: boolean;
    time: string;
    isWorkingDay: boolean | string;
}
export default function workingWeek(
    year: number,
    month: number,
    isWorkingDay?: (date: Dayjs) => boolean,
): ListItem[][] | ListItem[] {
    const startDay = dayjs()
        .year(year)
        .month(month)
        .startOf('month');
    const endDay = dayjs()
        .year(year)
        .month(month)
        .endOf('month');
    let list: ListItem[] = [];

    let currentDay = startDay;
    while (currentDay.isBefore(endDay)) {
        list.push({
            day: currentDay,
            isMonth: true,
            time: '',
            isWorkingDay: isWorkingDay ? isWorkingDay(currentDay) : '未传入',
        });
        currentDay = currentDay.add(1, 'day');
    }

    let week = list[0].day.day();
    if (week == 0) {
        week = 7;
    }
    for (let i = 1; i < week; i++) {
        list.unshift({
            day: startDay.add(-i, 'day'),
            isMonth: false,
            time: '',
            isWorkingDay: isWorkingDay ? isWorkingDay(startDay.add(-i, 'day')) : '未传入',
        });
    }

    let nextWeek = 42 - (endDay.date() + week - 1);
    for (let i = 1; i <= nextWeek; i++) {
        list.push({
            day: endDay.add(i, 'day'),
            isMonth: false,
            time: '',
            isWorkingDay: isWorkingDay ? isWorkingDay(endDay.add(i, 'day')) : '未传入',
        });
    }

    list.forEach(item => {
        let str = item.day.format('YYYY-MM-DD');
        item.time = str;
    });
    let arr = [[], [], [], [], [], []];
    let num = 0;
    list.forEach((item, index) => {
        if ((index + 1) % 7 == 0) {
            arr[num].push(item);
            num++;
        } else {
            arr[num].push(item);
        }
    });

    arr.forEach((item, index) => {
        if (!item?.[0]) {
            arr.splice(index, 1);
        }
    });
    // if (isWorkingDay) {
    //     const newArr = []
    //     for(let w=0;w<=arr.length;w++){
    //               let changenum = 0
    //               for(let d = arr[w].length;d>=0;d--){
    //                 changenum++
    //                 if(d.isWorkingDay){
    //                     newArr.push(d)
    //                     changenum = 0
    //                 }
    //                 if(changenum>=7){
    //                     let nextWeekData = arr[w+1]?.find((wdata)=>wdata.isWorkingDay)
    //                     newArr.push(nextWeekData)
    //                 }
    //               }
    //     }
    //     console.log(newArr,"?????");

    //     return newArr
    // }

    if (isWorkingDay) {
        const newArr = [];
        for (let w = 0; w < arr.length; w++) {
            let changenum = 0;
            for (let d = arr[w].length - 1; d >= 0; d--) {
                changenum++;
                if (arr[w][d].isWorkingDay) {
                    newArr.push(arr[w][d]);
                    changenum = 0;
                    break;
                }
            }
            if (changenum >= 7 && arr[w + 1]) {
                let nextWeekData = arr[w + 1].find(wdata => wdata.isWorkingDay);
                if (nextWeekData) {
                    newArr.push(nextWeekData);
                }
            }
        }
        return newArr;
    }

    return arr;
}
