import { CrudTable, DeleteWrapper, useCrudTableAction } from '@/components/CrudTable';
import { DrawerForm, FormTrigger } from '@/components/TriggerableForm';
import { mapAntdTable } from '@/utils/fieldProperty';
import { Button } from 'antd';
import React from 'react';
import { Delivery, deliveryFields } from '../models/Delivery';
import service from '../services/delivery.service';
import FormItems from './formItems';
import useOptionHistory from './useOptionHistory';

const Component = () => {
    const { saveHistory } = useOptionHistory.useContainer();

    const actionRef = useCrudTableAction<Delivery>();
    const columns = mapAntdTable(deliveryFields, [
        { dataIndex: 'date' },
        { dataIndex: 'no' },
        { dataIndex: 'createUserName' },
        { dataIndex: 'receiver' },
        { dataIndex: 'contact' },
        { dataIndex: 'address' },
        { dataIndex: 'fileInfo' },
        {
            title: '操作',
            key: 'option',
            valueType: 'option',
            width: 100,
            fixed: 'right',
            render: (_, record) => [
                <FormTrigger formName="deliveryForm" key="edit" id={record.id} data={record}>
                    <a>编辑</a>
                </FormTrigger>,
                <DeleteWrapper key="delete" id={record.id} confirm={`是否确认删除？`}>
                    <a style={{ color: 'red' }} href="#">
                        删除
                    </a>
                </DeleteWrapper>,
            ],
        },
    ]);

    const onCreate = async (data: Delivery) => {
        const response = await service.create(data);

        const { receiver, contact, address } = data;
        saveHistory({ receiver, contact, address });

        return response;
    };

    return (
        <DrawerForm<Delivery>
            name="deliveryForm"
            createTitle="创建"
            editTitle="编辑"
            onCreate={onCreate}
            onUpdate={service.update}
            afterCreate={actionRef.create}
            afterUpdate={data => actionRef.update(data.id, data)}
            formContents={<FormItems />}
        >
            <CrudTable<Delivery>
                title="快递单号"
                service={service}
                fields={deliveryFields}
                recordInsertAt="first"
                actionRef={actionRef}
                columns={columns}
                globalDataHookName="delivery"
                search={false}
                simpleSearches={
                    <>
                        {deliveryFields.searchItems.no({})}
                        {deliveryFields.searchItems.createUserName({})}
                        {deliveryFields.searchItems.receiver({})}
                        {deliveryFields.searchItems.fileInfo({})}
                    </>
                }
                toolBarRender={() => [
                    <FormTrigger key="create">
                        <Button type="primary">创建</Button>
                    </FormTrigger>,
                ]}
                rowKey="id"
                scroll={{ x: 'max-content' }}
                newVersion
            />
        </DrawerForm>
    );
};

export default () => (
    <useOptionHistory.Provider>
        <Component />
    </useOptionHistory.Provider>
);
