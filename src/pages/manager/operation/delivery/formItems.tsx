import { MyFormItem, useParentForm } from '@/components/MyForm';
import { useGlobalState } from '@/utils/globalStateTree';
import { HistoryOutlined } from '@ant-design/icons';
import { AutoComplete, Col, Row } from 'antd';
import { findLast, uniq, without } from 'lodash';
import React, { useState } from 'react';
import { Delivery, deliveryFields } from '../models/Delivery';
import useOptionHistory from './useOptionHistory';

export default () => {
    const { formItems } = deliveryFields;

    const { list } = useGlobalState<{ list: Delivery[] }>('delivery');
    const form = useParentForm();

    const fillOtherFields = (key: string, value: string) => {
        const item = findLast(list, it => it[key] === value);
        if (!item) return;

        const { address, receiver, contact } = item;

        const addressField = { name: 'address', value: address };
        const contactField = { name: 'contact', value: contact };
        const receiverFIeld = { name: 'receiver', value: receiver };

        if (key === 'receiver') form.setFields([addressField, contactField]);
        else if (key === 'address') form.setFields([contactField, receiverFIeld]);
    };

    return (
        <>
            <Row gutter={[10, 0]}>
                {formItems.date({ span: 8 })}
                {formItems.no({ span: 16 })}
            </Row>
            <AutoCompleteItem label="收件地址" name="address" onChange={value => fillOtherFields('address', value)} />
            <Row gutter={[10, 0]}>
                <Col span={12}>
                    <AutoCompleteItem
                        label="接收人"
                        name="receiver"
                        onChange={value => fillOtherFields('receiver', value)}
                    />
                </Col>
                <Col span={12}>
                    <AutoCompleteItem
                        label="联系方式"
                        name="contact"
                        onChange={value => fillOtherFields('contact', value)}
                    />
                </Col>
            </Row>
            {formItems.fileInfo()}
        </>
    );
};

interface AutoCompleteItemProps {
    label: string;
    name: string;
    onChange?: (value: string) => void;
}
const AutoCompleteItem = ({ label, name, onChange }: AutoCompleteItemProps) => {
    const [searchValue, setSearchValue] = useState<string>();

    const { history } = useOptionHistory.useContainer();
    const historyOptions = history?.[name] || [];

    const { list } = useGlobalState<{ list: Delivery[] }>('delivery');

    const otherOptions = without(
        uniq(list?.map(it => it[name]) || [])
            .filter(x => x)
            .sort((a: string, b: string) => a.localeCompare(b)),
        ...historyOptions,
    );

    const options = [
        ...historyOptions.map(it => ({
            value: it,
            label: (
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    {it}
                    <HistoryOutlined />
                </div>
            ),
        })),
        ...otherOptions.map(it => ({ value: it, label: it })),
    ];

    const filtered = options.filter(it => (!searchValue ? true : it.value.includes(searchValue)));

    return (
        <MyFormItem label={label} name={name}>
            <AutoComplete
                style={{ width: '100%' }}
                onChange={value => onChange?.(value)}
                options={filtered}
                onSearch={value => setSearchValue(value)}
            />
        </MyFormItem>
    );
};
