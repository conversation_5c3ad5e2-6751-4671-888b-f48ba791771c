import { entries, without } from 'lodash';
import { useState } from 'react';
import { createContainer } from 'unstated-next';

const getStoredOption = () => {
    try {
        const item = localStorage.getItem('operation.delivery.option');
        return JSON.parse(item);
    } catch {
        return {};
    }
};

const updateStoredOption = (value: any) => {
    localStorage.setItem('operation.delivery.option', JSON.stringify(value));
};

export default createContainer(() => {
    const [history, setHistory] = useState<Record<string, string[]>>(getStoredOption());

    const saveHistory = (items: Record<string, string>) => {
        const newHistory = { ...history };

        entries(items).forEach(([key, value]) => {
            if (value == null) return;

            const list = history?.[key] || [];
            const newList = [value].concat(without(list, value));
            newHistory[key] = newList.slice(0, 5);
        });

        setHistory(newHistory);
        updateStoredOption(newHistory);
    };

    return { history, saveHistory };
});
