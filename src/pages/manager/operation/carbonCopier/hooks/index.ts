import { useState, useEffect } from 'react';
import { createContainer } from 'unstated-next';
import { User } from '@/models/account/User';
import userService from '@/services/account.service';
import CarbonCopier, { Type } from '../models';
import service from '../service';
import { message } from 'antd';

export default createContainer(() => {
    const [users, setUsers] = useState<User[]>();
    const [carbonCopiers, setCarbonCopiers] = useState<CarbonCopier[]>();

    const [refresher, setRefresher] = useState(false);

    useEffect(() => {
        const fetchCarbonCopiers = async () => {
            try {
                const cResponse = await service.getList();
                setCarbonCopiers(cResponse.data);
            } catch (e) {
                if (e) message.error('获取抄送人列表失败');
            }
        };

        fetchCarbonCopiers();
    }, [refresher]);

    useEffect(() => {
        if (!carbonCopiers) return;

        const fetchUsers = async () => {
            try {
                const uResponse = await userService.getList(['accountDetail']);
                const filtered: User[] = [];
                uResponse.data.forEach(it => {
                    if (
                        it.realName != null &&
                        !filtered.map(({ id }) => id).includes(it.id) &&
                        !carbonCopiers.map(c => c.user.id).includes(it.id)
                    ) {
                        filtered.push(it);
                    }
                });
                setUsers(filtered);
            } catch {
                message.error('获取用户列表失败');
            }
        };

        fetchUsers();
    }, [carbonCopiers]);

    const refresh = () => {
        setRefresher(!refresher);
    };

    const add = async (userId: number, type: Type) => {
        try {
            const user = users.find(it => it.id === userId);
            if (user) {
                await service.create(user, type);
                refresh();
            } else {
                throw new Error();
            }
        } catch {
            message.error('添加抄送人失败');
        }
    };

    const remove = async (id: number) => {
        try {
            await service.delete(id);
            refresh();
        } catch {
            message.error('删除抄送人失败');
        }
    };

    return { users, carbonCopiers, refresh, add, remove };
});
