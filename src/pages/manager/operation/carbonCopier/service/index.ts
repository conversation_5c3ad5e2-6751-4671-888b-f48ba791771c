import service from '@/utils/service';
import { CustomResponse } from '@/models/common/CustomResponse';
import CarbonCopier from '../models';
import { User } from '@/models/account/User';
import { message } from 'antd';

const prefix = '/operation/carbon_copier';

class CarbonCopierService {
    getList() {
        return service.get<CustomResponse<CarbonCopier[]>>(prefix, {
            errorHandler: error => {
                if (error.response) {
                    if (error.response.status == 403) {
                        message.error('不允许访问');
                    }
                    throw error.data;
                } else
                    throw {
                        code: null,
                        message: '服务器未返回',
                    };
            },
        });
    }

    create(user: User, type: String) {
        return service.post<CustomResponse<CarbonCopier>>(prefix, {
            data: { userId: user.id, type },
        });
    }

    delete(id: number) {
        return service.delete<CustomResponse<null>>(prefix + `/${id}`);
    }
}

export default new CarbonCopierService();
