import React, { useState } from 'react';
import { Card, Tag, Select, Spin } from 'antd';
import styles from './index.less';
import useCarbonCopier from '../../hooks';
import { Type } from '../../models';

export default ({ type }: { type: Type }) => {
    const { users, carbonCopiers, add, remove } = useCarbonCopier.useContainer();
    const [value, setValue] = useState<number>();

    const [loading, setLoading] = useState(false);

    const options = users?.map(it => ({
        value: it.id,
        label: it.realName,
    }));

    const onSelect = async (userId: number) => {
        setLoading(true);
        await add(userId, type);

        setValue(null);
        setLoading(false);
    };

    const onClose = async (e: React.MouseEvent, id: number) => {
        e.preventDefault();
        setLoading(true);
        await remove(id);
        setLoading(false);
    };

    return (
        <Card className={styles.section} loading={carbonCopiers == null}>
            <h3>{type === 'operation' ? '运营' : '市场'}抄送人员</h3>
            <Spin spinning={loading}>
                <div className={styles.list}>
                    {carbonCopiers
                        ?.filter(it => it.type === type)
                        ?.map((it, index) => (
                            <Tag key={index} closable onClose={e => onClose(e, it.id)}>
                                {it.user.realName}
                            </Tag>
                        ))}
                    <Select style={{ width: 130 }} options={options} value={value} onSelect={onSelect} />
                </div>
            </Spin>
        </Card>
    );
};
