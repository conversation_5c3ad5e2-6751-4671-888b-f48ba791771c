import { EntityBase } from '@/models/common/EntityBase';
import { SegmentedData } from '@/models/common/SegmentedData';
import { WfEntityBase, wfEntityBaseField } from '@/models/common/WfEntityBase';
import { WorkflowTask } from '@/models/common/WorkflowTask';
import { DataDict } from '@/models/utils/DataDict';
import { FileUpload } from '@/models/utils/FileUpload';
import ClientTagField from '@/utils/ClientTagField';
import UserField from '@/utils/UserField';
import { FieldTitle, SearchItem, Validate, getFields } from '@/utils/fieldProperty';
import * as Field from '@/utils/fieldUtils';
import { Convertor, Transform } from '@/utils/io';
import { field } from '@/utils/newFieldProperty';
import { Dayjs as Moment } from 'dayjs';
import { SearchContractContent, onContractContentSearch } from '../components/SearchContractContent';
import { OpenDay } from './OpenDay';

export const portfolioTypeNew = {
    沪深300指增: '沪深300指增',
    中证500指增: '中证500指增',
    中证1000指增: '中证1000指增',
    中证全指: '中证全指',
    小市值指增: '小市值指增',
    中证A500指增: '中证A500指增',
    完全对冲: '完全对冲',
    灵活对冲: '灵活对冲',
    安心岛策略: '安心岛策略',
    收益互换: '收益互换',
    定制: '定制',
    混合: '混合',
    量化选股: '量化选股',
    长线策略: '长线策略',
    量化多头策略: '量化多头策略',
    微盘股指数: '微盘股指数',
    未知: '未知',
};

export const portfolioType = {
    ...portfolioTypeNew,
    中性: '中性',
    指增: '指增',
};

export const portfolioStateType = {
    待上线: '待上线',
    募集失败: '募集失败',
    运行中: '运行中',
    清盘中: '清盘中',
    已清盘: '已清盘',
};

export const productType = {
    募集层: '募集层',
    交易层: '交易层',
};

export const compositeType = {
    混合类: '混合类',
    权益类: '权益类',
    混合偏股类: '混合偏股类',
    无: '无',
};

export const filingProductType = {
    混合类: '混合类',
    权益类: '权益类',
    期货和衍生品类: '期货和衍生品类',
    无: '无',
};

export const portfolioRisk = {
    R3: 'R3',
    R4: 'R4',
    R5: 'R5',
};

export const portfolioLockType = {
    无: '无',
    份额锁定: '份额锁定',
    产品封闭: '产品封闭',
};

export const portfolioEstablishStatus = {
    NOT_START: '未上线',
    PROCESSING: '上线中',
    DONE: '已上线',
};

export const portfolioBrokerType = {
    futures: '期货',
    stock: '股票',
    credit: '信用账户',
};

export const incomeDistributionType = {
    默认分红转投: '默认分红转投',
    默认现金分红: '默认现金分红',
    分红转投: '分红转投',
    现金分红: '现金分红',
    分红转投或者现金分红: '分红转投或者现金分红',
    不分红: '不分红',
};

export const redemptionBelongingsType = {
    管理人: '管理人',
    基金资产: '基金资产',
};

export const saleType = {
    直销: '直销',
    代销: '代销',
};

export const performBaseType = {
    中证2000: '中证2000',
    国证2000: '国证2000',
    中证1000: '中证1000',
    中证500: '中证500',
    中证全指: '中证全指',
    沪深300: '沪深300',
    小市值: '小市值',
    微盘股指数: '微盘股指数',
    绝对计提: '绝对计提',
    无: '无',
};

export const performBaseRemarkType = {
    水上: '水上',
    水下: '水下',
    '分红水上，赎回水下': '分红水上，赎回水下',
};

export const profitRewardType = {
    单人单笔超额计提: '单人单笔超额计提',
    单人单笔高水位: '单人单笔高水位',
    无: '无',
};

export interface AgentPaymentRule {
    agent: string;
    from: number;
    to: number;
}

export const portfolioPropertyType = {
    券商代销: '券商代销',
    券商专户: '券商专户',
    非券商代销: '非券商代销',
    非券商专户: '非券商专户',
    集合类: '集合类',
};

export const assetTypeType = {
    券商代销: '券商代销',
    非券商代销: '非券商代销',
    资方指定: '资方指定',
    可用: '可用',
};

@Convertor
export class Portfolio extends EntityBase {
    @FieldTitle('产品名称')
    @Field.Text()
    @Validate({ required: true })
    name: string;

    @FieldTitle('简称')
    @Field.Text()
    abbr: string;

    @FieldTitle('产品编号')
    @Field.Text()
    @Validate({ required: true, pattern: /^.{6}$/ })
    no: string;

    @FieldTitle('YFE系统编号')
    @Field.Text()
    tradeNo: string;

    @FieldTitle('策略类型')
    @Field.Dict({ field: 'portfolio.type', multiple: false, valueType: 'name' })
    @Validate({ required: true })
    type: string;

    @FieldTitle('混合策略备注')
    @Field.Text()
    typeRemark: string;

    @FieldTitle('产品策略')
    @Field.Dict({ field: 'portfolio.portfolioStrategy', multiple: false, valueType: 'name' })
    portfolioStrategy: string;

    @FieldTitle('运作类型')
    @Field.Enum({ value: productType })
    @Validate({ required: true })
    productType: string;

    @FieldTitle('备注')
    @Field.Text()
    comment: string;

    @FieldTitle('托管机构')
    @Field.Dict({ field: 'portfolio.custodian', multiple: false, valueType: 'name' })
    @Validate({ required: true })
    custodian: string;

    @FieldTitle('外包方')
    @Field.Dict({ field: 'portfolio.outsourcing', multiple: false, valueType: 'name' })
    outsourcing: string;

    @FieldTitle('募集监督机构')
    @Field.Dict({ field: 'portfolio.regulatory', multiple: false, valueType: 'name' })
    regulatory: string;

    @FieldTitle('计划募集规模')
    @Field.Text()
    amount: number;

    @FieldTitle('成立日')
    @Field.Date({ searchRange: true })
    publishDate: Moment;

    @FieldTitle('建仓日')
    @Field.Date({ searchRange: true })
    startDate: Moment;

    @FieldTitle('到期日')
    @Field.Date({ searchRange: true })
    expireDate: Moment;

    @FieldTitle('开放日')
    @Field.Text()
    openDay: string;

    @FieldTitle('开放日详情')
    openDayContent: OpenDay[];

    @FieldTitle('份额确认日')
    @Field.Number({ prepends: 'T+' })
    confirmDate: number;

    @FieldTitle('风险等级')
    @Field.Enum({ value: portfolioRisk })
    riskLevel: string;

    @FieldTitle('锁定期类型')
    @Field.Enum({ value: portfolioLockType })
    lockType: string;

    @FieldTitle('锁定期')
    @Field.Text()
    lockPeriod: string;

    @FieldTitle('预警线')
    @Field.Number()
    warningLine: number;

    @FieldTitle('止损线')
    @Field.Number()
    stopLossLine: number;

    @FieldTitle('募集账户名')
    @Field.Text()
    raisingAccountName: string;

    @FieldTitle('募集账户账号')
    @Field.Text()
    raisingAccountPaymentId: string;

    @FieldTitle('开户行名称')
    @Field.Text()
    raisingAccountBankName: string;

    @FieldTitle('大额支付号')
    @Field.Text()
    raisingAccountLargePaymentId: string;

    @FieldTitle('业绩报酬计算方法')
    @Field.Text({ area: true })
    profitRewardMethod: string;

    @FieldTitle('投资范围')
    @SearchItem(SearchContractContent, onContractContentSearch)
    @Field.Text({ area: true })
    investmentRange: string;

    @FieldTitle('投资限制')
    @Field.Text({ area: true })
    investmentLimit: string;

    @FieldTitle('')
    @Field.Text({ area: true })
    selfMonitoringInvestmentIndicators: string;

    @FieldTitle('投资注意事项')
    @Field.Text({ area: true })
    investmentNotice: string;

    @FieldTitle('投资目标')
    @Field.Text()
    investmentTarget: string;

    @FieldTitle('投资策略')
    @Field.Text()
    investmentStrategy: string;

    @FieldTitle('投资经理')
    @Field.Dict({ field: 'portfolio.fundManager', multiple: false, valueType: 'name' })
    fundManager: string;

    @FieldTitle('任职时间起')
    @Field.Date()
    fundManageBegin: string;

    @FieldTitle('任职时间止')
    @Field.Date()
    fundManageEnd: string;

    @FieldTitle('是否可投资可转债')
    @Field.Boolean()
    allowConvertible: string;

    @FieldTitle('默认基金名称')
    @Transform((val: number) => val?.toString())
    @Field.Enum<Portfolio>({
        injector: 'portfolioList',
        render: val => val.name,
        search: true,
    })
    tradePortfolioId: number;

    @FieldTitle('Sharpe-Rf')
    @Field.Number()
    sharpeRf: number;

    @FieldTitle('Sortino-MAR')
    @Field.Number()
    sortinoMar: number;

    @FieldTitle('是否发送周报')
    @Field.Boolean()
    reportSendRequired: boolean;

    @FieldTitle('是否发送月报')
    @Field.Boolean()
    monthReportSendRequired: boolean;

    @FieldTitle('是否代销')
    @Field.Boolean({ radio: true })
    @Validate({ required: true })
    agency: boolean;

    @FieldTitle('销售形式')
    @Field.Enum({ value: saleType, multiple: true })
    @Validate({ required: true })
    saleType: string[];

    @FieldTitle('开放日前通知')
    @Field.Boolean()
    openDayNotify: boolean;

    @FieldTitle('代销机构')
    @Field.Dict({ field: 'portfolio.agent', multiple: true, disableCreate: true })
    agents: DataDict<null>[];

    @FieldTitle('代销打款日规则')
    agentPaymentRules: AgentPaymentRule[];

    @FieldTitle('代销费率')
    @Field.Text({ area: true })
    agencyRate: string;

    @FieldTitle('是否区分份额')
    @Field.Boolean()
    shareDivided: boolean;

    @FieldTitle('A份额')
    divideACosts: any;

    @FieldTitle('B份额')
    divideBCosts: any;

    @FieldTitle('业绩报酬计提基准日')
    profitRewardExtractDate: OpenDay[];

    @FieldTitle('固定日计提')
    fixRewardExtract: any;

    @FieldTitle('最大敞口')
    @Field.Number({ percent: true })
    maximumExposure: number;

    @FieldTitle('最新分红除权日')
    @Field.Date()
    latestExRightDate: Moment;

    @FieldTitle('业绩报酬计提方式')
    @Field.Text({ area: true })
    profitRewardMethodForAudit: string;

    @FieldTitle('自动生成周报')
    @Field.Boolean()
    reportAutoGeneration: boolean;

    @FieldTitle('基金状态')
    @Field.Enum({ value: portfolioStateType })
    portfolioState: string;

    @FieldTitle('预约截止时间')
    @Field.Text()
    reservationStop: string;

    @FieldTitle('周报展示类型')
    @Field.Enum({ value: portfolioType, nullable: true })
    displayType: keyof typeof portfolioType;

    @FieldTitle('分级产品')
    @Field.Boolean()
    graded: boolean;

    @FieldTitle('一码通')
    @Field.Text()
    csdcAc: string;

    @FieldTitle('中金所交易编码')
    @Field.Text()
    cfCode: string;

    @FieldTitle('期货保证金监控中心')
    @Field.Text()
    futuresMarginAc: string;

    @FieldTitle('巨额赎回比例')
    @Field.Number({ percent: true })
    hugeRedemptionRate: number;

    @FieldTitle('允许投资北交所')
    @Field.Boolean()
    allowBeijing: boolean;

    @FieldTitle('允许投资新三板')
    @Field.Boolean()
    allowNew3: boolean;

    @FieldTitle('上线状态')
    @Field.Enum({ value: portfolioEstablishStatus })
    establishStatus: keyof typeof portfolioEstablishStatus;

    @FieldTitle('是否编辑流程中')
    editing: boolean;

    @FieldTitle('产品信息变更流程中')
    @Field.Boolean()
    contractEditing: boolean;

    @FieldTitle('是否锁定')
    locked: boolean;

    @FieldTitle('上线材料')
    establishMaterial: PortfolioEstablishMaterial;

    @FieldTitle('经纪商')
    brokers: PortfolioBroker[];

    @FieldTitle('股票经纪商')
    stockBrokers: PortfolioBroker[];

    @FieldTitle('信用账户')
    creditBrokers: PortfolioBroker[];

    @FieldTitle('两融账户')
    lendingBrokers: PortfolioBroker[];

    @FieldTitle('期货经纪商')
    futuresBrokers: PortfolioBroker[];

    @FieldTitle('是否参与线下打新')
    @Field.Boolean()
    offlineNew: boolean;

    @FieldTitle('收益分配方式')
    @Field.Enum({ value: incomeDistributionType })
    incomeDistributionType: string;

    @FieldTitle('收益分配次数')
    @Field.Text()
    incomeDistributionTimes: string;

    @FieldTitle('投顾产品类型')
    @Field.Dict({ field: 'portfolio.investmentAdviserType', multiple: false, valueType: 'name' })
    investmentAdviserType: string;

    @FieldTitle('A类份额')
    @Transform(val => val?.toString())
    @Field.Enum<Portfolio>({
        injector: 'portfolioList',
        render: val => val?.name,
        search: true,
    })
    classAShareId?: number;

    @FieldTitle('B类份额')
    @Transform(val => val?.toString())
    @Field.Enum<Portfolio>({
        injector: 'portfolioList',
        render: val => val?.name,
        search: true,
    })
    classBShareId?: number;

    @FieldTitle('C类份额')
    @Transform(val => val?.toString())
    @Field.Enum<Portfolio>({
        injector: 'portfolioList',
        render: val => val?.name,
        search: true,
    })
    classCShareId?: number;

    @FieldTitle('交易层产品(多)')
    manyTradePortfolio?: number[];

    @FieldTitle('产品类型(合同)')
    @Field.Enum({ value: compositeType })
    compositeType: string;

    @FieldTitle('产品类型(备案)')
    @Field.Enum({ value: filingProductType })
    filingProductType: string;
}

export class FixRewardExtract {
    @FieldTitle('是否固定日计提')
    @Field.Boolean()
    fixRewardExtract: boolean;

    @FieldTitle('计提次数')
    @Field.Number()
    extractAmount: number;

    @FieldTitle('计提时间')
    @Field.Text()
    extractTime: string;
}

export class DivideCosts {
    @FieldTitle('表述')
    @Field.Text()
    description: string;

    @FieldTitle('托管费率')
    @Field.Number({ percent: true })
    custodyRate: number;

    @FieldTitle('外包服务费率')
    @Field.Number({ percent: true })
    outsourcingRate: number;

    @FieldTitle('固定托管费')
    @Field.Number()
    custodyCost: number;

    @FieldTitle('固定外包费')
    @Field.Number()
    outsourcingCost: number;

    @FieldTitle('管理费率')
    @Field.Number({ percent: true })
    manageRate: number;

    @FieldTitle('分段赎回费率')
    segmentedRedemptionRate: SegmentedData;

    @FieldTitle('赎回费率')
    @Field.Text()
    redemptionRate: string;

    @FieldTitle('赎回费归属')
    @Field.Enum({ value: redemptionBelongingsType })
    redemptionBelongings: string;

    @FieldTitle('认/申购费率')
    @Field.Number({ percent: true })
    applyRate: number;

    @FieldTitle('分段认/申购费率')
    segmentedApplyRate: SegmentedData;

    @FieldTitle('业绩计提基准')
    @Field.Dict({ field: 'portfolio.performBase', multiple: false, valueType: 'name' })
    performBase: string;

    @FieldTitle('备注')
    @Field.Enum({ value: performBaseRemarkType })
    performBaseRemark: string;

    @FieldTitle('业绩报酬提取方法')
    @Field.Enum({ value: profitRewardType })
    profitRewardType: string;

    @FieldTitle('业绩报酬率')
    @Field.Number({ percent: true })
    profitRewardRate: number;

    @FieldTitle('业绩报酬计算方法')
    @Field.Text({ area: true })
    profitRewardMethod: string;

    @FieldTitle('固定管理费率')
    @Field.Number()
    iaManageCost: number;

    @FieldTitle('保管费率')
    @Field.Number({ percent: true })
    iaSavingRate: number;

    @FieldTitle('投顾费率（信托合同）')
    @Field.Number({ percent: true })
    trustRate: number;

    @FieldTitle('固定投顾费率')
    @Field.Number({ percent: true })
    iaFixRate: number;

    @FieldTitle('浮动投顾费率')
    @Field.Number({ percent: true })
    iaFloatingRate: number;

    @FieldTitle('销售服务费率(合同)')
    @Field.Number({ percent: true })
    salesRate: number;
}

export class custodyAccounts {
    @FieldTitle('托管账户名')
    @Field.Text()
    @Validate({ required: true })
    custodyAccountName: string;

    @FieldTitle('托管账号')
    @Field.Text()
    @Validate({ required: true })
    custodyAccountNo: string;

    @FieldTitle('开户行名称')
    @Field.Text()
    @Validate({ required: true })
    custodyBankName: string;

    @FieldTitle('大额支付号')
    @Field.Text()
    custodyLargePaymentId: string;
}

export const portfolioBrokerStateMapper = {
    NORMAL: '正常',
    UNBIND: '解绑股卡未销户',
    DESTROYED: '已销户',
};

export const apaType = {
    首次: '首次',
    变更: '变更',
    停止使用: '停止使用',
};

export type PortfolioBrokerState = keyof typeof portfolioBrokerStateMapper;
export type ApaType = keyof typeof portfolioBrokerStateMapper;

@Convertor
export class PortfolioBroker extends WfEntityBase {
    portfolioId: number;

    @FieldTitle('产品名')
    @Field.Text()
    portfolioName: string;

    @FieldTitle('产品编号')
    @Field.Text()
    portfolioNo: string;

    @FieldTitle('类型')
    @Field.Enum({ value: portfolioBrokerType })
    @Validate({ required: true })
    type: keyof typeof portfolioBrokerType;

    @FieldTitle('经纪商名称')
    @Field.Dict({ field: 'portfolio.broker.brokerName', multiple: false, valueType: 'name' })
    @Validate({ required: true })
    brokerName: string;

    @FieldTitle('是否为追加')
    append: boolean;

    @FieldTitle('账号')
    @Field.Text()
    @Validate({ required: true, pattern: /^[^\s].*[^\s]$/ })
    account: string;

    @FieldTitle('统一开户编码')
    @Field.Text()
    @Validate({ required: true, pattern: /^[^\s].*[^\s]$/ })
    openAccountCode: string;

    @FieldTitle('出金限额')
    @Field.Number({ appends: '万' })
    withdrawalLimit: string;

    @FieldTitle('无出金限制')
    @Field.Boolean()
    noWithdrawalLimit: boolean;

    @FieldTitle('联系人')
    @Field.Text()
    @Validate({ pattern: /^[^\s].*[^\s]$/ })
    contact: string;

    @FieldTitle('号码')
    @Field.Text()
    @Validate({ pattern: /^[^\s].*[^\s]$/ })
    phone: string;

    @FieldTitle('沪市股东代码')
    @Field.Text()
    @Validate({ required: true, pattern: /^[^\s].*[^\s]$/ })
    shShareholderCode: string;

    @FieldTitle('深市股东代码')
    @Field.Text()
    @Validate({ required: true, pattern: /^[^\s].*[^\s]$/ })
    szShareholderCode: string;

    @FieldTitle('资金密码')
    @Field.Text()
    @Validate({ pattern: /^[^\s].*[^\s]$/ })
    fundingPassword: string;

    @FieldTitle('交易密码')
    @Field.Text()
    @Validate({ pattern: /^[^\s].*[^\s]$/ })
    tradingPassword: string;
    //后续如有为1单位提交失败变为/^[^\s].*[^\s]*$/
    @FieldTitle('通讯密码')
    @Field.Text()
    @Validate({ pattern: /^[^\s].*[^\s]$/ })
    communicationPassword: string;

    @FieldTitle('开户营业部')
    @Field.Text()
    @Validate({ required: true, pattern: /^[^\s].*[^\s]$/ })
    openDepartment: string;

    @FieldTitle('营业部代码')
    @Field.Text()
    @Validate({ pattern: /^[^\s].*[^\s]$/ })
    departmentCode: string;

    @FieldTitle('交易费率')
    @Field.Text()
    @Validate({ required: true })
    tradingRate: string;

    @FieldTitle('沪市交易费率')
    @Field.Number({ percent: true, doublePercent: true })
    @Validate({ required: true })
    shTradingRate: number;

    @FieldTitle('深市交易费率')
    @Field.Number({ percent: true, doublePercent: true })
    @Validate({ required: true })
    szTradingRate: number;

    @FieldTitle('沪市过户费率')
    @Field.Number({ percent: true, doublePercent: true })
    shPassingRate: number;

    @FieldTitle('深市过户费率')
    @Field.Number({ percent: true, doublePercent: true })
    szPassingRate: number;

    @FieldTitle('IC保证金比例')
    @Field.Number({ prepends: '交易所标准+' })
    @Validate({ required: true })
    icMarginRate: string;

    @FieldTitle('IF保证金比例')
    @Field.Number({ prepends: '交易所标准+' })
    @Validate({ required: true })
    ifMarginRate: string;

    @FieldTitle('账户开通权限')
    accountPermission: any;

    @FieldTitle('已开通业务')
    @Field.Enum({
        value: { index: '股指期货', commodity: '商品期货' },
        multiple: true,
        flatten: true,
    })
    openedBusinessList: string[];

    @FieldTitle('最小值(元)')
    @Field.Number()
    minimum: number;

    @FieldTitle('流量费(元/笔)')
    @Field.Number()
    flowCost: number;

    @FieldTitle('线下打新')
    @Field.Boolean()
    offlineNew: boolean;

    @FieldTitle('深市席位号')
    @Field.Text()
    @Validate({ pattern: /^[^\s].*[^\s]$/ })
    szSeatNumber: string;

    @FieldTitle('程序化报备类型')
    @Field.Enum({
        value: apaType,
    })
    apaType: ApaType;

    @FieldTitle('账户状态')
    @Field.Enum({
        value: portfolioBrokerStateMapper,
    })
    state: PortfolioBrokerState;

    @FieldTitle('客户号')
    @Field.Text()
    @Validate({ pattern: /^[^\s].*[^\s]$/ })
    clientNumber: string;

    @FieldTitle('其他')
    @Field.Text()
    @Validate({ pattern: /^[^\s].*[^\s]$/ })
    otherNumber: string;

    @FieldTitle('北交所权限(老)')
    @Field.Boolean()
    beijingPermission: boolean;

    @FieldTitle('北交所交易费率(老)')
    @Field.Number({ percent: true, doublePercent: true })
    beijingRate: boolean;

    @FieldTitle('投资者保障基金')
    @Field.Number({ prepends: '亿分之' })
    investorInsurance: number;

    @FieldTitle('是否包含在手续费中')
    @Field.Boolean()
    investorInsuranceInCommission: boolean;

    @FieldTitle('返还利息')
    @Field.Number({ percent: true })
    returnInterest: number;

    @FieldTitle('有效期起始时间')
    @Field.Date()
    startDate: Moment;

    @FieldTitle('有效期截至时间')
    @Field.Date()
    endDate: Moment;

    offlineProcessId: string;

    offlineComplete: boolean;

    cancelOfflineEnabled: boolean;

    tasks: WorkflowTask[];

    taskMap: Record<string, WorkflowTask>;

    commissionNote: {
        brokerId: number;
        filename: string;
        hash: string;
        id: number;
        time: string;
        url: string;
    };

    commissionTransferNote: {
        brokerId: number;
        filename: string;
        hash: string;
        id: number;
        time: string;
        url: string;
    };
}

export const portfolioBrokerEntity = field.extend(wfEntityBaseField).extend({
    portfolioId: field.number(),

    portfolioName: field.text({ title: '产品名' }),

    portfolioNo: field.text({ title: '产品编号' }),

    type: field.enum(portfolioBrokerType, { title: '类型' }),

    brokerName: field.text({ title: '经纪商名称' }),

    append: field.bool({ title: '是否为追加' }),

    account: field.text({ title: '账号' }),
}).field;

@Convertor
export class PortfolioEstablishMaterial extends WfEntityBase {
    @FieldTitle('产品要素表')
    @Field.File({ async: true })
    @Validate({ required: true })
    elementTable: string;

    @FieldTitle('是否标准要素')
    @Field.Boolean({ radio: true, trueText: '标准要素', falseText: '非标准要素，已提交特殊要素审批' })
    @Validate({ required: true })
    standardElement: boolean;

    @FieldTitle('基金合同初稿')
    @Field.File({ async: true })
    @Validate({ required: true })
    contract: string;

    @FieldTitle('基金合同定稿')
    @Field.File({ async: true })
    @Validate({ required: true })
    finalizedContract: string;

    @FieldTitle('代销协议初稿')
    @Field.File({ async: true, multiple: true })
    @Validate({ required: true })
    consignmentAgreement: string;

    @FieldTitle('代销协议定稿')
    @Field.File({ async: true, multiple: true })
    @Validate({ required: true })
    finalizedAgreement: string;

    @FieldTitle('签订代销协议扫描件')
    @Field.File({ async: true, multiple: true })
    @Validate({ required: true })
    signedAgreement: string;

    @FieldTitle('是否现金管理')
    @Field.Boolean()
    @Validate({ required: true })
    cash: boolean;

    @FieldTitle('产品运营负责人')
    @UserField({ perms: ['portfolioOperation'] })
    @Validate({ required: true })
    portfolioManagerId: number;

    portfolioManagerName?: string;

    @FieldTitle('产品市场部负责人')
    @UserField({ perms: ['portfolioMarketing'] })
    @Validate({ required: true })
    portfolioMarketInChargeId: number;

    @FieldTitle('已暂停')
    @Field.Boolean()
    paused: boolean;

    files: Record<string, FileUpload[]>;

    @FieldTitle('产品性质')
    @Field.Enum({ value: portfolioPropertyType })
    property: string;

    @FieldTitle('客户标签')
    @ClientTagField()
    tagId: number;

    @FieldTitle('资金类型')
    @Field.Enum({ value: assetTypeType })
    assetType: string;

    portfolioId: number;
}

@Convertor
export class PortfolioEstablishEditing extends PortfolioEstablishMaterial {
    em: PortfolioEstablishMaterial;

    @FieldTitle('产品名称')
    @Field.Text()
    portfolioName: string;

    @FieldTitle('产品编号')
    @Field.Text()
    portfolioNo: string;
}

export type PortfolioAgencyDTO = {
    id?: number;
    name?: string;
    abbr?: string;
    no?: string;
    type?: string;
    state?: string;
    openDay?: string;
    openDayContent?: any;
    agentPaymentRules?: any;
    agentId?: number;
    agentName?: string;
};
export type PortfolioEstablishTagWriteDTO = {
    tagId?: number;
    property?: string;
    assetType?: string;
};

export type PortfolioOutlined = Portfolio & {
    managerId?: number;
    managerName?: string;
    state?: string;
};

export const portfolioFields = getFields(Portfolio);

export const divideCostsFields = getFields(DivideCosts);

export const custodyAccountsFields = getFields(custodyAccounts);

export const fixRewardExtractFields = getFields(FixRewardExtract);

export const portfolioBrokerFields = getFields(PortfolioBroker);

export const establishMaterialFields = getFields(PortfolioEstablishMaterial);

export const establishEditingFields = getFields(PortfolioEstablishEditing);
