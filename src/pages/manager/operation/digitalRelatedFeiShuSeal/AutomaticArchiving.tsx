import React, { useState } from 'react';
import feishuApprovalService from './services/allApprovalList.service';
import { useRequest } from 'ahooks';
import { Button, Collapse, Form, Popconfirm, message, Select, Input } from 'antd';
import FeishuApproval from './components/feishuApproval';
import useNamefromOpenId from './hooks/useNamefromOpenId';
import { LinkOutlined } from '@ant-design/icons';
import fileService from '@/services/file.service';
import ShowRealName from './components/showRealName';
import { fileArchiveFields } from '../models/FileArchive';
import TextArea from 'antd/lib/input/TextArea';
import fileArchiveService from '../services/fileArchive.service';
import { useGlobalState } from '@/utils/globalStateTree';
import { Portfolio } from '../models/Portfolio';
import portfolioService from '../services/portfolio.service';
const { Panel } = Collapse;
export default function(props: { link; currentId; afterSubmit }) {
    const { Option } = Select;
    const [form] = Form.useForm();
    const { link, currentId, afterSubmit } = props;
    const { formItems } = fileArchiveFields;
    const uploadFile = useRequest(async () => {
        const res = await feishuApprovalService.getUploadFilesByUploadId(parseInt(link[0]));
        return res.data;
    });
    const [sOptions, setsOptions] = useState<number>();
    const portfolioList = useRequest(async () => {
        const res = await portfolioService.getList();
        return res.data;
    });

    const options = [
        {
            label: '公司文件',
            value: 1,
        },
        {
            label: '产品文件',
            value: 2,
        },
        {
            label: '其他文件',
            value: 3,
        },
        {
            label: '保密协议',
            value: 4,
        },
        {
            label: '代销协议',
            value: 5,
        },
    ];
    return (
        <>
            <Form form={form}>
                <Form.Item label="飞书审批编号">
                    <strong>{link[1]}</strong>
                </Form.Item>
                <Form.Item label="文件类型" name="type" rules={[{ required: true, message: '请选择文件类型' }]}>
                    <Select
                        options={options}
                        onChange={v => {
                            setsOptions(v);
                        }}
                    ></Select>
                </Form.Item>
                {sOptions == 1 && (
                    <Form.Item
                        label="产品名称"
                        name="portfolioId"
                        rules={[{ required: true, message: '请选择产品名称' }]}
                    >
                        <Select
                            filterOption={(input, option) =>
                                option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                            }
                            showSearch
                        >
                            {portfolioList?.data?.map(p => (
                                <Option value={p.id} name={p.name}>
                                    {p.name}
                                </Option>
                            ))}
                        </Select>
                    </Form.Item>
                )}
                <Form.Item label="文件名称" name="filename" rules={[{ required: true, message: '请输入文件名称' }]}>
                    <Input></Input>
                </Form.Item>
                <Form.Item label="已用印文件">
                    <Button
                        type="link"
                        href={fileService.downloadUrl(uploadFile?.data?.sealedUrl)}
                        icon={<LinkOutlined />}
                        style={{ padding: 0 }}
                    >
                        {uploadFile?.data?.sealedFileName}
                    </Button>
                </Form.Item>

                <Form.Item label="备注" name="remark">
                    <TextArea placeholder="默认自动添加-电子用印文件自动申请备注"></TextArea>
                </Form.Item>
            </Form>
            <Popconfirm
                title="确认不归档"
                onConfirm={async () => {
                    try {
                        const res = await feishuApprovalService.noAutoArchive(currentId);
                        if (res.code == 0) {
                            message.success('提交成功');
                            afterSubmit();
                        }
                    } catch (e) {
                        message.error(`提交失败:${e.message}`);
                    }
                }}
            >
                {' '}
                <Button type="primary" danger>
                    不归档
                </Button>
            </Popconfirm>
            <Popconfirm
                title="确认归档"
                onConfirm={async () => {
                    try {
                        const values = await form.validateFields();
                        values.remark = !values.remark ? '电子用印文件自动申请' : values.remark;
                        const defaultObj = {
                            assigneeId: 176,
                            file: uploadFile?.data?.sealedUrl,
                        };
                        const res = await fileArchiveService.startProcess({ ...defaultObj, ...values });
                        if (res.code == 0) {
                            message.success('申请归档成功');
                            await feishuApprovalService.agreeAutoArchive(currentId);
                            afterSubmit();
                        }
                    } catch (erroinfo) {
                        message.error(erroinfo.message);
                    }
                }}
            >
                <Button type="primary" style={{ marginLeft: 20 }}>
                    归档
                </Button>
            </Popconfirm>
        </>
    );
}
