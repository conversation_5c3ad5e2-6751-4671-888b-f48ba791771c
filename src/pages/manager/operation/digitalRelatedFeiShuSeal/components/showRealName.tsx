import { useRequest } from 'ahooks';
import React, { useEffect } from 'react';
import feishuApprovalService from '../services/allApprovalList.service';
export default function(props: { openId: string }) {
    const { openId } = props;
    const realNameReq = useRequest(
        async () => {
            const res = await feishuApprovalService.getRealNameFromOpenId(openId);
            return res.data;
        },
        { manual: true },
    );
    useEffect(() => {
        realNameReq.run();
    }, [openId]);
    return <>{realNameReq.data}</>;
}
