import React from 'react';
import { commentList, timeLine } from '../models/approvalListData';
import { Card, Collapse, Table, Tag, Button } from 'antd';
import ShowRealName from './showRealName';
import useNamefromOpenId from '../hooks/useNamefromOpenId';
import dayjs from 'dayjs';
import { LinkOutlined } from '@ant-design/icons';
export default function(props: { commentList?: commentList[]; form?: string; timeLine?: timeLine[] }) {
    const { Panel } = Collapse;
    const { commentList, form, timeLine } = props;
    const { getRealName } = useNamefromOpenId();
    const formObj = form ? JSON.parse(form) : [];
    const Approvaltypedict = {
        START: '提交',
        PASS: '通过',
        REJECT: '拒绝',
        AUTO_PASS: '自动通过',
        AUTO_REJECT: '自动拒绝',
        REMOVE_REPEAT: '去重',
        CC: '抄送',
    };
    const columns = [
        {
            title: '审批人',
            render: (_, record) => (
                <>
                    <ShowRealName openId={record.open_id} />
                </>
            ),
        },
        {
            title: '审批结果',
            key: 'type',
            dataIndex: 'type',
            render: (_, record) => <Tag color="green">{Approvaltypedict[record.type]}</Tag>,
        },
        {
            title: '审批意见',
            key: 'comment',
            dataIndex: 'comment',
            render: (_, record) => {
                return (
                    <>
                        <div>
                            <Tag>{record.comment}</Tag>
                        </div>
                        {/* {record.files?.map((file) => (
                            <div style={{
                                width: 200,
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis'
                            }}>
                                <Button
                                    type="link"
                                    href={file.url}
                                    icon={<LinkOutlined />}
                                    style={{ padding: 0 }}
                                >
                                    {file.title}
                                </Button>
                            </div>
                        ))} */}
                    </>
                );
            },
        },
        {
            title: '审批时间',
            key: 'create_time',
            dataIndex: 'create_time',
            render: (_, record) => {
                const date = new Date(parseInt(record.create_time));
                return (
                    <>
                        <Tag color="cyan">{dayjs(date).format('YYYY-MM-DD HH:mm:ss')}</Tag>
                    </>
                );
            },
        },
    ];

    return (
        <>
            <Collapse defaultActiveKey={['1', '2', '3']}>
                <Panel header="审批详情" key="1">
                    <div>
                        {formObj?.map(form => {
                            if (form.ext == null) {
                                return (
                                    <>
                                        {form.name} : {form.value} <br />
                                    </>
                                );
                            }
                            //  else {
                            //     const fileNameList = form.ext.split(",")
                            //     return (
                            //         <>
                            //             {form.name} :  {
                            //                 fileNameList.map((fileName, index) =>
                            //                     <>

                            //                         <br />
                            //                         <Button
                            //                             type="link"
                            //                             href={form.value[index]}
                            //                             icon={<LinkOutlined />}
                            //                             style={{ padding: 0 }}
                            //                         >
                            //                             {fileName}
                            //                         </Button>

                            //                         {/* <a href={form.value[index]}>{fileName}</a> */}
                            //                     </>
                            //                 )

                            //             }
                            //         </>
                            //     )
                            // }
                        })}
                    </div>
                </Panel>
                <Panel header="审批记录" key="2">
                    <Table dataSource={timeLine} columns={columns} pagination={false}></Table>
                </Panel>
                <Panel header="全文评论" key="3">
                    {commentList?.map(comment => {
                        const date = new Date(parseInt(comment.create_time));
                        return (
                            <Card
                                title={
                                    <>
                                        <ShowRealName openId={comment.open_id} />
                                    </>
                                }
                                extra={<>{<Tag>{dayjs(date).format('YYYY-MM-DD HH:mm:ss')}</Tag>}</>}
                                style={{ marginTop: 10 }}
                            >
                                {comment.comment}
                                {/* {
                                        comment.files?.map((file) => (
                                            <div style={{
                                                width: 500,
                                                whiteSpace: 'nowrap',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis'
                                            }}>
                                                <Button
                                                    type="link"
                                                    href={file.url}
                                                    icon={<LinkOutlined />}
                                                    style={{ padding: 0 }}
                                                >
                                                    {file.title}
                                                </Button>
                                            </div>
                                        ))
                                    } */}
                            </Card>
                        );
                    })}
                </Panel>
            </Collapse>
        </>
    );
}
