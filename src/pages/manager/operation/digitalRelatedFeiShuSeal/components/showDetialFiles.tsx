import useAccount from '@/hooks/useAccount';
import React, { useEffect, useState } from 'react';
import { ProColumnType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Form, Select, message, Drawer, Modal, Button, Tag, Popover, Table } from 'antd';
import { useRequest, useSetState } from 'ahooks';
import MyUpload from '@/components/MyUpload';
import { history } from 'umi';
import { functions } from 'lodash';
import dayjs from 'dayjs';
import { LinkOutlined } from '@ant-design/icons';
import fileService from '@/services/file.service';
import service from '@/services/account.service';
import { ApprovalListData, UploadFilesData } from '../models/approvalListData';
import feishuApprovalService from '../services/allApprovalList.service';
import fileArchiveService from '../../services/fileArchive.service';

export function ShowDetialFiles(props: {
    setShowDetialFiles: React.Dispatch<React.SetStateAction<boolean>>;
    currentRowData: ApprovalListData;
}) {
    const { setShowDetialFiles, currentRowData } = props;
    const formObj = currentRowData.form ? JSON.parse(currentRowData.form) : [];
    const [selectedRows, setSelectedRow] = useState<UploadFilesData[]>([]);
    // const [openModal, setOpenModal] = useState<boolean>(false);
    // const [selectOptions, setselectOptions] = useState<string>('');
    const [fileType, setFileType] = useState<string>('');
    const [fileName, setFileName] = useState<string>('');
    const uploadFilesData = useRequest(async () => {
        const res = await feishuApprovalService.getAllUploadFiles(currentRowData.id);
        return res.data;
    });
    useEffect(() => {
        uploadFilesData.run();
    }, [currentRowData.id]);
    useEffect(() => {
        const formValue1 = formObj.find(form => form.name == '用印文件类型');
        const formValue2 = formObj.find(form => form.name == '文件名称');
        setFileName(formValue2?.value);
        setFileType(formValue1?.value);
    }, [formObj]);
    const rowSelection = {
        onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRow(selectedRows);
        },
        getCheckboxProps: record => ({
            name: record.id,
            disabled: !record.printAble,
        }),
    };
    const columns = [
        {
            title: '上传的文件',
            key: 'fileName',
            dataIndex: 'fileName',
        },
        {
            title: '已盖章的文件',
            key: 'sealedFileName',
            dataIndex: 'sealedFileName',
        },
        {
            title: '状态',
            key: 'status',
            render: (_, record) => (
                <>
                    {record.inReview && <Tag color="blue">审核中</Tag>}
                    {record.printAble && <Tag color="green">可以用印</Tag>}
                    {record.rejected && <Tag color="red">拒绝用印</Tag>}
                </>
            ),
        },
        {
            title: '操作',
            render: (_, record) => (
                <>
                    {record.printAble && (
                        <span
                            style={{ color: 'blue', cursor: 'pointer' }}
                            onClick={() => {
                                goSeal([record]);
                            }}
                        >
                            用印
                        </span>
                    )}
                </>
            ),
            width: 50,
            fixed: 'right',
        },
    ];
    // const options = [
    //     {
    //         value: '每页加盖',
    //         label: '每页加盖',
    //     },
    //     {
    //         value: '首页加盖',
    //         label: '首页加盖',
    //     },
    //     {
    //         value: '尾页加盖',
    //         label: '尾页加盖',
    //     },
    //     {
    //         value: '首尾页加盖',
    //         label: '首尾页加盖',
    //     },
    //     {
    //         value: '首页加盖,每页加盖骑缝章',
    //         label: '首页加盖,每页加盖骑缝章',
    //     },
    //     {
    //         value: '尾页加盖,每页加盖骑缝章',
    //         label: '尾页加盖,每页加盖骑缝章',
    //     },
    //     {
    //         value: '首尾页加盖,每页加盖骑缝章',
    //         label: '首尾页加盖,每页加盖骑缝章',
    //     },
    // ];
    function goSeal(selectedRows) {
        console.log(selectedRows, 'selectedRows');

        const selectRowfileUrlArr = [];
        const selectRowIdArr = [];
        selectedRows.forEach(value => selectRowfileUrlArr.push(value.url));
        selectedRows.forEach(value => selectRowIdArr.push({ rowId: value.id, fileUrl: value.url }));
        if (navigator.userAgent.includes('Lark')) {
            history.replace('/new_digital_seal_editor', {
                id: currentRowData.id,
                pdfs: selectRowfileUrlArr,
                seals: currentRowData.sealFiles,
                selectRowIdArr: selectRowIdArr,
                // option: selectOptions,
                fileType,
                fileName,
            });
        } else {
            const newWindow = window.open('/new_digital_seal_editor');
            if (!newWindow) return;

            newWindow.onload = () => {
                newWindow.postMessage({
                    message: 'pdf',
                    payload: {
                        id: currentRowData.id,
                        pdfs: selectRowfileUrlArr,
                        seals: currentRowData.sealFiles,
                        selectRowIdArr: selectRowIdArr,
                        // option: selectOptions,
                        fileType,
                        fileName,
                    },
                });
            };
        }
    }

    return (
        <>
            {/* <Modal
                open={openModal}
                footer={
                    <>
                        <Button
                            onClick={() => {
                                setOpenModal(false);
                                uploadFilesData.run();
                            }}
                        >
                            取消
                        </Button>
                        <Button type="primary" style={{ marginLeft: 20 }} onClick={goSeal}>
                            用印
                        </Button>
                    </>
                }
                onCancel={() => {
                    setOpenModal(false);
                }}
            >
                <Form>
                    <Form.Item name="selectMethod" label="选择用印方法">
                        <Select
                            options={options}
                            onChange={value => {
                                setselectOptions(value);
                            }}
                        ></Select>
                    </Form.Item>
                </Form>
            </Modal> */}
            <ProTable
                toolBarRender={() => [
                    <>
                        {selectedRows.length !== 0 && (
                            <Button
                                type="primary"
                                onClick={() => {
                                    goSeal(selectedRows);
                                }}
                            >
                                用印
                            </Button>
                        )}
                    </>,
                ]}
                search={false}
                columns={columns}
                dataSource={uploadFilesData.data}
                loading={uploadFilesData.loading}
                rowSelection={rowSelection}
                rowKey={'id'}
                pagination={false}
            ></ProTable>
        </>
    );
}
