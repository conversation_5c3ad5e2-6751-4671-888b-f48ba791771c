import { PlusOutlined } from '@ant-design/icons';
import { Upload } from 'antd';
import { RcFile } from 'antd/lib/upload';
import React, { useState } from 'react';
import styles from './index.less';

export interface ImageUploadProps {
    onChange?: (image: Uint8Array) => void;
    onImgClick?: (event: React.MouseEvent<HTMLImageElement, MouseEvent>) => void;
}

export default ({ onChange, onImgClick }: ImageUploadProps) => {
    const [imageUrl, setImageUrl] = useState<string>();

    const handleImgClick = (event: React.MouseEvent<HTMLImageElement, MouseEvent>) => {
        event.stopPropagation();
        onImgClick?.(event);
    };

    const beforeUpload = async (file: RcFile) => {
        const arrayBuffer = await file.arrayBuffer();
        const imageData = new Uint8Array(arrayBuffer);
        onChange?.(imageData);
        setImageUrl(URL.createObjectURL(new Blob([imageData])));
        return false;
    };

    return (
        <Upload beforeUpload={beforeUpload} showUploadList={false} accept=".png">
            <div className={styles.wrapper}>
                <div className={styles.container}>
                    {imageUrl ? (
                        <img src={imageUrl} style={{ width: '100%' }} alt="Stamp" onClick={handleImgClick} />
                    ) : (
                        <>
                            <PlusOutlined />
                            <div style={{ marginTop: 8 }}>上传 PNG 格式图片</div>
                        </>
                    )}
                </div>
            </div>
        </Upload>
    );
};
