import fileService from '@/services/file.service';
import {
    AuditOutlined,
    ClearOutlined,
    DownloadOutlined,
    FileExcelOutlined,
    LayoutOutlined,
    QuestionCircleFilled,
    SnippetsOutlined,
    FolderOpenOutlined,
    FilePdfOutlined,
} from '@ant-design/icons';
import { Button, Divider, Row, Spin, Tooltip, Modal, Form } from 'antd';
import moment from 'dayjs';
import { values } from 'lodash';
import React, { useRef, useState } from 'react';
import { createUseStyles } from 'react-jss';
import SimpleBar from 'simplebar-react';
import 'simplebar-react/dist/simplebar.min.css';
import service from '../../services/digitalSeal.service';
import { getPDFPageNumbers } from '../utils';
import styles from './index.less';
import './simpleBar.less';
import { usePdf } from './usePDF';
import feishuApprovalService from '../services/allApprovalList.service';
import { FileUpload } from '@/models/utils/FileUpload';

const scale = 1;

const useStyles = createUseStyles({
    main: {
        height: '100%',
        width: 'calc(100% - 600px)',
        backgroundColor: '#535659',
        padding: 20,

        '& .simplebar-content': {
            display: 'flex',
            flexDirection: ({ flexDirection }: React.CSSProperties) => flexDirection,
            height: ({ flexDirection }: React.CSSProperties) => (flexDirection === 'row' ? '100%' : undefined),
            alignItems: 'center',
            gap: 10,
        },
    },
});

export default () => {
    const canvasDictRef = useRef<Record<number | string, HTMLCanvasElement>>({});
    const anchorRef = useRef<HTMLAnchorElement>(null);
    const [openModal, setOpenModal] = useState<boolean>(false);
    const [flexDirection, setFlexDirection] = useState<'row' | 'column'>('column');
    const [downloading, setDownloading] = useState(false);

    const innerStyles = useStyles({ flexDirection });

    const {
        fileName,
        fileType,
        // option,
        selectRowIdArr,
        imageUrls,
        selectedPageNumber,
        pdfDocument,
        selectedImageUrl,
        files,
        selectedFile,
        addImage,
        save,
        applyToAllPages,
        applyPagingSeal,
        selectPage,
        reset,
        resetAll,
        getId,
        selectImageUrl,
        changeFile,
        applyToAllFiles,
        saveAllFiles,
        saveAll,
    } = usePdf({
        scale,
        canvasDictRef,
    });

    const disableOperation = !selectedImageUrl || !pdfDocument;

    const download = async () => {
        const { current: anchor } = anchorRef;
        if (!anchor) return;
        setOpenModal(true);
        setDownloading(true);
        try {
            const data = await save();
            // testData.run()
            if (!data) return;

            const sealedName = `${moment().format('YYYYMMDDHHmmss')}_sealed_${selectedFile?.filename}`;
            const blob = new Blob([data]);

            const uploadResult = await fileService.uploadFile(blob, sealedName);
            // await service.seal(getId(), selectedFile.hash, uploadResult.data.hash);

            const selectedRowObj = selectRowIdArr.find(selectRow => selectRow.fileUrl == selectedFile?.url);
            await feishuApprovalService.sealAndSave(selectedRowObj.rowId, {
                filename: sealedName,
                url: uploadResult.data.hash,
            });
            const autoArchiveRes = await feishuApprovalService.autoFileArchive({
                fileName,
                fileType,
                file: uploadResult.data.hash,
            });
            const url = URL.createObjectURL(blob);
            anchor.href = url;
            anchor.download = sealedName;
            anchor.click();
        } finally {
            setDownloading(false);
        }
    };

    const downloadAll = async (file?: FileUpload) => {
        const { current: anchor } = anchorRef;
        if (!anchor) return;
        setOpenModal(true);
        setDownloading(true);

        try {
            const data = await saveAll(file);
            // testData.run()
            if (!data) return;

            const sealedName = `${moment().format('YYYYMMDDHHmmss')}_sealed_${file?.filename}`;
            const blob = new Blob([data]);

            const uploadResult = await fileService.uploadFile(blob, sealedName);
            // await service.seal(getId(), selectedFile.hash, uploadResult.data.hash);

            const selectedRowObj = selectRowIdArr.find(selectRow => selectRow.fileUrl == selectedFile?.url);
            await feishuApprovalService.sealAndSave(selectedRowObj.rowId, {
                filename: sealedName,
                url: uploadResult.data.hash,
            });
            const autoArchiveRes = await feishuApprovalService.autoFileArchive({
                fileName,
                fileType,
                file: uploadResult.data.hash,
            });
            const url = URL.createObjectURL(blob);
            anchor.href = url;
            anchor.download = sealedName;
            anchor.click();
        } finally {
            setDownloading(false);
        }
    };

    const downloadAllFiles = async () => {
        values(files).forEach((file, index) => {
            setTimeout(() => {
                changeFile(file);
                downloadAll(file);
            }, 3000 * index);
        });
        // const { current: anchor } = anchorRef;
        // if (!anchor) return;
        // setOpenModal(true);
        // setDownloading(true);
        // try {
        //     const datas = await saveAllFiles();
        //     console.log(datas,"see全部需要下载的data数据");

        //     // testData.run()
        //     if (!datas) return;
        //     for (const data in datas) {
        //         const sealedName = `${moment().format('YYYYMMDDHHmmss')}_sealed_${selectedFile?.filename}`;
        //         const blob = new Blob([data]);

        //         const uploadResult = await fileService.uploadFile(blob, sealedName);
        //         // await service.seal(getId(), selectedFile.hash, uploadResult.data.hash);

        //         const selectedRowObj = selectRowIdArr.find(selectRow => selectRow.fileUrl == selectedFile?.url);
        //         await feishuApprovalService.sealAndSave(selectedRowObj.rowId, {
        //             filename: sealedName,
        //             url: uploadResult.data.hash,
        //         });
        //         const autoArchiveRes = await feishuApprovalService.autoFileArchive({
        //             fileName,
        //             fileType,
        //             file: uploadResult.data.hash,
        //         });
        //         const url = URL.createObjectURL(blob);
        //         anchor.href = url;
        //         anchor.download = sealedName;
        //         anchor.click();
        //     }

        // } finally {
        //     setDownloading(false);
        // }
    };

    // const downloadAllFiles = async ()=>{

    // }

    const switchFlexDirection = () => {
        switch (flexDirection) {
            case 'column':
                setFlexDirection('row');
                break;
            case 'row':
                setFlexDirection('column');
                break;
        }
    };
    return (
        <>
            <Modal
                open={openModal}
                onCancel={() => {
                    setOpenModal(false);
                }}
                onOk={() => {
                    setOpenModal(false);
                }}
            >
                {'已自动归档'}
            </Modal>
            <div className={styles.wrapper}>
                <div className={styles.toolbar}>
                    <Tooltip overlay="应用选择页面改动到所有文件">
                        <Button
                            icon={<FolderOpenOutlined />}
                            type="primary"
                            onClick={applyToAllFiles}
                            disabled={disableOperation}
                        >
                            应用选择页面改动到所有文件
                        </Button>
                    </Tooltip>
                    <Divider type="vertical" style={{ borderColor: '#ffffffd9' }} />
                    <Tooltip overlay="应用选择页面改动到所有页面">
                        <Button
                            icon={<SnippetsOutlined />}
                            type="primary"
                            onClick={applyToAllPages}
                            disabled={disableOperation}
                        >
                            应用选择页面改动到所有页面
                        </Button>
                    </Tooltip>
                    <Divider type="vertical" style={{ borderColor: '#ffffffd9' }} />
                    <Tooltip overlay="添加骑缝章">
                        <Button
                            icon={<AuditOutlined />}
                            type="primary"
                            onClick={() => {
                                applyPagingSeal();
                                setFlexDirection('row');
                            }}
                            disabled={disableOperation}
                        >
                            添加骑缝章
                        </Button>
                    </Tooltip>
                    <Divider type="vertical" style={{ borderColor: '#ffffffd9' }} />
                    <Tooltip overlay="改变为横/纵向布局">
                        <Button
                            icon={<LayoutOutlined />}
                            type="primary"
                            onClick={switchFlexDirection}
                            disabled={!pdfDocument}
                        >
                            改变为横/纵向布局
                        </Button>
                    </Tooltip>
                    <Divider type="vertical" style={{ borderColor: '#ffffffd9' }} />
                    <Tooltip overlay="清除选择页面改动">
                        <Button
                            icon={<FileExcelOutlined />}
                            type="primary"
                            onClick={() => reset(selectedPageNumber)}
                            disabled={disableOperation}
                        >
                            清除选择页面改动
                        </Button>
                    </Tooltip>
                    <Divider type="vertical" style={{ borderColor: '#ffffffd9' }} />
                    <Tooltip overlay="清除所有页面改动">
                        <Button icon={<ClearOutlined />} type="primary" onClick={resetAll} disabled={disableOperation}>
                            清除所有页面改动
                        </Button>
                    </Tooltip>

                    <Divider type="vertical" style={{ borderColor: '#ffffffd9' }} />
                    <Tooltip overlay="保存并下载">
                        <Button icon={<DownloadOutlined />} type="primary" onClick={download} disabled={!pdfDocument}>
                            保存并下载
                        </Button>
                    </Tooltip>
                    <Divider type="vertical" style={{ borderColor: '#ffffffd9' }} />
                    <Tooltip overlay="保存并下载所有文件">
                        <Button
                            icon={<FilePdfOutlined />}
                            type="primary"
                            onClick={downloadAllFiles}
                            disabled={!pdfDocument}
                        >
                            保存并下载所有文件
                        </Button>
                    </Tooltip>
                </div>
                <div className={styles.body}>
                    <div style={{ width: 300, backgroundColor: '#323639', padding: 24, overflow: 'auto' }}>
                        {values(files).map((file, index) => (
                            <div key={index} style={{ padding: '5px 0' }}>
                                <a
                                    onClick={() => changeFile(file)}
                                    className={styles.fileItem}
                                    style={{ color: file.hash === selectedFile?.hash ? undefined : '#ffffffd9' }}
                                >
                                    {file.filename}
                                </a>
                            </div>
                        ))}
                    </div>
                    <SimpleBar className={innerStyles.main} autoHide={false}>
                        {getPDFPageNumbers(pdfDocument).map(pageNumber => (
                            <div
                                key={pageNumber}
                                style={{
                                    position: 'relative',
                                    outline: pageNumber === selectedPageNumber ? '4px solid #448ef7' : undefined,
                                }}
                                onMouseDown={() => selectPage(pageNumber)}
                            >
                                <canvas
                                    className={styles.canvas}
                                    ref={dom => dom && (canvasDictRef.current[pageNumber] = dom)}
                                />
                            </div>
                        ))}
                        {flexDirection === 'row' && (
                            <div>
                                <canvas width={20} />
                            </div>
                        )}
                    </SimpleBar>
                    <div className={styles.nav}>
                        <Row style={{ color: '#ffffffd9', fontSize: 20, marginBottom: 12 }} justify="center">
                            <Tooltip
                                overlay={
                                    <div>
                                        <div style={{ marginBottom: 12 }}>点击左侧文件列表中项目，可选择文件。</div>
                                        <div style={{ marginBottom: 12 }}>点击中间文档，可选择页面。</div>
                                        <div style={{ marginBottom: 12 }}>双击下方图片，可直接添加印章到所选页面。</div>
                                        <div>
                                            点击下方图片，选择想要使用的印章，再点击顶栏“添加骑缝章”图标，即可添加骑缝章。
                                        </div>
                                    </div>
                                }
                            >
                                <QuestionCircleFilled />
                            </Tooltip>
                        </Row>
                        {imageUrls?.map((url, index) => (
                            <Tooltip overlay={<>单击选择切换图片，双击将选择到的图片应用到所选的页面上</>}>
                                <div
                                    key={index}
                                    className={styles.imgContainer}
                                    style={{
                                        marginBottom: 24,
                                        border: selectedImageUrl === url ? '2px solid #448ef7' : undefined,
                                    }}
                                >
                                    <div
                                        className={styles.img}
                                        style={{ backgroundImage: `url(${url})` }}
                                        onClick={() => selectImageUrl(url)}
                                        onDoubleClick={() => addImage(url)}
                                    />
                                </div>
                            </Tooltip>
                        ))}
                    </div>
                </div>
                <a ref={anchorRef} style={{ display: 'none' }}></a>
                {downloading && (
                    <div
                        style={{
                            background: '#00000090',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            position: 'absolute',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                        }}
                    >
                        <Spin />
                    </div>
                )}
            </div>
        </>
    );
};
