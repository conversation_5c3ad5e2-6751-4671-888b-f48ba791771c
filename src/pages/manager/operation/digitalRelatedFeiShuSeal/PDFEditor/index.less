.wrapper {
    height: 100vh;

    .toolbar {
        position: fixed;
        width: 100%;
        height: 56px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #323639;
        box-shadow: 0 -2px 8px #00000017, 0 4px 8px #0000000f, 0 1px 2px #0000004d, 0 2px 6px #00000026;
    }

    .body {
        height: 100%;
        padding-top: 56px;
        display: flex;

        .nav {
            width: 300px;
            background-color: #323639;
            overflow: overlay;
            padding: 24px;

            .tip {
                color: #ffffffd9;
                font-size: 16px;
            }
        }

        .canvas {
            position: absolute;
            left: 0;
            top: 0;
        }

        .fileItem {
            transition: 0.3s;
            &:hover {
                color: #40a9ff !important;
            }
        }
    }
}

.imgContainer {
    width: 252px;
    height: 252px;
    margin: 0 auto;
    color: #ffffffd9;
    cursor: pointer;
    border: 2px dashed #ffffffd9;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: hidden;

    .img {
        width: 100%;
        height: 100%;
        background-repeat: no-repeat;
        background-position: center;
        background-size: 80%;
    }
}

.mask {
    background-color: #00000073;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
}
