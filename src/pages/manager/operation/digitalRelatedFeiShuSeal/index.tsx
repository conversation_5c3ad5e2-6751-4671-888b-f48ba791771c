import useAccount from '@/hooks/useAccount';
import React, { useEffect, useState } from 'react';
import { ProColumnType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Form, Select, message, Drawer, Modal, Button, Tag, Popover, Divider, Upload } from 'antd';
import { useRequest } from 'ahooks';
import feishuApprovalService from './services/allApprovalList.service';
import { ApprovalListData, UploadFilesData } from './models/approvalListData';
import { SingleFileUpload } from '../ipoLot/components/SingleFileUpload';
import MyUpload from '@/components/MyUpload';
import { history } from 'umi';
import { functions } from 'lodash';
import dayjs from 'dayjs';
import FeishuApproval from './components/feishuApproval';
import useNamefromOpenId from './hooks/useNamefromOpenId';
import { LinkOutlined } from '@ant-design/icons';
import fileService from '@/services/file.service';
import service from '@/services/account.service';
import { useOptionsWithLabelUnique, useOptionsWithUnique } from '../ipoLot/hooks/useDataToOptions';
import { useFilterTableData } from '../ipoLot/hooks/useFilterTableData';
import { saleType } from '../models/Portfolio';
import { ShowDetialFiles } from './components/showDetialFiles';
import ShowRealName from './components/showRealName';
import { compact } from 'lodash';
import conf from '@/conf';
import { UploadOutlined } from '@ant-design/icons';

export default function() {
    const [form] = Form.useForm();
    // const { userList, getRealName } = useNamefromOpenId();
    const [currentRow, setCurretRow] = useState<ApprovalListData>({} as ApprovalListData);
    const [uploadFileModal, setUploadFileModal] = useState<boolean>(false);
    const [showDetialFiles, setShowDetialFiles] = useState<boolean>(false);
    const [openDrawer, setOpenDrawer] = useState<boolean>(false);
    const [defaultApprovalUser, setDefaultApprovalUser] = useState<number>();
    const currentUser = useAccount.useContainer().useCurrentUser();

    const approvalList = useRequest(async () => {
        const currentUserReq = await service.getInfo();
        const currentUser = currentUserReq?.data;
        const res = await feishuApprovalService.getCurrentUserApproval(currentUser?.id);
        if (res.data == true) {
            const resData = await feishuApprovalService.getAllData(currentUser?.id);
            return resData.data;
        } else {
            message.error('飞书数据写入数据库失败');
        }
    });

    const userList = useRequest(async () => {
        try {
            const res = await service.getListMin();
            if (res.code == 0) {
                return res.data;
            }
        } catch (e) {
            message.error(`加载用户列表失败${e.message}`);
        }
    });

    const sealApprovalUser = useRequest(async () => {
        try {
            const res = await feishuApprovalService.getApprovalUser();
            if (res.code == 0) {
                return res.data;
            }
        } catch (e) {
            message.error(`获取当前审批用户失败${e.message}`);
        }
    });

    useEffect(() => {
        setDefaultApprovalUser(sealApprovalUser?.data?.userId);
    }, [sealApprovalUser.data]);

    const sealTypeOptions = useOptionsWithLabelUnique<ApprovalListData, string>({
        data: approvalList.data,
        mapFunc: (val: ApprovalListData) => ({ value: val.approvalType, label: val.approvalType }),
    });

    const userListShowOptions = useOptionsWithLabelUnique({
        data: userList.data,
        mapFunc: val => ({ value: val.id, label: val.realName ? val.realName : val.username }),
    });

    const complianceOfficialOptions = [
        { value: 124, label: '王慕涵' },
        { value: 200, label: '陈雨纯' },
        { value: 176, label: '刘君妍' },
        { value: 157, label: '孟慧慧' },
        { value: 188, label: '吴艺璇' },
        { value: 175, label: '肖怡馨' },
        { value: 194, label: '谢铭' },
    ];

    const columns: ProColumns<ApprovalListData>[] = [
        {
            title: '状态',
            render: (_, record) => {
                if (record.uploadFiles.length == 0) {
                    return (
                        <>
                            <Tag color="red">待上传文件</Tag>
                        </>
                    );
                }
                if (record.printAble && !record.rejected) {
                    // const waitToSealFilesArr = record.uploadFiles.map(uploadFile => uploadFile.printAble);
                    const rejectFilesArr = record.uploadFiles.filter(uploadFile => uploadFile.rejected == true);
                    const inReviewFilesArr = record.uploadFiles.filter(uploadFile => uploadFile.inReview == true);
                    // const sealedFileArr = record.uploadFiles.map(uploadFile => uploadFile.sealedUrl);
                    // const showSealed = compact(waitToSealFilesArr).length == compact(sealedFileArr).length;
                    // const hasSealed = compact(sealedFileArr).length;

                    return (
                        <>
                            <Tag color="green">可以用印</Tag>
                            {!!rejectFilesArr.length && <Tag color="red">部分已拒绝</Tag>}
                            {!!inReviewFilesArr.length && <Tag color="blue">部分正在审批</Tag>}
                            {/* {hasSealed ? (
                                showSealed ? (
                                    <Tag color="green">全部已用印</Tag>
                                ) : (
                                    <Tag color="lime">部分已用印</Tag>
                                )
                            ) : (
                                <Tag color="magenta">待用印</Tag>
                            )} */}
                        </>
                    );
                }
                // if (!record.printAble && !record.rejected) {
                //     return (
                //         <>
                //             <Tag color="red">审批中</Tag>
                //         </>
                //     );
                // }
                // if (!record.printAble && record.rejected) {
                //     return (
                //         <>
                //             <Tag color="#f50">已被驳回</Tag>
                //         </>
                //     );
                // }
            },
        },
        {
            title: '审批类型',
            dataIndex: 'approvalType',
            key: 'approvalType',
        },
        {
            title: '创建时间',
            render: (_, record) => {
                const date = new Date(parseInt(record.endTime));
                return <>{dayjs(date).format('YYYY-MM-DD')}</>;
            },
            sorter: (a, b) => parseInt(a.endTime) - parseInt(b.endTime),
        },
        {
            title: '申请人',
            dataIndex: 'openId',
            key: 'openId',
            render: (_, record) => (
                <>
                    <ShowRealName openId={record.openId} />
                </>
            ),
        },
        {
            title: '飞书审批编号',
            dataIndex: 'serialNumber',
            key: 'serialNumber',
        },
        {
            title: '印章类型',
            dataIndex: 'sealType',
            key: 'sealType',
        },
        {
            title: '查看飞书审批详情',
            render: (_, record) => (
                <>
                    <span
                        style={{ color: '#2db7f5', cursor: 'pointer' }}
                        onClick={() => {
                            setOpenDrawer(true);
                            setCurretRow(record);
                        }}
                    >
                        查看飞书审批详情
                    </span>
                </>
            ),
        },
        {
            title: '待用印文件',
            render: (_, record) => (
                <>
                    {
                        <Popover
                            trigger="hover"
                            content={
                                <>
                                    {record?.uploadFiles?.map(file => (
                                        <div style={{ padding: '5px 0' }}>
                                            <Button
                                                type="link"
                                                href={fileService.downloadUrl(file.url)}
                                                icon={<LinkOutlined />}
                                                style={{ padding: 0 }}
                                            >
                                                {file.fileName}
                                            </Button>
                                        </div>
                                    ))}
                                </>
                            }
                        >
                            {record.uploadFiles.length == 0
                                ? currentUser.id !== 124 && (
                                      <span
                                          style={{ color: '#108ee9', cursor: 'pointer' }}
                                          onClick={() => {
                                              setCurretRow(record);
                                              setUploadFileModal(true);
                                          }}
                                      >
                                          上传
                                      </span>
                                  )
                                : currentUser.id !== 124 && (
                                      <span
                                          style={{ color: '#108ee9', cursor: 'pointer' }}
                                          onClick={() => {
                                              setCurretRow(record);
                                              setUploadFileModal(true);
                                          }}
                                      >
                                          重新上传
                                      </span>
                                  )}
                        </Popover>
                    }
                </>
            ),
        },
        {
            title: '用印完成文件',
            render: (_, record) => (
                <>
                    {record?.uploadFiles?.length !== 0 && (
                        <Popover
                            trigger="hover"
                            content={
                                <>
                                    {record?.uploadFiles?.map(file => (
                                        <div style={{ padding: '5px 0' }}>
                                            <Button
                                                type="link"
                                                href={fileService.downloadUrl(file.sealedUrl)}
                                                icon={<LinkOutlined />}
                                                style={{ padding: 0 }}
                                            >
                                                {file.sealedFileName ? file.sealedFileName : '暂未用印'}
                                            </Button>
                                        </div>
                                    ))}
                                </>
                            }
                        >
                            <span style={{ color: '#1890ff', cursor: 'pointer' }}>查看详情</span>
                        </Popover>
                    )}
                </>
            ),
        },
        {
            title: '操作',
            render: (_, record) => (
                <>
                    {record.printAble && currentUser.id !== 124 && (
                        <span
                            style={{ color: 'green', cursor: 'pointer' }}
                            onClick={() => {
                                setShowDetialFiles(true);
                                setCurretRow(record);
                            }}
                        >
                            用印
                        </span>
                    )}
                </>
            ),
        },
    ];
    return (
        <>
            <Drawer
                open={openDrawer}
                onClose={() => {
                    setOpenDrawer(false);
                }}
                size="large"
                title={
                    <>
                        {/* <strong>{getRealName(currentRow.openId)} </strong> 提交于 {dayjs(new Date(parseInt(currentRow.endTime))).format("YYYY-MM-DD HH:mm:ss")} */}
                        飞书审批编号：<strong>{currentRow.serialNumber}</strong>
                    </>
                }
            >
                <FeishuApproval
                    commentList={currentRow.commentList}
                    form={currentRow.form}
                    timeLine={currentRow.timeLine}
                />
            </Drawer>
            <Modal
                open={uploadFileModal}
                onOk={async () => {
                    const finalArr = [];
                    const { uploadFiles } = form.getFieldsValue();
                    const { fileList } = uploadFiles;
                    for (const file of fileList) {
                        if (file.response) {
                            finalArr.push(file.response.data);
                        }
                    }
                    try {
                        const res = await feishuApprovalService.uploadFiles(currentRow.id, finalArr);
                        if (res.code == 0) {
                            message.success('文件上传成功');
                            form.resetFields();
                            setUploadFileModal(false);
                            approvalList.run();
                        }
                    } catch (e) {
                        message.error(`文件上传失败:${e.message}`);
                    }
                }}
                onCancel={() => {
                    setUploadFileModal(false);
                    form.resetFields();
                }}
            >
                <Form form={form}>
                    <Form.Item name="uploadFiles" label="上传将要用印的pdf文件">
                        <Upload action={`${conf.serviceUrl}/file`} multiple>
                            <Button icon={<UploadOutlined />}>点击或拖拽多个文件上传</Button>
                        </Upload>
                    </Form.Item>
                </Form>
            </Modal>
            <ProTable
                search={false}
                toolBarRender={() => [
                    <>
                        <Form layout="inline">
                            {currentUser?.id == 124 && (
                                <Form.Item label="选择审批人">
                                    <Select
                                        options={complianceOfficialOptions}
                                        style={{ width: 150 }}
                                        value={defaultApprovalUser}
                                        onChange={async val => {
                                            setDefaultApprovalUser(val);
                                            try {
                                                const res = await feishuApprovalService.changeApprovalUser(val);
                                                if (res.code == 0) {
                                                    message.success('更改审批人成功');
                                                }
                                            } catch (e) {
                                                message.error(`更改审批人失败${e.message}`);
                                            }
                                        }}
                                    ></Select>
                                </Form.Item>
                            )}
                            <Form.Item label="用印申请类型">
                                <Select options={sealTypeOptions} style={{ width: 150 }}></Select>
                            </Form.Item>
                        </Form>
                    </>,
                ]}
                columns={columns}
                dataSource={approvalList.data}
                loading={approvalList.loading}
                request={() => approvalList.run()}
            ></ProTable>
            {showDetialFiles && (
                <Modal
                    open={showDetialFiles}
                    onCancel={() => {
                        setShowDetialFiles(false);
                        approvalList.run();
                    }}
                    title={
                        <>
                            飞书审批编号:<strong>{currentRow.serialNumber}</strong>
                        </>
                    }
                    width={1200}
                >
                    <ShowDetialFiles
                        setShowDetialFiles={setShowDetialFiles}
                        currentRowData={currentRow}
                    ></ShowDetialFiles>
                </Modal>
            )}
        </>
    );
}
