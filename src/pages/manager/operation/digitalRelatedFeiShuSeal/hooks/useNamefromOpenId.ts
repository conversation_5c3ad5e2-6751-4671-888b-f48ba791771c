import useAccount from '@/hooks/useAccount';
import React, { useEffect, useState } from 'react';
import { ProColumnType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Form, Select, message, Drawer, Modal, Button, Tag } from 'antd';
import { useRequest } from 'ahooks';
import feishuApprovalService from '../services/allApprovalList.service';

import MyUpload from '@/components/MyUpload';
import { history } from 'umi';
import { functions } from 'lodash';
import dayjs from 'dayjs';
// import { User } from '@/models/account/User';
interface User {
    openId: string;
    realName: string;
    username: string;
}
export default function() {
    const [userList, setUserList] = useState<User[]>([]);
    const UserList = useRequest(async () => {
        const res = await feishuApprovalService.getAllUser();
        return res.data;
    });
    useEffect(() => {
        setUserList(UserList.data);
    }, [UserList.data]);
    function getRealName(openId) {
        const user = userList?.find(user => user.openId == openId);
        return user?.realName ? user?.realName : user?.username;
    }
    return {
        getRealName,
        userList,
    };
}
