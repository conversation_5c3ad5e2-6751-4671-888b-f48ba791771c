export interface ApprovalListData {
    approvalCode: string;
    approvalName: string;
    approvalType: string;
    endTime: string;
    form: string;
    id: number;
    instanceCode: string;
    needArching: boolean;
    openId: string;
    printAble: boolean;
    sealFiles: Array<string>;
    sealType: string;
    serialNumber: string;
    status: string;
    urgentOrNot: boolean;
    uploadFiles: UploadFilesData[];
    commentList: commentList[];
    timeLine: timeLine[];
    rejected: boolean;
}

export interface UploadFilesData {
    id: number;
    fileName: string;
    url: string;
    sealedFileName: string;
    sealedUrl: string;
    inReview: boolean;
    printAble: boolean;
    rejected: boolean;
}
export interface timeLine {
    type: string;
    files: file[];
    comment: string;
    create_time: string;
    open_id: string;
}
export interface commentList {
    id: string;
    comment: string;
    create_time: string;
    open_id: string;
    user_id: string;
    files: file[];
}
export interface file {
    file_size: string;
    title: string;
    type: string;
    url: string;
}
