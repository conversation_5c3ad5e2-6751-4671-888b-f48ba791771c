import service from '@/utils/service';
import { CustomResponse } from '@/models/common/CustomResponse';
import { ApprovalListData, UploadFilesData } from '../models/approvalListData';
const prefix = '/feishu';

// const list_prefix = "operation/portfolio/outline"
class feishuApprovalService {
    getCurrentUserApproval(id: number) {
        return service.get<CustomResponse<boolean>>(`${prefix}/get_feishu_approval_list/${id}`);
    }

    getAllData(id: number) {
        return service.get<CustomResponse<ApprovalListData[]>>(`${prefix}/getAllData/${id}`);
    }

    uploadFiles(id: number, data) {
        return service.post<CustomResponse<null>>(`${prefix}/uploadFiles/${id}`, { data });
    }
    getAllUploadFiles(id: number) {
        return service.get<CustomResponse<UploadFilesData[]>>(`${prefix}/getUploadFiles/${id}`);
    }
    sealAndSave(id, data) {
        return service.post<CustomResponse<null>>(`${prefix}/sealSave/${id}`, { data });
    }
    getUserApprovalById(id: number) {
        return service.get<CustomResponse<ApprovalListData>>(`${prefix}/getUserApprovalById/${id}`);
    }
    agreeToPrint(data: { messageId: number; approvalId: number; openId: string; uploadFilesId: number[] }) {
        return service.post<CustomResponse<null>>(`${prefix}/agressToPrint`, { data });
    }
    noAgreeToPrint(data: { messageId: number; approvalId: number; openId: string }) {
        return service.post<CustomResponse<null>>(`${prefix}/noAgreeToPrint`, { data });
    }
    getRealNameFromOpenId(data) {
        return service.post<CustomResponse<string>>(`${prefix}/getRealNameFromOpenId`, { data });
    }
    getUploadFilesByUploadId(id) {
        return service.get<CustomResponse<UploadFilesData>>(`${prefix}/getUploadFilesByUploadId/${id}`);
    }
    noAutoArchive(id) {
        return service.post<CustomResponse<null>>(`${prefix}/noAutoArchive/${id}`);
    }
    agreeAutoArchive(id) {
        return service.post<CustomResponse<null>>(`${prefix}/agreeAutoArchive/${id}`);
    }
    autoFileArchive(data) {
        return service.post<CustomResponse<null>>(`${prefix}/autoArchive`, { data });
    }
    getUserList() {
        return service.get(`${prefix}/getUserList`);
    }
    getApprovalUser() {
        return service.get(`${prefix}/getApprovalUser`);
    }
    changeApprovalUser(id) {
        return service.post(`${prefix}/changeApprovalUser/${id}`);
    }
}

export default new feishuApprovalService();
