import React from 'react';
import feishuApprovalService from './services/allApprovalList.service';
import { useRequest } from 'ahooks';
import { Button, Collapse, Popconfirm, message } from 'antd';
import FeishuApproval from './components/feishuApproval';
import useNamefromOpenId from './hooks/useNamefromOpenId';
import { LinkOutlined } from '@ant-design/icons';
import fileService from '@/services/file.service';
import ShowRealName from './components/showRealName';
import { upload } from '../portfolio/tradeManage/distributed/queries';
const { Panel } = Collapse;
export default function(props: { link; currentId; afterSubmit }) {
    const { link, currentId, afterSubmit } = props;
    const approvalList = useRequest(async () => {
        const res = await feishuApprovalService.getUserApprovalById(parseInt(link[0]));
        return res.data;
    });

    function getUploadFilesId() {
        return approvalList?.data?.uploadFiles
            .filter(uploadFile => uploadFile.inReview == true)
            .map(uploadFile => uploadFile.id);
    }
    return (
        <>
            <Collapse defaultActiveKey={['1', '2']}>
                <Panel
                    header={
                        <>
                            <ShowRealName openId={approvalList?.data?.openId} />
                            上传的待用印文件
                        </>
                    }
                    key="1"
                >
                    {approvalList?.data?.uploadFiles
                        .filter(uploadFile => uploadFile.inReview == true)
                        .map(uploadFile => (
                            <>
                                <Button
                                    type="link"
                                    href={fileService.downloadUrl(uploadFile.url)}
                                    icon={<LinkOutlined />}
                                    style={{ padding: 0 }}
                                >
                                    {uploadFile.fileName}
                                </Button>
                                {/* <a href="test">{uploadFile.fileName}</a> */}
                                <br />
                            </>
                        ))}
                </Panel>
                <Panel
                    header={
                        <>
                            <strong>{`飞书审批编号:(${approvalList?.data?.serialNumber})`}</strong>
                        </>
                    }
                    key="2"
                >
                    <FeishuApproval
                        form={approvalList?.data?.form}
                        commentList={approvalList?.data?.commentList}
                        timeLine={approvalList?.data?.timeLine}
                    />
                </Panel>
            </Collapse>
            <Popconfirm
                title="确认不同意"
                onConfirm={async () => {
                    try {
                        const res = await feishuApprovalService.noAgreeToPrint({
                            uploadFilesId: getUploadFilesId(),
                            messageId: currentId,
                            approvalId: approvalList?.data?.id,
                            openId: approvalList?.data?.openId,
                        });
                        if (res.code == 0) {
                            message.success('提交成功');
                            afterSubmit();
                        }
                    } catch (e) {
                        message.error(`提交失败:${e.message}`);
                    }
                }}
            >
                <Button type="primary" danger>
                    不同意用印
                </Button>
            </Popconfirm>
            <Popconfirm
                title="确认同意"
                onConfirm={async () => {
                    try {
                        const res = await feishuApprovalService.agreeToPrint({
                            uploadFilesId: getUploadFilesId(),
                            messageId: currentId,
                            approvalId: approvalList?.data?.id,
                            openId: approvalList?.data?.openId,
                        });
                        if (res.code == 0) {
                            message.success('提交成功');
                            afterSubmit();
                        }
                    } catch (e) {
                        message.error(`提交失败:${e.message}`);
                    }
                }}
            >
                <Button type="primary" style={{ marginLeft: 20 }}>
                    同意用印
                </Button>
            </Popconfirm>
        </>
    );
}
