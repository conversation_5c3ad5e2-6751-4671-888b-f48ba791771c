import moment from 'dayjs';
import React from 'react';
import { Calendar, Tag, Tooltip, Spin, Popover, Form, Input, Button } from 'antd';
import useOpenDay from '../../hooks/useOpenDay';
import styles from './index.less';

export default function() {
    const { openDayDict, loading } = useOpenDay.useContainer();
    const form = Form.useForm();
    const y = moment().year();
    const onFinish = (values: any) => {
        console.log('Finish:', values);
    };
    return (
        <Spin spinning={loading}>
            <Calendar
                validRange={[moment(new Date(y, 0, 1)), moment(new Date(y + 1, 11, 31))]}
                dateCellRender={date => {
                    if (!openDayDict) {
                        return null;
                    }
                    const dStr = date.format('YYYYMMDD');
                    if (!openDayDict[dStr]) {
                        return null;
                    }

                    const Portfolio = ({ portfolios }) =>
                        portfolios.map(x => (
                            <Tooltip key={x.id} title={x.openDay}>
                                <Tag color={x.agency ? 'purple' : 'blue'}>{x.abbr || x.name}</Tag>
                            </Tooltip>
                        ));

                    if (openDayDict[dStr].length > 3) {
                        return (
                            <Popover
                                placement={'right'}
                                content={
                                    <div className={styles.popOver}>
                                        <Portfolio portfolios={openDayDict[dStr]}></Portfolio>
                                    </div>
                                }
                            >
                                <div className={styles.dayContentWrapper}>
                                    <Portfolio portfolios={openDayDict[dStr].slice(0, 2)}></Portfolio>
                                    <span>...</span>
                                </div>
                            </Popover>
                        );
                    } else {
                        return (
                            <div className={styles.dayContentWrapper}>
                                <Portfolio portfolios={openDayDict[dStr]}></Portfolio>
                            </div>
                        );
                    }
                }}
            />
        </Spin>
    );
}
