import React, { useEffect, useState } from 'react';
import { Button } from 'antd';
import UserSelect from '@/components/UserSelect';
import { User } from '@/models/account/User';
import useUserList from '@/hooks/useUserList';

export interface AssigneeSetterProps {
    name: string;
    id: number;
    canChangeAssignee: boolean;
    onSubmit: (assignee: User) => Promise<void>;
}

export default function AssigneeSetter(props: AssigneeSetterProps) {
    const { name, id, canChangeAssignee, onSubmit } = props;

    const userReq = useUserList.useContainer().useUserList([]);
    const [reassigning, setReassigning] = useState(false);
    const [reassignTo, setReassignTo] = useState<User>(null);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (id != null && userReq.data != null) {
            setReassignTo(userReq.data.find(x => x.id === id));
        }
    }, [id, userReq.data]);

    useEffect(() => {
        setReassigning(false);
    }, [id]);

    if (!canChangeAssignee) {
        return <>{name}</>;
    }

    if (reassigning) {
        return (
            <>
                <UserSelect
                    style={{ width: 240 }}
                    value={reassignTo?.id}
                    onChangeObject={setReassignTo}
                    disabled={loading}
                />
                <Button
                    type="link"
                    loading={loading}
                    disabled={loading}
                    onClick={async () => {
                        if (id !== reassignTo?.id) {
                            setLoading(true);
                            await onSubmit(reassignTo);
                            setLoading(false);
                        }
                        setReassigning(false);
                    }}
                >
                    确认
                </Button>
                <Button type="text" disabled={loading} onClick={() => setReassigning(false)}>
                    取消
                </Button>
            </>
        );
    } else {
        return (
            <>
                {name}
                <Button type="link" onClick={() => setReassigning(true)}>
                    修改
                </Button>
            </>
        );
    }
}
