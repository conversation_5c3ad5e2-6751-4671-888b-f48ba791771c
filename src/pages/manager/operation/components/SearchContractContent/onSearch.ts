import { mapValues } from 'lodash';
import contractContent from '@/assets/contractContent.json';
import { Portfolio } from '../../models/Portfolio';

const matchers = mapValues(
    contractContent,
    v =>
        new RegExp(
            v
                .map(x => {
                    // 替换括号，全角和半角都能匹配
                    const replaced = x.replace(/[(（]/g, '[(（]').replace(/[）)]/g, '[）)]');
                    return replaced;
                })
                .join('|'),
        ),
);

export default function onSearch(record: Portfolio, text: string) {
    if (!record.investmentRange) {
        return false;
    }

    const matcher: RegExp = matchers[text];
    if (!matcher) {
        return false;
    }

    return record.investmentRange.match(matcher) != null;
}
