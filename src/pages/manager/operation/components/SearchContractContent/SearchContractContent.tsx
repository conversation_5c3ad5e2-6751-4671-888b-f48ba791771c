import React, { useContext } from 'react';
import { Form, Select } from 'antd';
import contractContent from '@/assets/contractContent.json';
import { SearchItemProps, searchContext } from '@/utils/fieldProperty';

const options = Object.keys(contractContent).map(k => ({ lavel: k, value: k }));

export default function SearchContractContent(props: SearchItemProps) {
    const { label, name, ...otherProps } = props;
    const { triggerSearch } = useContext(searchContext);

    return (
        <Form.Item name={name} {...otherProps}>
            <Select
                placeholder={label}
                options={options}
                showSearch
                allowClear
                onSelect={triggerSearch}
                onClear={triggerSearch}
            />
        </Form.Item>
    );
}
