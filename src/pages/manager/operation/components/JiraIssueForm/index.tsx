import React, { useState, useEffect } from 'react';
import { message, Spin } from 'antd';
import { WorkflowFormProps } from '@/components/MessageCenter';
import service, { JiraIssue } from '@/services/jira.service';
import styles from './index.less';

export default (props: WorkflowFormProps) => {
    const { link } = props;
    const [key] = link;

    const [issue, setIssue] = useState<JiraIssue>(null);

    const [loading, setLoading] = useState(false);

    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            try {
                const response = await service.getIssue(key);
                setIssue(response.data);
            } catch {
                message.error('获取数据失败');
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [key]);

    const parseDate = (date: string) => {
        if (!date) return null;

        return new Date(Date.parse(date)).toLocaleString();
    };

    return (
        <Spin spinning={loading}>
            <div className={styles.container}>
                <div className={styles.body}>
                    <div className={styles.left}>
                        <div className={styles.header}>
                            <img
                                src={issue?.fields.project.avatarUrls['48x48']}
                            />
                            <div>
                                <div>
                                    {issue?.fields.project.name + ' / ' + key}
                                </div>
                                <h1 style={{ display: 'inline-block' }}>
                                    {issue?.fields.summary}
                                </h1>
                            </div>
                        </div>
                        <div className={styles.block}>
                            <strong>详情</strong>
                            <div className={styles['flex-wrap']}>
                                <div className={styles['item-left']}>
                                    <span>类型：</span>
                                    <span>
                                        <img
                                            src={
                                                issue?.fields.issuetype.iconUrl
                                            }
                                        />
                                        {issue?.fields.issuetype.name}
                                    </span>
                                </div>
                                <div className={styles['item-left']}>
                                    <span>状态：</span>
                                    <span>{issue?.fields.status.name}</span>
                                </div>
                                <div className={styles['item-left']}>
                                    <span>优先级：</span>
                                    <span>
                                        <img
                                            height="16px"
                                            width="16px"
                                            src={issue?.fields.priority.iconUrl}
                                        />
                                        {issue?.fields.priority.name}
                                    </span>
                                </div>
                                <div className={styles['item-left']}>
                                    <span>解决结果：</span>
                                    <span>
                                        {issue?.fields.resolution?.name ||
                                            '未解决'}
                                    </span>
                                </div>
                                <div className={styles['item-label']}>
                                    <span>标签：</span>
                                    {issue &&
                                        (issue.fields.labels.length == 0 ? (
                                            <span>无</span>
                                        ) : (
                                            <div
                                                className={styles['flex-wrap']}
                                            >
                                                {issue.fields.labels.map(
                                                    (label, index) => (
                                                        <div
                                                            key={index}
                                                            className={
                                                                styles.label
                                                            }
                                                        >
                                                            {label}
                                                        </div>
                                                    ),
                                                )}
                                            </div>
                                        ))}
                                </div>
                            </div>
                        </div>
                        <div className={styles.block}>
                            <strong>描述</strong>
                            <p style={{ marginTop: '5px' }}>
                                {issue?.fields.description || '无'}
                            </p>
                        </div>
                    </div>
                    <div className={styles.right}>
                        <div
                            className={styles.header}
                            style={{ alignItems: 'flex-end' }}
                        >
                            <a
                                style={{ textDecoration: 'underline' }}
                                href={`https://jira.yanfuinvest.com/browse/${key}`}
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                在Jira上查看
                            </a>
                        </div>
                        <div className={styles.block}>
                            <strong>人员</strong>
                            <div className={styles['flex-column']}>
                                <div className={styles['item-right']}>
                                    <span>经办人：</span>
                                    <span>
                                        <img
                                            height="24px"
                                            width="24px"
                                            src={
                                                issue?.fields.assignee
                                                    .avatarUrls['48x48']
                                            }
                                        />
                                        {issue?.fields.assignee.displayName}
                                    </span>
                                </div>
                                <div className={styles['item-right']}>
                                    <span>报告人：</span>
                                    <span>
                                        <img
                                            height="24px"
                                            width="24px"
                                            src={
                                                issue?.fields.reporter
                                                    .avatarUrls['48x48']
                                            }
                                        />
                                        {issue?.fields.reporter.displayName}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div className={styles.block}>
                            <strong>日期</strong>
                            <div className={styles['flex-column']}>
                                <div className={styles['item-right']}>
                                    <span>创建日期：</span>
                                    <span>
                                        {parseDate(issue?.fields.created)}
                                    </span>
                                </div>
                                <div className={styles['item-right']}>
                                    <span>已更新：</span>
                                    <span>
                                        {parseDate(issue?.fields.updated)}
                                    </span>
                                </div>
                                {issue?.fields.resolutiondate && (
                                    <div className={styles['item-right']}>
                                        <span>已解决：</span>
                                        <span>
                                            {parseDate(
                                                issue?.fields.resolutiondate,
                                            )}
                                        </span>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Spin>
    );
};
