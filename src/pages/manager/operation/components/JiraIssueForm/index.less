.container {
    width: 100%;
}

.header {
    display: flex;
    gap: 10px;
    height: 54px;
}

.body {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 20px;
}

.left {
    grid-column: ~'1 / 9';
}

.right {
    grid-column: ~'9 / 13';
}

.block {
    margin-top: 30px;
}

.item-label {
    display: flex;
    padding-top: 5px;

    :global(span):nth-of-type(1) {
        width: 150px;
        color: #6b778c;
    }
}

.item-left {
    display: flex;
    width: 50%;
    padding-top: 5px;

    :global(span):nth-of-type(1) {
        width: 150px;
        color: #6b778c;
    }

    :global(span):nth-of-type(2) {
        display: flex;
        gap: 2px;
        align-items: center;
    }
}

.item-right {
    display: flex;
    flex-direction: column;
    padding-top: 5px;

    :global(span):nth-of-type(1) {
        color: #6b778c;
    }

    :global(span):nth-of-type(2) {
        display: flex;
        gap: 5px;
        align-items: center;
    }

    :global(img) {
        border-radius: 50%;
    }
}

.label {
    margin: 3px 3px 0 0;
    padding: 2px 5px;
    font-size: 14px;
    line-height: 1;
    background-color: rgba(9, 30, 66, 0.08);
    border: 1px solid transparent;
    border-radius: 3px;
}

.flex-wrap {
    display: flex;
    flex-wrap: wrap;
}

.flex-column {
    display: flex;
    flex-direction: column;
}
