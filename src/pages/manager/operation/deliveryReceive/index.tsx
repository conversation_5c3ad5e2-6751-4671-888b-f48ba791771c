import HistoryList from '@/components/ControlledForm/HistoryList';
import { CrudTable, useCrudTableAction } from '@/components/CrudTable';
import { DrawerForm, FormTrigger } from '@/components/TriggerableForm';
import useAccount from '@/hooks/useAccount';
import useMessages from '@/hooks/useMessages';
import wfService from '@/services/worflow.service';
import { mapAntdTable } from '@/utils/fieldProperty';
import { HistoryOutlined, NodeIndexOutlined } from '@ant-design/icons';
import { Button, Drawer, Image, message, Modal, Tag } from 'antd';
import React, { useState } from 'react';
import { DeliveryReceive, deliveryReceiveFields } from '../models/DeliveryReceive';
import deliveryReceiveService from '../services/deliveryReceive.service';
import FormItems from './FormItems';

export default function() {
    const actionRef = useCrudTableAction<DeliveryReceive>();

    const msgContainer = useMessages.useContainer();

    const { useCurrentUser } = useAccount.useContainer();
    const currentUser = useCurrentUser();
    const [visitDiagramId, setVisitDiagramId] = useState<string>(null);
    const [visitHistoryId, setVisitHistoryId] = useState<string>(null);
    const [operating, setOperating] = useState(false);

    const columns = mapAntdTable<DeliveryReceive>(deliveryReceiveFields, [
        {
            key: 'state',
            title: '状态',
            render: (_, record) => {
                switch (record.state) {
                    case 'NOT_CLAIMED':
                        return <Tag>待认领</Tag>;
                    case 'CLAIMED':
                        return <Tag color="green">已认领</Tag>;
                    case 'FINISHED':
                        return <Tag color="blue">已完成</Tag>;
                }
            },
        },
        { dataIndex: 'date' },
        { dataIndex: 'no' },
        { dataIndex: 'name' },
        { dataIndex: 'initiatorName' },
        { dataIndex: 'claimUserName' },
        { dataIndex: 'description' },
        {
            key: 'detail',
            title: '详情',
            valueType: 'option',
            doNotExport: true,
            render: (_, record) => [
                record.open ? (
                    <a key="diagram" onClick={() => setVisitDiagramId(record.instanceId)}>
                        <NodeIndexOutlined />
                    </a>
                ) : null,
                record.open || record.processComplete ? (
                    <a key="history" onClick={() => setVisitHistoryId(record.instanceId)}>
                        <HistoryOutlined />
                    </a>
                ) : null,
            ],
        },
        {
            key: 'operation',
            title: '操作',
            doNotExport: true,
            render: (_, record) => {
                if (!record.claimed) {
                    return (
                        <Button
                            disabled={operating}
                            onClick={async () => {
                                setOperating(true);
                                try {
                                    await deliveryReceiveService.complete(record.id);
                                    actionRef.update(record.id, {
                                        ...record,
                                        claimed: true,
                                        claimUserId: currentUser?.id,
                                        claimUserName: currentUser?.realName || currentUser?.username,
                                        state: 'CLAIMED',
                                    });
                                    msgContainer.refresh();
                                    message.success('认领成功');
                                } catch (e) {
                                    message.error('认领失败');
                                } finally {
                                    setOperating(false);
                                }
                            }}
                        >
                            认领
                        </Button>
                    );
                }
                if (!record.checkClaimed && record.initiatorId === currentUser?.id) {
                    return (
                        <Button
                            disabled={operating}
                            onClick={async () => {
                                setOperating(true);
                                try {
                                    await deliveryReceiveService.complete(record.id);
                                    actionRef.update(record.id, {
                                        ...record,
                                        checkClaimed: true,
                                    });
                                    msgContainer.refresh();
                                    message.success('确认认领成功');
                                } catch (e) {
                                    message.error('确认认领失败');
                                } finally {
                                    setOperating(false);
                                }
                            }}
                        >
                            确认认领
                        </Button>
                    );
                }
                if (record.open && record.checkClaimed && record.claimUserId === currentUser?.id) {
                    return (
                        <Button
                            disabled={operating}
                            onClick={async () => {
                                setOperating(true);
                                try {
                                    await deliveryReceiveService.complete(record.id);
                                    actionRef.update(record.id, {
                                        ...record,
                                        processComplete: true,
                                        open: false,
                                        state: 'FINISHED',
                                    });
                                    msgContainer.refresh();
                                    message.success('完成认领成功');
                                } catch {
                                    message.error('完成认领失败');
                                } finally {
                                    setOperating(false);
                                }
                            }}
                        >
                            完成认领
                        </Button>
                    );
                }
            },
        },
    ]);

    return (
        <DrawerForm
            name="deliveryReceiveForm"
            createTitle="接收快递"
            editTitle="修改快递"
            formContents={<FormItems />}
            width={500}
            labelWidth={80}
            onCreate={deliveryReceiveService.startProcess}
            afterCreate={data => actionRef.create(data)}
        >
            <CrudTable
                loading={operating}
                fields={deliveryReceiveFields}
                actionRef={actionRef}
                title="快递接收"
                service={{
                    getList: async () => {
                        const response = await deliveryReceiveService.getList();
                        return {
                            ...response,
                            data: [...response.data.sort((a, b) => b.createTime.diff(a.createTime))],
                        };
                    },
                }}
                recordInsertAt="first"
                search={false}
                rowKey="id"
                columns={columns}
                // toolBarRender={() => [
                //     <FormTrigger key="create">
                //         <Button type="primary">接收快递</Button>
                //     </FormTrigger>,
                // ]}
                simpleSearches={
                    <>
                        {deliveryReceiveFields.searchItems.state({})}
                        {deliveryReceiveFields.searchItems.date({})}
                        {deliveryReceiveFields.searchItems.no({})}
                        {deliveryReceiveFields.searchItems.initiatorName({})}
                        {deliveryReceiveFields.searchItems.claimUserName({})}
                    </>
                }
                newVersion
            />
            <Drawer
                visible={visitHistoryId != null}
                onClose={() => setVisitHistoryId(null)}
                title="流程记录"
                width={360}
            >
                {visitHistoryId && <HistoryList processId={visitHistoryId} />}
            </Drawer>
            <Modal
                width={800}
                visible={visitDiagramId != null}
                onCancel={() => setVisitDiagramId(null)}
                onOk={() => setVisitDiagramId(null)}
                title="流程图"
            >
                {visitDiagramId && <Image src={wfService.getDiagramUrl(visitDiagramId)} preview={false} />}
            </Modal>
        </DrawerForm>
    );
}
