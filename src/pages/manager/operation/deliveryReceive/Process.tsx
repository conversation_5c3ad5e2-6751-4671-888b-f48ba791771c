import React from 'react';
import FormVerifier from '@/components/FormVerifier';
import { singleBranchSubmit, WfWorkingFormOperation } from '@/components/ControlledForm';
import { getWorkflowForm, InnerWorkflowProps } from '@/components/MessageCenter';
import { FormItemSetter, MyForm } from '@/components/MyForm';
import service from '../services/deliveryReceive.service';
import FormItems from './FormItems';
import { deliveryReceiveFields } from '../models/DeliveryReceive';

const verifyTextTransform = {
    claim: '认领',
    checkClaimed: '确认认领',
    confirmClaim: '完成认领',
};

function WorkflowForm(props: InnerWorkflowProps) {
    const { taskId, step, title, afterSubmit, hideTitle, onCancel } = props;
    const { formItems } = deliveryReceiveFields;

    return (
        <MyForm name="deliveryReceiveProcessingForm">
            {!hideTitle && <h2>{title}</h2>}
            <FormItemSetter readonly>
                <FormItems />
                {step == 'checkClaimed' && formItems.claimUserName()}
            </FormItemSetter>
            <FormVerifier verifyText={verifyTextTransform[step]} />
            <WfWorkingFormOperation taskId={taskId} afterSubmit={afterSubmit} onCancel={onCancel}>
                {singleBranchSubmit()}
            </WfWorkingFormOperation>
        </MyForm>
    );
}

export default getWorkflowForm(async taskId => {
    const req = await service.findByTaskId(taskId);
    return req.data;
}, WorkflowForm);
