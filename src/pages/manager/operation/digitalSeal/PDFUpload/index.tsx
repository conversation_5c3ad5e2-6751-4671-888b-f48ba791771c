import { InboxOutlined } from '@ant-design/icons';
import { Upload } from 'antd';
import { RcFile } from 'antd/lib/upload';
import React from 'react';

const { Dragger } = Upload;

export default ({ onChange }: { onChange?: (pdf: Uint8Array, name: string) => void | Promise<void> }) => {
    const beforeUpload = async (file: RcFile) => {
        const arrayBuffer = await file.arrayBuffer();
        onChange?.(new Uint8Array(arrayBuffer), file.name);
        return false;
    };

    return (
        <div style={{ height: '100%', display: 'flex', alignItems: 'center' }}>
            <div style={{ width: '40%', height: '20%', margin: '0 auto' }}>
                <Dragger accept=".pdf" beforeUpload={beforeUpload} fileList={[]}>
                    <p className="ant-upload-drag-icon">
                        <InboxOutlined />
                    </p>
                    <p className="ant-upload-text">点击或拖拽文件到此区域</p>
                    <p className="ant-upload-hint">支持PDF文件</p>
                </Dragger>
            </div>
        </div>
    );
};
