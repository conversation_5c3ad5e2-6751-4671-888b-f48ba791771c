import { MyFormItem, useParentForm } from '@/components/MyForm';
import { FileUpload } from '@/models/utils/FileUpload';
import fileService from '@/services/file.service';
import { DeleteOutlined, PaperClipOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Select, Upload } from 'antd';
import React, { useRef, useState } from 'react';
import { digitalSealFields } from '../models/DigitalSeal';
import { DigitalSealConfig } from './SealConfig/DigitalSealConfig';

export default ({ sealOptions }: { sealOptions: DigitalSealConfig[] }) => {
    const { formItems } = digitalSealFields;

    const filesRef = useRef<FileUpload[]>([]);
    const [files, setFiles] = useState<FileUpload[]>([]);
    const form = useParentForm();

    const mutateFiles = (files: FileUpload[]) => {
        filesRef.current = files;
        setFiles([...filesRef.current]);
        form.setFields([{ name: 'originalFile', value: filesRef.current.map(file => file.hash).join(',') }]);
    };

    const addFile = (file: FileUpload) => {
        mutateFiles([...filesRef.current, file]);
    };

    const removeFile = (file: FileUpload) => {
        const index = filesRef.current.findIndex(({ hash }) => hash === file.hash);
        mutateFiles([...filesRef.current.slice(0, index), ...filesRef.current.slice(index + 1)]);
    };

    return (
        <>
            <MyFormItem name="sealFiles" label="电子印" rules={[{ required: true }]}>
                <Select options={sealOptions?.map(it => ({ label: it.name, value: it.value }))} mode="multiple" />
            </MyFormItem>
            <MyFormItem
                name="originalFile"
                label="要用印的文件"
                rules={[{ required: true, message: '请上传要用印的文件' }]}
            >
                <Upload
                    multiple
                    accept="application/pdf"
                    showUploadList={false}
                    beforeUpload={async rcFile => {
                        const response = await fileService.uploadFile(new Blob([rcFile]), rcFile.name);
                        addFile(response.data);
                    }}
                >
                    <Button icon={<UploadOutlined />}>点击上传</Button>
                </Upload>
                {files.map((file, index) => (
                    <div key={index} style={{ marginTop: 8, display: 'flex', justifyContent: 'space-between' }}>
                        <div>
                            <span style={{ color: '#00000073' }}>
                                <PaperClipOutlined />
                            </span>
                            <a
                                style={{ marginLeft: 8 }}
                                download={file.filename}
                                href={fileService.downloadUrl(file.url)}
                            >
                                {file.filename}
                            </a>
                        </div>
                        <div>
                            <DeleteOutlined style={{ cursor: 'pointer' }} onClick={() => removeFile(file)} />
                        </div>
                    </div>
                ))}
            </MyFormItem>
            {formItems.assigneeId()}
            {formItems.comment()}
        </>
    );
};
