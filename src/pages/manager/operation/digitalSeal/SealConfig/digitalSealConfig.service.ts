import { CustomResponse } from '@/models/common/CustomResponse';
import { DataDict } from '@/models/utils/DataDict';
import { Convert } from '@/utils/io';
import service from '@/utils/service';
import { DigitalSealConfig } from './DigitalSealConfig';

const prefix = '/operation/digital_seal_config';
const testprefix = '/feishu';
class DigitalSealConfigService {
    @Convert(DigitalSealConfig)
    getList() {
        return service.get<CustomResponse<DataDict<string>[]>>(prefix);
    }

    @Convert(DigitalSealConfig)
    getReadonlyList() {
        return service.get<CustomResponse<DataDict<string>[]>>(`${prefix}/readonly_list`);
    }

    @Convert(DigitalSealConfig)
    create(data: DataDict<string>) {
        return service.post<CustomResponse<DataDict<string>>>(prefix, { data });
    }

    @Convert(DigitalSealConfig)
    update(id: number, data: DataDict<string>) {
        return service.put<CustomResponse<DataDict<string>>>(prefix + `/${id}`, { data });
    }

    delete(id: number) {
        return service.delete<CustomResponse<null>>(prefix + `/${id}`);
    }

    // testData(id:number){
    //     return service.get<CustomResponse<null>>(`${testprefix}/get_feishu_approval_list/${id}`)
    // }
}

export default new DigitalSealConfigService();
