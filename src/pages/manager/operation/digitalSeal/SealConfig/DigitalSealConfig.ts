import { DataDict } from '@/models/utils/DataDict';
import { FieldTitle, getFields, Validate } from '@/utils/fieldProperty';
import * as Field from '@/utils/fieldUtils';
import { Convertor } from '@/utils/io';

@Convertor
export class DigitalSealConfig implements DataDict<string> {
    id?: number;
    field: string;

    @FieldTitle('名称')
    @Field.Text()
    @Validate({ required: true })
    name: string;
    key: string;

    @FieldTitle('文件')
    @Field.File({ async: true })
    @Validate({ required: true })
    value: string;
}

export const digitalSealConfigFields = getFields(DigitalSealConfig);
