import { CrudTable, DeleteWrapper, useCrudTableAction } from '@/components/CrudTable';
import { DrawerForm, FormTrigger } from '@/components/TriggerableForm';
import { mapAntdTable } from '@/utils/fieldProperty';
import { Button } from 'antd';
import React from 'react';
import { DigitalSealConfig, digitalSealConfigFields } from './DigitalSealConfig';
import service from './digitalSealConfig.service';

export default () => {
    const actionRef = useCrudTableAction<DigitalSealConfig>();
    const columns = mapAntdTable(digitalSealConfigFields, [
        { dataIndex: 'name' },
        { dataIndex: 'value' },
        {
            title: '操作',
            key: 'option',
            valueType: 'option',
            width: 100,
            fixed: 'right',
            render: (_, record) => [
                <FormTrigger formName="digitalSealConfigForm" key="edit" id={record.id} data={record}>
                    <a>编辑</a>
                </FormTrigger>,
                <DeleteWrapper key="delete" id={record.id} confirm={`是否确认删除？`}>
                    <a style={{ color: 'red' }} href="#">
                        删除
                    </a>
                </DeleteWrapper>,
            ],
        },
    ]);

    return (
        <DrawerForm<DigitalSealConfig>
            name="digitalSealConfigForm"
            createTitle="创建"
            editTitle="编辑"
            width={600}
            onCreate={service.create}
            onUpdate={service.update}
            afterCreate={actionRef.create}
            afterUpdate={data => actionRef.update(data.id, data)}
            formContents={<FormItems />}
        >
            <CrudTable<DigitalSealConfig>
                title="印章"
                service={service}
                fields={digitalSealConfigFields}
                recordInsertAt="last"
                actionRef={actionRef}
                columns={columns}
                search={false}
                toolBarRender={() => [
                    <FormTrigger key="create">
                        <Button type="primary">创建</Button>
                    </FormTrigger>,
                ]}
                rowKey="id"
                newVersion
            ></CrudTable>
        </DrawerForm>
    );
};

const FormItems = () => {
    const { formItems } = digitalSealConfigFields;

    return (
        <>
            {formItems.name()}
            {formItems.value()}
        </>
    );
};
