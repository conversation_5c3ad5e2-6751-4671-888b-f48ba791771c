import { CrudTable, useCrudTableAction } from '@/components/CrudTable';
import AsyncFileDisplay from '@/components/MyUpload/AsyncFileDisplay';
import { DrawerForm, FormTrigger } from '@/components/TriggerableForm';
import conf from '@/conf';
import useAccount from '@/hooks/useAccount';
import useMessages from '@/hooks/useMessages';
import { mapAntdTable } from '@/utils/fieldProperty';
import { useRequest } from 'ahooks';
import { Button, Popover, Tag, Form, Select } from 'antd';
import { keyBy } from 'lodash';
import React, { useState } from 'react';
import { history } from 'umi';
import { DigitalSeal, digitalSealFields, digitalSealStateEnum } from '../models/DigitalSeal';
import service from '../services/digitalSeal.service';
import FormItems from './FormItems';
import digitalSealConfigService from './SealConfig/digitalSealConfig.service';
import { useReadonlyConfigList } from './queries';

const tagColor = {
    PROCESSING: 'blue',
    APPROVED: 'green',
    REJECTED: 'red',
};

export default () => {
    const { useCurrentUser } = useAccount.useContainer();
    const currentUser = useCurrentUser();
    const [openModal, setOpenModal] = useState<boolean>(false);

    // const testData = useRequest(digitalSealConfigService.testData(currentUser?.id))
    const configListReq = useReadonlyConfigList();
    const configList = configListReq.data;

    const { todos } = useMessages.useContainer();
    const todoDict = keyBy(todos, 'instanceId');

    const sealReq = useRequest(digitalSealConfigService.getReadonlyList);
    const actionRef = useCrudTableAction<DigitalSeal>();
    const columns = mapAntdTable(digitalSealFields, [
        {
            dataIndex: 'state',
            render: (_, { state }) => <Tag color={tagColor[state]}>{digitalSealStateEnum[state]}</Tag>,
        },
        { dataIndex: 'createTime' },
        { dataIndex: 'initiatorName' },
        { dataIndex: 'assigneeName' },
        {
            dataIndex: 'sealFiles',
            render: (_, { sealFiles, assigneeId, initiatorId }) => {
                if (currentUser?.id === assigneeId) {
                    return (
                        <AsyncFileDisplay
                            value={sealFiles.join(',')}
                            filenameRender={(dom, hash) => configList?.find(it => it.value === hash)?.name || dom}
                        />
                    );
                }

                if (currentUser?.id === initiatorId) {
                    return sealFiles
                        .map(sealFile => sealReq.data?.data.find(seal => seal.value === sealFile)?.name)
                        .join(',');
                }

                return '-';
            },
        },
        {
            dataIndex: 'originalFile',
            render: (_, { originalFile }) => {
                const originalFiles = originalFile.split(',');
                if (originalFiles.length === 1) return <AsyncFileDisplay value={originalFile} />;

                return (
                    <Popover
                        trigger="hover"
                        content={
                            <>
                                {originalFiles.map(file => (
                                    <div style={{ padding: '5px 0' }}>
                                        <AsyncFileDisplay value={file} />
                                    </div>
                                ))}
                            </>
                        }
                    >
                        <span style={{ color: '#1890ff', cursor: 'pointer' }}>多个文件...</span>
                    </Popover>
                );
            },
        },
        {
            dataIndex: 'sealedFile',
            render: (_, { sealedFile }) => {
                const sealedFiles = sealedFile.split(',');
                if (sealedFiles.length === 1) return <AsyncFileDisplay value={sealedFile} />;

                return (
                    <Popover
                        trigger="hover"
                        content={
                            <>
                                {sealedFiles.map(file => (
                                    <div style={{ padding: '5px 0' }}>
                                        <AsyncFileDisplay value={file} />
                                    </div>
                                ))}
                            </>
                        }
                    >
                        <span style={{ color: '#1890ff', cursor: 'pointer' }}>多个文件...</span>
                    </Popover>
                );
            },
        },
        { dataIndex: 'comment' },
        {
            title: '操作',
            render: (_, entity) => {
                if (entity.state === 'APPROVED' && currentUser?.id === entity.initiatorId) {
                    return (
                        <a
                            onClick={() => {
                                console.log(entity, 'see EneityData');

                                if (navigator.userAgent.includes('Lark')) {
                                    history.replace('/digital_seal_editor', {
                                        id: entity.id,
                                        pdfs: entity.originalFile.split(','),
                                        seals: entity.sealFiles,
                                        options: ['1'],
                                    });
                                } else {
                                    const newWindow = window.open('/digital_seal_editor');
                                    if (!newWindow) return;

                                    newWindow.onload = () => {
                                        newWindow.postMessage(
                                            {
                                                message: 'pdf',
                                                payload: {
                                                    id: entity.id,
                                                    pdfs: entity.originalFile.split(','),
                                                    seals: entity.sealFiles,
                                                    options: ['2'],
                                                },
                                            },
                                            conf.origin,
                                        );
                                    };
                                }
                            }}
                        >
                            使用印章
                        </a>
                    );
                }

                if (entity.state === 'PROCESSING' && currentUser?.id === entity.assigneeId) {
                    return (
                        <a onClick={() => history.push('/message_center/' + todoDict[entity.instanceId].id)}>审批</a>
                    );
                }

                return '-';
            },
        },
    ]);

    return (
        <DrawerForm
            name="digitalSealForm"
            createTitle="申请用印"
            editTitle="编辑"
            formContents={<FormItems sealOptions={sealReq.data?.data} />}
            width={500}
            labelWidth={110}
            onCreate={service.startProcess}
            afterCreate={actionRef.create}
        >
            <CrudTable
                title="电子用印"
                rowKey="id"
                columns={columns}
                actionRef={actionRef}
                search={false}
                service={{
                    getList: async () => {
                        const response = await service.getList();
                        response.data.sort((a, b) => b.createTime.diff(a.createTime));
                        return response;
                    },
                }}
                toolBarRender={() => [
                    <>
                        <FormTrigger key="create">
                            <Button type="primary">发起申请</Button>
                        </FormTrigger>
                        ,
                    </>,
                ]}
                recordInsertAt="first"
                scroll={{ x: 'max-content' }}
                newVersion
            />
        </DrawerForm>
    );
};
