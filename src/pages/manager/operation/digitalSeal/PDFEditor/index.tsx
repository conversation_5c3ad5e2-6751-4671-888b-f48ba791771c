import fileService from '@/services/file.service';
import {
    AuditOutlined,
    ClearOutlined,
    DownloadOutlined,
    FileExcelOutlined,
    LayoutOutlined,
    QuestionCircleFilled,
    SnippetsOutlined,
} from '@ant-design/icons';
import { Button, Divider, Row, Spin, Tooltip } from 'antd';
import moment from 'dayjs';
import { values } from 'lodash';
import React, { useRef, useState } from 'react';
import { createUseStyles } from 'react-jss';
import SimpleBar from 'simplebar-react';
import 'simplebar-react/dist/simplebar.min.css';
import service from '../../services/digitalSeal.service';
import { getPDFPageNumbers } from '../utils';
import styles from './index.less';
import './simpleBar.less';
import { usePdf } from './usePDF';

const scale = 1;

const useStyles = createUseStyles({
    main: {
        height: '100%',
        width: 'calc(100% - 600px)',
        backgroundColor: '#535659',
        padding: 20,

        '& .simplebar-content': {
            display: 'flex',
            flexDirection: ({ flexDirection }: React.CSSProperties) => flexDirection,
            height: ({ flexDirection }: React.CSSProperties) => (flexDirection === 'row' ? '100%' : undefined),
            alignItems: 'center',
            gap: 10,
        },
    },
});

export default () => {
    const canvasDictRef = useRef<Record<number | string, HTMLCanvasElement>>({});
    const anchorRef = useRef<HTMLAnchorElement>(null);

    const [flexDirection, setFlexDirection] = useState<'row' | 'column'>('column');
    const [downloading, setDownloading] = useState(false);

    const innerStyles = useStyles({ flexDirection });

    const {
        imageUrls,
        selectedPageNumber,
        pdfDocument,
        selectedImageUrl,
        files,
        selectedFile,
        addImage,
        save,
        applyToAllPages,
        applyPagingSeal,
        selectPage,
        reset,
        resetAll,
        getId,
        selectImageUrl,
        changeFile,
    } = usePdf({
        scale,
        canvasDictRef,
    });

    const disableOperation = !selectedImageUrl || !pdfDocument;

    const download = async () => {
        const { current: anchor } = anchorRef;
        if (!anchor) return;

        setDownloading(true);

        try {
            const data = await save();
            if (!data) return;

            const sealedName = `${moment().format('YYYYMMDDHHmmss')}_sealed_${selectedFile?.filename}`;
            const blob = new Blob([data]);
            const uploadResult = await fileService.uploadFile(blob, sealedName);
            await service.seal(getId(), selectedFile.hash, uploadResult.data.hash);

            const url = URL.createObjectURL(blob);
            anchor.href = url;
            anchor.download = sealedName;
            anchor.click();
        } finally {
            setDownloading(false);
        }
    };

    const switchFlexDirection = () => {
        switch (flexDirection) {
            case 'column':
                setFlexDirection('row');
                break;
            case 'row':
                setFlexDirection('column');
                break;
        }
    };
    console.log(getPDFPageNumbers(pdfDocument), 'seeData');

    return (
        <div className={styles.wrapper}>
            <div className={styles.toolbar}>
                <Tooltip overlay="应用选择页面改动到所有页面">
                    <Button
                        icon={<SnippetsOutlined />}
                        type="link"
                        onClick={applyToAllPages}
                        disabled={disableOperation}
                    />
                </Tooltip>
                <Tooltip overlay="添加骑缝章">
                    <Button
                        icon={<AuditOutlined />}
                        type="link"
                        onClick={() => {
                            applyPagingSeal();
                            setFlexDirection('row');
                        }}
                        disabled={disableOperation}
                    />
                </Tooltip>
                <Tooltip overlay="改变为横/纵向布局">
                    <Button
                        icon={<LayoutOutlined />}
                        type="link"
                        onClick={switchFlexDirection}
                        disabled={!pdfDocument}
                    />
                </Tooltip>
                <Divider type="vertical" style={{ borderColor: '#ffffffd9' }} />
                <Tooltip overlay="清除选择页面改动">
                    <Button
                        icon={<FileExcelOutlined />}
                        type="link"
                        onClick={() => reset(selectedPageNumber)}
                        disabled={disableOperation}
                    />
                </Tooltip>
                <Tooltip overlay="清除所有页面改动">
                    <Button icon={<ClearOutlined />} type="link" onClick={resetAll} disabled={disableOperation} />
                </Tooltip>

                <Divider type="vertical" style={{ borderColor: '#ffffffd9' }} />
                <Tooltip overlay="保存并下载">
                    <Button icon={<DownloadOutlined />} type="link" onClick={download} disabled={!pdfDocument} />
                </Tooltip>
            </div>
            <div className={styles.body}>
                <div style={{ width: 300, backgroundColor: '#323639', padding: 24, overflow: 'auto' }}>
                    {values(files).map((file, index) => (
                        <div key={index} style={{ padding: '5px 0' }}>
                            <a
                                onClick={() => changeFile(file)}
                                className={styles.fileItem}
                                style={{ color: file.hash === selectedFile?.hash ? undefined : '#ffffffd9' }}
                            >
                                {file.filename}
                            </a>
                        </div>
                    ))}
                </div>
                <SimpleBar className={innerStyles.main} autoHide={false}>
                    {getPDFPageNumbers(pdfDocument).map(pageNumber => (
                        <div
                            key={pageNumber}
                            style={{
                                position: 'relative',
                                outline: pageNumber === selectedPageNumber ? '4px solid #448ef7' : undefined,
                            }}
                            onMouseDown={() => selectPage(pageNumber)}
                        >
                            <canvas
                                className={styles.canvas}
                                ref={dom => dom && (canvasDictRef.current[pageNumber] = dom)}
                            />
                        </div>
                    ))}
                    {flexDirection === 'row' && (
                        <div>
                            <canvas width={20} />
                        </div>
                    )}
                </SimpleBar>
                <div className={styles.nav}>
                    <Row style={{ color: '#ffffffd9', fontSize: 20, marginBottom: 12 }} justify="center">
                        <Tooltip
                            overlay={
                                <div>
                                    <div style={{ marginBottom: 12 }}>点击左侧文件列表中项目，可选择文件。</div>
                                    <div style={{ marginBottom: 12 }}>点击中间文档，可选择页面。</div>
                                    <div style={{ marginBottom: 12 }}>双击下方图片，可直接添加印章到所选页面。</div>
                                    <div>
                                        点击下方图片，选择想要使用的印章，再点击顶栏“添加骑缝章”图标，即可添加骑缝章。
                                    </div>
                                </div>
                            }
                        >
                            <QuestionCircleFilled />
                        </Tooltip>
                    </Row>
                    {imageUrls?.map((url, index) => (
                        <div
                            key={index}
                            className={styles.imgContainer}
                            style={{
                                marginBottom: 24,
                                border: selectedImageUrl === url ? '2px solid #448ef7' : undefined,
                            }}
                        >
                            <div
                                className={styles.img}
                                style={{ backgroundImage: `url(${url})` }}
                                onClick={() => selectImageUrl(url)}
                                onDoubleClick={() => addImage(url)}
                            />
                        </div>
                    ))}
                </div>
            </div>
            <a ref={anchorRef} style={{ display: 'none' }}></a>
            {downloading && (
                <div
                    style={{
                        background: '#00000090',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        position: 'absolute',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}
                >
                    <Spin />
                </div>
            )}
        </div>
    );
};
