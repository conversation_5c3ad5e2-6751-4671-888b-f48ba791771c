import conf from '@/conf';
import { FileUpload } from '@/models/utils/FileUpload';
import fileService from '@/services/file.service';
import { fabric } from 'fabric';
import { entries, first, keyBy, keys, mean, values } from 'lodash';
import { PDFDocument, PDFPage } from 'pdf-lib';
import * as pdfjs from 'pdfjs-dist';
import { PDFDocumentProxy, PDFPageProxy } from 'pdfjs-dist';
import { RefObject, useEffect, useRef, useState } from 'react';
import { useLocation } from 'umi';
import { getPDFPageNumbers } from '../utils';
import styles from './index.less';

pdfjs.GlobalWorkerOptions.workerPort = new Worker(new URL('pdfjs-dist/build/pdf.worker.js', import.meta.url));

export interface UsePdfProps {
    scale?: number;
    canvasDictRef: RefObject<Record<number | string, HTMLCanvasElement>>;
}

interface DigitalSealMessageBody {
    id: number;
    pdfs: string[];
    name: string;
    seals: string[];
}

export const usePdf = (options: UsePdfProps) => {
    const { scale, canvasDictRef } = { scale: 1, ...options };

    const location = useLocation();
    // console.log(location,'location');

    const [selectedPageNumber, setSelectedPageNumber] = useState(3);
    const [pdfDocument, setPdfDocument] = useState<PDFDocumentProxy>();
    const [pdfPageDict, setPdfPageDict] = useState<Record<number, PDFPageProxy>>({});

    const [imageUrls, setImageUrls] = useState<string[]>();
    const [selectedImageUrl, setSelectedImageUrl] = useState<string>();

    const [pdf, setPDF] = useState<Uint8Array>();

    const pdfPageAreaRef = useRef<number>();
    const fabricCanvasDictRef = useRef<Record<number | string, fabric.Canvas>>({});
    const fabricObjectDictRef = useRef<Record<number | string, fabric.Object[]>>({});

    const idRef = useRef<number>();

    const [files, setFiles] = useState<{ [key: string]: FileUpload }>({});
    const [selectedFile, setSelectedFile] = useState<FileUpload>();

    const cacheRef = useRef<{
        [hash: string]: {
            selectedPageNumber: typeof selectedPageNumber;
            pdfDocument: typeof pdfDocument;
            pdfPageDict: typeof pdfPageDict;
            pdf: typeof pdf;
            pdfPageArea: typeof pdfPageAreaRef.current;
            fabricObjectDict: typeof fabricObjectDictRef.current;
            scrollTop: number;
            scrollLeft: number;
        };
    }>({});

    const fetchPDF = async (hash: string) => {
        const pdfBuffer = await fetch(fileService.downloadUrl(hash)).then(response => response.arrayBuffer());
        const pdfTypedArray = new Uint8Array(pdfBuffer);
        const pdfDocument = await pdfjs.getDocument(pdfTypedArray).promise;
        setPDF(pdfTypedArray);
        setPdfDocument(pdfDocument);
    };

    const resetState = () => {
        setSelectedPageNumber(1);
        setPdfDocument(undefined);
        setPdfPageDict({});
        setPDF(undefined);

        pdfPageAreaRef.current = undefined;
        fabricObjectDictRef.current = {};
    };

    const changeFile = (file: FileUpload) => {
        const currentHash = selectedFile?.hash;

        if (currentHash) {
            const fabricObjectDict = {};

            keys(pdfPageDict).forEach(pageNumber => {
                const fabricCanvas = fabricCanvasDictRef.current[pageNumber];
                if (!fabricCanvas) return;

                fabricObjectDict[pageNumber] = fabricCanvas.getObjects();
            });

            const { scrollTop, scrollLeft } = getSimpleBarWrapper();

            cacheRef.current[currentHash] = {
                selectedPageNumber,
                pdfDocument,
                pdfPageDict,
                pdf,
                pdfPageArea: pdfPageAreaRef.current,
                fabricObjectDict,
                scrollTop,
                scrollLeft,
            };
        }

        const cache = cacheRef.current[file.hash];

        if (cache) {
            setSelectedPageNumber(cache.selectedPageNumber);
            setPdfDocument(cache.pdfDocument);
            setPdfPageDict(cache.pdfPageDict);
            setPDF(cache.pdf);

            pdfPageAreaRef.current = cache.pdfPageArea;
            fabricObjectDictRef.current = cache.fabricObjectDict;
        } else {
            resetState();
            fetchPDF(file.hash);
        }

        setSelectedFile(file);
    };

    useEffect(() => {
        const onMessage = ({ data, origin }: MessageEvent<{ message: string; payload: DigitalSealMessageBody }>) => {
            if (origin !== conf.origin || data.message !== 'pdf') return;

            const { id, pdfs, seals } = data.payload;

            const fetchFiles = async () => {
                const response = await fileService.findDetailByHashPost(pdfs);

                setFiles(keyBy(response.data, it => it.hash));
                changeFile(first(response.data));
            };

            const fetchSeals = async () => {
                const imageBuffers = await Promise.all(
                    seals.map(seal => fetch(fileService.downloadUrl(seal)).then(response => response.arrayBuffer())),
                );
                const imageUrls = imageBuffers.map(buffer => URL.createObjectURL(new Blob([new Uint8Array(buffer)])));
                setImageUrls(imageUrls);
                setSelectedImageUrl(first(imageUrls));
            };

            idRef.current = id;

            Promise.all([fetchFiles(), fetchSeals()]);
        };

        window.addEventListener('message', onMessage);

        if (location.state) {
            postMessage(
                {
                    message: 'pdf',
                    payload: location.state,
                },
                conf.origin,
            );
        }

        return () => {
            window.removeEventListener('message', onMessage);
        };
    }, []);

    useEffect(() => {
        const { current: canvasDict } = canvasDictRef;
        if (!pdfDocument || !canvasDict) return;

        const pdfPageAreaList: number[] = [];

        const promises = getPDFPageNumbers(pdfDocument).map(async pageNumber => {
            const canvas: HTMLCanvasElement | undefined = canvasDict[pageNumber];
            const canvasContext = canvas?.getContext('2d');

            if (!canvas || !canvasContext) return;

            const { devicePixelRatio: dpRatio } = window;
            const pdfPage = await pdfDocument.getPage(pageNumber);
            const viewport = pdfPage.getViewport({ scale: scale * dpRatio });
            const { height, width } = viewport;
            canvas.style.height = `${height / dpRatio}px`;
            canvas.style.width = `${width / dpRatio}px`;
            canvas.height = height;
            canvas.width = width;
            await pdfPage.render({ canvasContext, viewport }).promise;

            const renderedViewport = pdfPage.getViewport({ scale });
            const workCanvas = document.createElement('canvas');
            canvas.insertAdjacentElement('afterend', workCanvas);
            workCanvas.className = styles.canvas;
            workCanvas.height = renderedViewport.height;
            workCanvas.width = renderedViewport.width;
            const fabricCanvas = new fabric.Canvas(workCanvas);
            fabricCanvasDictRef.current[pageNumber] = fabricCanvas;

            const fabricObjects = fabricObjectDictRef.current[pageNumber];
            if (fabricObjects) fabricCanvas.add(...fabricObjects);

            pdfPageAreaList.push(renderedViewport.height * renderedViewport.width);

            return pdfPage;
        });

        Promise.all(promises).then(pdfPages => {
            const dict: typeof pdfPageDict = {};
            pdfPages.forEach(pdfPage => pdfPage && (dict[pdfPage.pageNumber] = pdfPage));
            pdfPageAreaRef.current = mean(pdfPageAreaList);
            setPdfPageDict(dict);

            const { scrollTop, scrollLeft } = cacheRef.current?.[selectedFile?.hash] || {};
            getSimpleBarWrapper().scrollTo({ top: scrollTop, left: scrollLeft });
        });

        return () => {
            values(fabricCanvasDictRef.current).forEach(fabricCanvas => fabricCanvas.clear());
            fabricCanvasDictRef.current = {};
            Array.from(document.getElementsByClassName('canvas-container')).forEach(workCanvas => workCanvas.remove());
        };
    }, [pdfDocument]);

    const selectPage = (pageNumber: number) => setSelectedPageNumber(pageNumber);

    const addImage = async (imageUrl: string) => {
        const fabricCanvas = fabricCanvasDictRef.current[selectedPageNumber];
        if (!imageUrl || !fabricCanvas) return;

        const fabricImage = await fabricImageFromUrl(imageUrl);
        fabricImage.set({
            lockScalingX: true,
            lockScalingY: true,
        });
        const fabricImageArea = (fabricImage.height || 0) * (fabricImage.width || 0);
        const { current: pdfPageArea } = pdfPageAreaRef;

        const fabricImageScale =
            pdfPageArea && fabricImageArea ? Math.sqrt(pdfPageArea / 35) / Math.sqrt(fabricImageArea) : 1;
        fabricImage.scale(fabricImageScale);
        fabricCanvas.add(fabricImage);
    };

    const save = async () => {
        const pdfDoc = await PDFDocument.load(pdf);

        for (const [pageNumber, fabricCanvas] of entries(fabricCanvasDictRef.current)) {
            if (!fabricCanvas.getObjects().length) continue;

            const pdfPage = pdfDoc.getPage(parseInt(pageNumber) - 1);
            const png = fabricCanvas.toDataURL({ format: 'png', multiplier: devicePixelRatio * 3 });
            const pdfImage = await pdfDoc.embedPng(png);

            const { width, height } = fabricCanvas;
            pdfPage.drawImage(pdfImage, getImagePosition(pdfPage, { x: 0, y: 0, width, height }, 1));
        }

        return await pdfDoc.save();
    };

    const applyToAllPages = () => {
        const currFabricCanvas: fabric.Canvas | undefined = fabricCanvasDictRef.current[selectedPageNumber];
        if (!currFabricCanvas) return;
        const currPageNumberStr = selectedPageNumber.toString();

        const promises = entries(fabricCanvasDictRef.current).map(async ([pageNumber, fabricCanvas]) => {
            if (pageNumber === currPageNumberStr) return;
            const fabricClones = await Promise.all(currFabricCanvas.getObjects().map(cloneFabricObject));
            fabricCanvas.add(...fabricClones);
        });

        Promise.all(promises);
    };

    const applyPagingSeal = async () => {
        if (!selectedImageUrl || !pdfDocument) return;

        const fabricImage = await fabricImageFromUrl(selectedImageUrl);
        const fabricImageArea = (fabricImage.height || 0) * (fabricImage.width || 0);
        const { current: pdfPageArea } = pdfPageAreaRef;
        const fabricImageScale =
            pdfPageArea && fabricImageArea ? Math.sqrt(pdfPageArea / 35) / Math.sqrt(fabricImageArea) : 1;
        const fabricImageHeight = (fabricImage.height || 0) * fabricImageScale;
        const fabricImageWidth = (fabricImage.width || 0) * fabricImageScale;
        const clipWidth = fabricImageWidth / pdfDocument.numPages;

        const angle = Math.random() * 360;

        const promises = entries(fabricCanvasDictRef.current).map(async ([pageNumberStr, fabricCanvas]) => {
            const pageNumber = parseInt(pageNumberStr);
            const fabricCanvasHeight = fabricCanvas.height || 0;
            const fabricCanvasWidth = fabricCanvas.width || 0;

            const fabricClone = await cloneFabricObject(fabricImage);
            fabricClone.lockRotation = true;
            fabricClone.lockScalingX = true;
            fabricClone.lockScalingY = true;
            fabricClone.lockMovementX = true;
            fabricClone.lockMovementY = true;
            fabricClone.centeredRotation = true;
            fabricClone.centeredScaling = true;

            fabricClone
                .rotate(angle)
                .scale(fabricImageScale)
                .setCoords();

            const topLeft = '0 0';
            const topRight = `${clipWidth} 0`;
            const bottomRight = `${clipWidth} ${fabricImageHeight}`;
            const bottomLeft = `0 ${fabricImageHeight}`;
            const path = `M ${topLeft} L ${topRight} L ${bottomRight} L ${bottomLeft} z`;

            fabricClone.clipPath = new fabric.Path(path, {
                absolutePositioned: true,
                left: fabricCanvas.width,
                top: fabricCanvasHeight / 2,
                originX: 'right',
                originY: 'center',
            });

            const x = fabricCanvasWidth + fabricImageWidth / 2 - clipWidth * pageNumber;
            const y = fabricCanvasHeight / 2;

            fabricClone.setPositionByOrigin(new fabric.Point(x, y), 'center', 'center');

            fabricCanvas.add(fabricClone);
        });

        Promise.all(promises);
    };

    const reset = (pageNumber: number) => fabricCanvasDictRef.current[pageNumber]?.clear();

    const resetAll = () => {
        for (const fabricCanvas of values(fabricCanvasDictRef.current)) {
            fabricCanvas.clear();
        }
    };

    const getId = () => idRef.current;

    const selectImageUrl = (imageUrl: string) => {
        setSelectedImageUrl(imageUrl);
    };

    return {
        imageUrls,
        pdfDocument,
        pdfPageDict,
        selectedPageNumber,
        selectedImageUrl,
        files,
        selectedFile,
        addImage,
        save,
        applyToAllPages,
        applyPagingSeal,
        selectPage,
        reset,
        resetAll,
        getId,
        selectImageUrl,
        changeFile,
    };
};

const getSimpleBarWrapper = () => first(document.getElementsByClassName('simplebar-content-wrapper'));

const cloneFabricObject = (fabricObject: fabric.Object) =>
    new Promise<fabric.Object>(resolve => fabricObject.clone(resolve));

const fabricImageFromUrl = (url: string) => new Promise<fabric.Image>(resolve => fabric.Image.fromURL(url, resolve));

const compensateRotation = (
    pageRotation: number,
    x: number,
    y: number,
    scale: number,
    dimensions: { width: number; height: number },
    fontSize: number,
) => {
    const rotationRads = (pageRotation * Math.PI) / 180;

    const coordsFromBottomLeft = {
        x: x / scale,
        y:
            pageRotation === 90 || pageRotation === 270
                ? dimensions.width - (y + fontSize) / scale
                : dimensions.height - (y + fontSize) / scale,
    };

    let drawX = null;
    let drawY = null;
    if (pageRotation === 90) {
        drawX =
            coordsFromBottomLeft.x * Math.cos(rotationRads) -
            coordsFromBottomLeft.y * Math.sin(rotationRads) +
            dimensions.width;
        drawY = coordsFromBottomLeft.x * Math.sin(rotationRads) + coordsFromBottomLeft.y * Math.cos(rotationRads);
    } else if (pageRotation === 180) {
        drawX =
            coordsFromBottomLeft.x * Math.cos(rotationRads) -
            coordsFromBottomLeft.y * Math.sin(rotationRads) +
            dimensions.width;
        drawY =
            coordsFromBottomLeft.x * Math.sin(rotationRads) +
            coordsFromBottomLeft.y * Math.cos(rotationRads) +
            dimensions.height;
    } else if (pageRotation === 270) {
        drawX = coordsFromBottomLeft.x * Math.cos(rotationRads) - coordsFromBottomLeft.y * Math.sin(rotationRads);
        drawY =
            coordsFromBottomLeft.x * Math.sin(rotationRads) +
            coordsFromBottomLeft.y * Math.cos(rotationRads) +
            dimensions.height;
    } else {
        drawX = coordsFromBottomLeft.x;
        drawY = coordsFromBottomLeft.y;
    }

    return { x: drawX, y: drawY };
};

const getImagePosition = (
    page: PDFPage,
    position: { x: number; y: number; width: number; height: number },
    sizeRatio: number,
) => {
    const pageWidth = [90, 270].includes(page.getRotation().angle) ? page.getHeight() : page.getWidth();
    const imagePosition = { ...position, vpWidth: pageWidth };

    const pageRatio = pageWidth / (imagePosition.vpWidth * sizeRatio);
    const imageWidth = imagePosition.width * sizeRatio * pageRatio;
    const imageHeight = imagePosition.height * sizeRatio * pageRatio;
    const imageX = imagePosition.x * sizeRatio * pageRatio;
    const imageYFromTop = imagePosition.y * sizeRatio * pageRatio;

    const correction = compensateRotation(
        page.getRotation().angle,
        imageX,
        imageYFromTop,
        1,
        page.getSize(),
        imageHeight,
    );

    return {
        width: imageWidth,
        height: imageHeight,
        x: correction.x,
        y: correction.y,
        rotate: page.getRotation(),
    };
};
