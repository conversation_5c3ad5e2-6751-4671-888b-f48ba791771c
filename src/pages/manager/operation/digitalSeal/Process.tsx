import { twoWaySubmit, WfWorkingFormOperation } from '@/components/ControlledForm';
import FormVerifier from '@/components/FormVerifier';
import { getWorkflowForm, InnerWorkflowProps } from '@/components/MessageCenter';
import { FormItemSetter, MyForm, useFormData } from '@/components/MyForm';
import AsyncFileDisplay from '@/components/MyUpload/AsyncFileDisplay';
import React from 'react';
import { DigitalSeal, digitalSealFields } from '../models/DigitalSeal';
import service from '../services/digitalSeal.service';

const workflowForm = (props: InnerWorkflowProps) => {
    const { taskId, title, afterSubmit, hideTitle, onCancel } = props;
    const { formItems } = digitalSealFields;

    return (
        <MyForm name="digitalSealProcessingForm">
            {!hideTitle && <h2>{title}</h2>}
            <FormItemSetter readonly>
                <SealFiles />
                {formItems.originalFile()}
                {formItems.comment()}
            </FormItemSetter>
            <FormVerifier verifyText="审核通过" />
            <WfWorkingFormOperation taskId={taskId} afterSubmit={afterSubmit} onCancel={onCancel}>
                {twoWaySubmit()}
            </WfWorkingFormOperation>
        </MyForm>
    );
};

const SealFiles = () => {
    const { data }: { data: DigitalSeal } = useFormData();

    return (
        <div style={{ display: 'flex', marginBottom: 24 }}>
            <div style={{ display: 'flex', justifyContent: 'flex-end', width: 124, marginRight: 8, marginTop: 5 }}>
                申请使用的印章<span style={{ marginLeft: 2, transform: 'translateY(-1.5px)' }}>:</span>
            </div>
            <div>{data?.sealFiles && <AsyncFileDisplay value={data.sealFiles.join(',')} />}</div>
        </div>
    );
};

export default getWorkflowForm(async taskId => {
    const req = await service.findByTaskId(taskId);
    return req.data;
}, workflowForm);
