import React from 'react';
import MessageCenter from '@/components/MessageCenter';
import useMessages from '@/hooks/useMessages';
import workflowForms from '@/pages/workflowForms';
import jiraIssueForms from '@/pages/jiraIssueForms';
import openDayMessage from '@/pages/openDayMessage';
import { history } from 'umi';

export default props => {
    const id = parseInt(props.match.params.id as string);
    const { todos, setProcessed } = useMessages.useContainer();
    const forms = { ...workflowForms, ...jiraIssueForms, ...openDayMessage };
    return (
        <MessageCenter
            messages={todos}
            forms={forms}
            id={isNaN(id) ? null : id}
            onClick={id => history.push(`/message_center/${id}`)}
            afterSubmit={id => {
                history.replace('/message_center');
                setProcessed(id);
            }}
        />
    );
};
