import { ModalForm } from '@ant-design/pro-form';
import { FormInstance } from 'antd';
import React, { useRef } from 'react';
import { GroupWriteDTO } from '../models/Group';
import { useCreateGroupMutation } from '../queries/group';
import GroupFormItems from './GroupFormItems';
import { createGroupDialog } from './store';

export default () => {
    const formRef = useRef<FormInstance<GroupWriteDTO>>();
    const { open } = createGroupDialog.use();

    const { trigger } = useCreateGroupMutation();

    const onFinish = async () => {
        const formData = formRef.current.getFieldsValue();
        await trigger({ data: formData });
        createGroupDialog.hide();
    };

    return (
        <ModalForm
            formRef={formRef}
            title="修改名称"
            visible={open}
            modalProps={{ onCancel: () => createGroupDialog.hide() }}
            onFinish={onFinish}
        >
            <GroupFormItems />
        </ModalForm>
    );
};
