import { <PERSON>ton, ButtonProps, <PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd';
import React, { useEffect, useRef } from 'react';
import { Permission } from '../permission';
import { PermissionActionRef } from '../permission/store';
import { useSetGroupPermissionMutation } from '../queries/group';
import { usePermissionQuery } from '../queries/permission';
import { setGroupPermissionsDialog } from './store';

export default () => {
    const { open, data } = setGroupPermissionsDialog.use();
    const actionRef = useRef<PermissionActionRef>();

    const { trigger, isMutating } = useSetGroupPermissionMutation();

    useEffect(() => {
        const action = actionRef.current;
        if (!action) return;

        action.setSelectedPermissions([]);

        if (!data || !open) return;
        action.setSelectedPermissions(data.permissions);
    }, [open]);

    const onFinish = async () => {
        const action = actionRef.current;
        if (!action) return;

        const selectedPermissions = action.getSelectedPermissions();
        const permissionIds = selectedPermissions.map(permission => permission.id);
        await trigger({ id: data.id, data: { permissionIds } });
        setGroupPermissionsDialog.hide();
    };

    return (
        <Drawer
            width="80vw"
            title="修改关联权限"
            open={open}
            onClose={() => setGroupPermissionsDialog.hide()}
            destroyOnClose
        >
            <Permission actionRef={actionRef} group={data} />
            <Row justify="end" gutter={[12, 0]} style={{ padding: '0 24px' }}>
                <Col>
                    <SubmitButton type="primary" onClick={onFinish} loading={isMutating}>
                        提交
                    </SubmitButton>
                </Col>
            </Row>
        </Drawer>
    );
};

const SubmitButton = (props: ButtonProps) => {
    const { isLoading } = usePermissionQuery();

    return (
        <Button {...props} disabled={isLoading}>
            提交
        </Button>
    );
};
