import CollapseItems from '@/components/CollapseItems';
import DropdownOperation from '@/components/DropdownOperation';
import { ProColumnType, ProTable } from '@ant-design/pro-components';
import { Button, Modal, Popover } from 'antd';
import { sortBy } from 'lodash';
import React from 'react';
import { Group } from '../models/Group';
import PermissionDescription from '../permission/PermissionDescription';
import { useDeleteGroupMutation, useGroupQuery } from '../queries/group';
import { createGroupDialog, setGroupPermissionsDialog, setGroupUsersDialog, updateGroupDialog } from './store';
import { useCreation } from 'ahooks';
import useTableFilter from '../hooks/useTableFilter';

export default () => {
    const groupQuery = useGroupQuery();
    const deleteMutation = useDeleteGroupMutation();

    const { filterColumns } = useTableFilter(groupQuery.data, columns);

    const operationColumn: ProColumnType<Group> = {
        key: 'operation',
        title: '操作',
        width: 44,
        render: (_, record) => {
            return (
                <DropdownOperation
                    menu={{
                        items: [
                            {
                                label: '修改名称',
                                key: 'editName',
                                onClick: () => updateGroupDialog.show({ data: record }),
                            },
                            {
                                label: '修改关联权限',
                                key: 'editPermissions',
                                onClick: () => setGroupPermissionsDialog.show({ data: record }),
                            },
                            {
                                label: '修改关联用户',
                                key: 'editUsers',
                                onClick: () => setGroupUsersDialog.show({ data: record }),
                            },
                            { type: 'divider' },
                            {
                                label: '删除',
                                key: 'delete',
                                style: { color: 'red' },
                                onClick: () =>
                                    Modal.confirm({
                                        title: '确定要删除吗？',
                                        onOk: () => deleteMutation.trigger({ id: record.id }),
                                    }),
                            },
                        ],
                    }}
                />
            );
        },
    };

    return (
        <ProTable
            headerTitle="用户组列表"
            loading={groupQuery.isLoading || groupQuery.isValidating}
            search={false}
            columns={filterColumns.concat([operationColumn])}
            dataSource={groupQuery.data}
            pagination={{ pageSize: 10 }}
            toolBarRender={() => [
                <Button type="primary" onClick={() => createGroupDialog.show()}>
                    新增
                </Button>,
            ]}
            rowKey="id"
        />
    );
};

const columns: ProColumnType<Group>[] = [
    { dataIndex: 'ownerName', title: '创建人' },
    {
        dataIndex: 'name',
        title: '用户组名称',
        sorter: (a, b) => (a.name || '')?.localeCompare(b?.name || ''),
        defaultSortOrder: 'ascend',
    },
    { dataIndex: 'description', title: '用户组描述' },
    {
        dataIndex: 'tags',
        title: '标签',
        render: (_, { tags }) => (tags?.length ? <CollapseItems items={tags} /> : '-'),
    },
    {
        dataIndex: 'permissions',
        title: '关联权限',
        render: (_, record) => {
            const { permissions } = record;
            if (!permissions?.length) return '-';

            return (
                <CollapseItems
                    items={sortBy(permissions, permission => permission.name).map((permission, index) => (
                        <Popover key={index} content={<PermissionDescription permissionId={permission.id} />}>
                            <a>{permission.name}</a>
                        </Popover>
                    ))}
                />
            );
        },
    },
    {
        dataIndex: 'users',
        title: '关联用户',
        render: (_, record) => {
            const { users } = record;
            if (!users?.length) return '-';

            return (
                <CollapseItems
                    items={sortBy(users, user => user.username).map((user, index) => (
                        <a key={index}>{user.realName || user.username}</a>
                    ))}
                />
            );
        },
    },
];
