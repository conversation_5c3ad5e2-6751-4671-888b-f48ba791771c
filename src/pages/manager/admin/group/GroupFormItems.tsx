import { ProFormSelect, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { Col, Row } from 'antd';
import React from 'react';
import { useGroupTagsQuery } from '../queries/group';
import group from '.';

export default () => {
    const groupTagsQuery = useGroupTagsQuery();
    const { data: groups } = groupTagsQuery;
    const targetOption = groups?.map(group => ({
        label: group.description,
        value: group.name,
    }));
    return (
        <Row>
            <Col span={24}>
                <ProFormText name="name" label="用户组名称" />
            </Col>
            <Col span={24}>
                <ProFormTextArea name="description" label="用户组描述" />
            </Col>
            <Col span={24}>
                <ProFormSelect name="tags" label="标签" mode="multiple" showSearch options={targetOption} />
            </Col>
        </Row>
    );
};
