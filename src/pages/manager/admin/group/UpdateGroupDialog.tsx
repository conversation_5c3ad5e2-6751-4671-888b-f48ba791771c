import { ModalForm } from '@ant-design/pro-form';
import { FormInstance } from 'antd';
import React, { useEffect, useRef } from 'react';
import { GroupWriteDTO } from '../models/Group';
import { useUpdateGroupMutation } from '../queries/group';
import GroupFormItems from './GroupFormItems';
import { updateGroupDialog } from './store';

export default () => {
    const formRef = useRef<FormInstance<GroupWriteDTO>>();
    const { open, data } = updateGroupDialog.use();

    const { trigger } = useUpdateGroupMutation();

    useEffect(() => {
        const form = formRef.current;
        if (!form) return;

        form.resetFields();
        if (!data || !open) return;

        form.setFieldsValue(data);
    }, [open]);

    const onFinish = async () => {
        const formData = formRef.current.getFieldsValue();
        await trigger({ id: data.id, data: formData });
        updateGroupDialog.hide();
    };

    return (
        <ModalForm
            formRef={formRef}
            title="修改名称"
            visible={open}
            modalProps={{ onCancel: () => updateGroupDialog.hide() }}
            onFinish={onFinish}
        >
            <GroupFormItems />
        </ModalForm>
    );
};
