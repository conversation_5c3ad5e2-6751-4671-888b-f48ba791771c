import { Col, Row } from 'antd';
import React from 'react';
import CreateGroupDialog from './CreateGroupDialog';
import GroupTable from './GroupTable';
import SetGroupPermissionsDialog from './SetGroupPermissionsDialog';
import SetGroupUsersDialog from './SetGroupUsersDialog';
import UpdateGroupDialog from './UpdateGroupDialog';

const Group = () => {
    return (
        <Row gutter={[12, 0]}>
            <Col span={24}>
                <GroupTable />
            </Col>
        </Row>
    );
};

export default () => {
    return (
        <React.Fragment>
            <Group />
            <CreateGroupDialog />
            <UpdateGroupDialog />
            <SetGroupPermissionsDialog />
            <SetGroupUsersDialog />
        </React.Fragment>
    );
};
