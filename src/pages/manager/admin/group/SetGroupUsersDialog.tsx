import { ModalForm, ProFormSelect } from '@ant-design/pro-form';
import { Col, FormInstance, Row } from 'antd';
import React, { useEffect, useRef } from 'react';
import { useUserQuery } from '../queries/user';
import { setGroupUsersDialog } from './store';
import { useSetGroupUsersMutation } from '../queries/group';
import { GroupUserWriteDTO } from '../models/Group';
import sortBy from 'lodash/sortBy';

export default () => {
    const fomrRef = useRef<FormInstance<GroupUserWriteDTO>>();
    const { open, data } = setGroupUsersDialog.use();
    const userQuery = useUserQuery();

    const options = sortBy(
        userQuery.data?.map(user => ({ label: user.realName || user.username, value: user.id })),
        it => it.label,
    );

    const { trigger } = useSetGroupUsersMutation();

    useEffect(() => {
        const form = fomrRef.current;
        if (!form) return;

        form.resetFields();

        if (!open || !data) return;
        form.setFields([{ name: 'userIds', value: data.users?.map(user => user.id) }]);
    }, [open]);

    const onFinish = async () => {
        const form = fomrRef.current;
        if (!form) return;

        const formData = form.getFieldsValue();
        await trigger({ id: data.id, data: formData });
        setGroupUsersDialog.hide();
    };

    return (
        <ModalForm
            formRef={fomrRef}
            visible={open}
            title="修改关联用户"
            onFinish={onFinish}
            modalProps={{ onCancel: () => setGroupUsersDialog.hide() }}
        >
            <Row>
                <Col span={24}>
                    <ProFormSelect showSearch name="userIds" label="关联用户" mode="multiple" options={options} />
                </Col>
            </Row>
        </ModalForm>
    );
};
