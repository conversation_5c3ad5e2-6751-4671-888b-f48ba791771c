import { CustomResponse } from '@/models/common/CustomResponse';
import service from '@/utils/service';
import { Permission, PermissionEndPointWriteDTO, PermissionWriteDTO } from '../models/Permission';

const prefix = '/account/permission';

class PermissionService {
    getList() {
        return service.get<CustomResponse<Permission[]>>(prefix);
    }

    get(id: number) {
        return service.get<CustomResponse<Permission>>(`${prefix}/${id}`);
    }

    create(data: PermissionWriteDTO) {
        return service.post<CustomResponse<Permission>>(prefix, { data });
    }

    update(id: number, data: PermissionWriteDTO) {
        return service.put<CustomResponse<Permission>>(`${prefix}/${id}`, { data });
    }

    setEndPoints(id: number, data: PermissionEndPointWriteDTO) {
        return service.put<CustomResponse<Permission>>(`${prefix}/${id}/end_points`, { data });
    }

    delete(id: number) {
        return service.delete<CustomResponse<null>>(`${prefix}/${id}`);
    }
}

export default new PermissionService();
