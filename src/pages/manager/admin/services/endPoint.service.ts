import { CustomResponse } from '@/models/common/CustomResponse';
import service from '@/utils/service';
import { EndPoint, EndPointCategoryWriteDTO, EndPointWriteDTO } from '../models/EndPoint';

const prefix = '/meta/end_point';

class EndPointService {
    getList() {
        return service.get<CustomResponse<EndPoint[]>>(prefix);
    }

    get(method: string, pattern: string) {
        return service.get<CustomResponse<EndPoint>>(`${prefix}/single`, { params: { method, pattern } });
    }

    hasAccess(method: string, pattern: string) {
        return service.get<CustomResponse<boolean>>(`${prefix}/has_access`, { params: { method, pattern } });
    }
    // 更新信息的url请求
    update(data: EndPointWriteDTO) {
        return service.put<CustomResponse<EndPoint>>(`${prefix}`, { data: data });
    }
    // 批量更新分类的请求
    setCategory(data: EndPointCategoryWriteDTO) {
        return service.put<CustomResponse<null>>(`${prefix}/category`, { data });
    }
}

export default new EndPointService();
