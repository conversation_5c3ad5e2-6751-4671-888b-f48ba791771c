import { User } from '@/models/account/User';
import { CustomResponse } from '@/models/common/CustomResponse';
import service from '@/utils/service';
import {
    Group,
    GroupPermissionWriteDTO,
    GroupTag,
    GroupUserWriteDTO,
    GroupWriteDTO,
    UserGroupWriteDTO,
} from '../models/Group';

const prefix = '/account/group';

class GroupService {
    getList() {
        return service.get<CustomResponse<Group[]>>(prefix);
    }

    create(data: GroupWriteDTO) {
        return service.post<CustomResponse<Group>>(prefix, { data });
    }

    update(id: number, data: GroupWriteDTO) {
        return service.put<CustomResponse<Group>>(`${prefix}/${id}`, { data });
    }

    delete(id: number) {
        return service.delete<CustomResponse<null>>(`${prefix}/${id}`);
    }

    setGroupPermissions(id: number, data: GroupPermissionWriteDTO) {
        return service.put<CustomResponse<Group>>(`${prefix}/${id}/group_permissions`, { data });
    }

    setGroupUsers(id: number, data: GroupUserWriteDTO) {
        return service.put<CustomResponse<Group>>(`${prefix}/${id}/group_users`, { data });
    }

    setUserGroups(data: UserGroupWriteDTO) {
        return service.put<CustomResponse<User>>(`${prefix}/user_groups`, { data });
    }

    getTagList() {
        return service.get<CustomResponse<GroupTag[]>>(`${prefix}/tags`);
    }

    getUsersByTags(tags: string[]) {
        return service.get<CustomResponse<User[]>>(`${prefix}/users_by_tags`, { params: { tags } });
    }
}

export default new GroupService();
