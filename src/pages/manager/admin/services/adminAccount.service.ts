import { User } from '@/models/account/User';
import { CustomResponse } from '@/models/common/CustomResponse';
import { Convert } from '@/utils/io';
import service from '@/utils/service';
import { AdminUser } from '../models/AdminUser';
import { UserCreateFeishuParams, UserCreateParams } from '../models/UserCreateParams';

const prefix = '/account_manage';

class AdminAccountService {
    @Convert(AdminUser)
    getList() {
        return service.get<CustomResponse<AdminUser[]>>(prefix);
    }

    @Convert(AdminUser)
    createUser(data: UserCreateParams) {
        return service.post<CustomResponse<AdminUser>>(prefix, { data });
    }

    @Convert(AdminUser)
    createUserFeishu(data: UserCreateFeishuParams) {
        return service.post<CustomResponse<AdminUser>>(`${prefix}/with_feishu`, { data });
    }

    resetPwd(userId: number, newPwd: string) {
        return service.put<CustomResponse<null>>(prefix + `/${userId}/reset_pwd`, { data: { newPwd } });
    }

    @Convert(AdminUser)
    updateProfile(userId: number, profile: User) {
        return service.put<CustomResponse<AdminUser>>(prefix + `/${userId}/profile`, { data: profile });
    }

    @Convert(AdminUser)
    setRoles(userId: number, roleIds: number[]) {
        return service.put<CustomResponse<AdminUser>>(prefix + `/${userId}/roles`, { data: { roleIds } });
    }

    @Convert(AdminUser)
    setValid(userId: number, valid: boolean) {
        return service.put<CustomResponse<AdminUser>>(prefix + `/${userId}/valid`, { data: { valid } });
    }
}

export default new AdminAccountService();
