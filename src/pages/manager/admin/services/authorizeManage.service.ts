// TODO 测试 废弃
// import service from '@/utils/service'
// import { CustomResponse } from '@/models/common/CustomResponse';
// import { Role } from '../models/Role';
// import { Perm } from '@/models/account/Perm';
// import { Convert } from '@/utils/io';

// const prefix = '/authorize_manage';

// class AuthorizeManageService {
//     @Convert(Role)
//     getList() {
//         return service.get<CustomResponse<Role[]>>(prefix + '/role');
//     }

//     @Convert(Role)
//     create(role: Role) {
//         return service.post<CustomResponse<Role>>(prefix + '/role', { data: role });
//     }

//     @Convert(Role)
//     update(id: number, role: Role) {
//         return service.put<CustomResponse<Role>>(prefix + `/role/${id}`, { data: role });
//     }

//     delete(id: number) {
//         return service.delete<CustomResponse<null>>(prefix + `/role/${id}`);
//     }

//     getPermsList() {
//         return service.get<CustomResponse<Record<string, Perm>>>(prefix + '/perm');
//     }
// }

// export default new AuthorizeManageService();
