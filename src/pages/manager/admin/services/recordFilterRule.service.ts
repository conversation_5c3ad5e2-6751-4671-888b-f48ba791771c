import service from '@/utils/service';
import { FilterRule, FilterRuleWriteDTO } from '../models/FilterRule';
import { CustomResponse } from '@/models/common/CustomResponse';

const prefix = '/account/record_filter_rule';

class FilterRuleService {
    getList(method?: string, pattern?: string, permissionId?: number) {
        return service.get<CustomResponse<FilterRule[]>>(prefix, { params: { method, pattern, permissionId } });
    }

    create(data: FilterRuleWriteDTO) {
        return service.post<CustomResponse<FilterRule>>(prefix, { data });
    }

    delete(id: number) {
        return service.delete<CustomResponse<FilterRule>>(`${prefix}/${id}`);
    }
}

export default new FilterRuleService();
