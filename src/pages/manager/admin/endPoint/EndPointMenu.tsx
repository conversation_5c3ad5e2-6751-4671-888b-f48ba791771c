import { Menu, Input, MenuProps } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import uniq from 'lodash/uniq';
import React, { useEffect, useState } from 'react';
import { useCreation } from 'ahooks';
import { useEndPointQuery } from '../queries/endPoint';
import { selectedEndPointCategory, selectedEndPointsAtom } from './store';
import Item from 'antd/lib/list/Item';

export default () => {
    const menu: React.CSSProperties = {
        maxHeight: 'calc(100vh - 142px)',
        overflowY: 'auto',
        display: 'flex',
        flexDirection: 'column',
    };

    const [searchValue, setSearchValue] = useState('');
    const endPointQuery = useEndPointQuery();
    const selectedCategory = selectedEndPointCategory.use();
    const selectedEndPoints = selectedEndPointsAtom.use();

    const categories = uniq(endPointQuery.data?.map(({ category }) => category)?.filter(Boolean)).sort();
    const countAll = selectedEndPointsAtom.useSelectedEndPoints.length;

    const items = [
        {
            label: !!countAll ? (
                <strong>
                    <span>全部</span>
                    <span>（{countAll}）</span>
                </strong>
            ) : (
                '全部'
            ),
            key: '全部',
        },
    ];

    const filterItems: MenuProps['items'] = useCreation(() => {
        const filterCategory = !!searchValue
            ? categories.concat('全部').filter(item => item.toLowerCase().includes(searchValue.toLowerCase()))
            : categories;
        // 根据categories创建新的菜单项
        const categoryItems = filterCategory.map(category => {
            const count = selectedEndPoints[category]?.length;

            if (count) {
                return {
                    label: (
                        <strong>
                            {category}
                            <span>（{count}）</span>
                        </strong>
                    ),
                    key: category,
                };
            }

            return { label: category, key: category };
        });

        return !!searchValue ? [...categoryItems] : [...items, ...categoryItems];
    }, [categories, countAll, searchValue]);

    return (
        <div>
            <Input
                prefix={<SearchOutlined />}
                placeholder="搜索类别"
                value={searchValue}
                onChange={e => setSearchValue(e.target.value)}
                allowClear
            />

            <div style={menu}>
                <Menu
                    items={filterItems}
                    selectedKeys={[selectedCategory]}
                    onSelect={({ key }) => selectedEndPointCategory.store.setState(key)}
                    style={{ border: 'none' }}
                />
            </div>
        </div>
    );
};
