import SwaggerUI from '@/components/SwaggerUI';
import { CheckOutlined } from '@ant-design/icons';
import { useCreation } from 'ahooks';
import { Button, Col, Divider, Form, Modal, Row, Select, Table, TableColumnType, message } from 'antd';
import differenceBy from 'lodash/differenceBy';
import sortBy from 'lodash/sortBy';
import uniqBy from 'lodash/uniqBy';
import React from 'react';
import { useSWRConfig } from 'swr';
import { FilterRule, FilterRuleWriteDTO } from '../models/FilterRule';
import { getSinglePermissionQueryKey } from '../queries/permission';
import {
    useAllRecordFilterRulesQuery,
    useCreateRecordFilterRuleMutation,
    useDeleteRecordFilterRuleMutation,
    useRecordFilterRulesQuery,
} from '../queries/recordFilterRule';
import { useSwaggerApiDocsQuery } from '../queries/swagger';
import { updateRecordFilterRulesDialog } from './store';

type FormData = {
    filterId: number;
};

export default () => {
    const [form] = Form.useForm<FormData>();
    const { open, data } = updateRecordFilterRulesDialog.use();
    const { endPoint, permission } = data || {};
    const { method, pattern } = endPoint || {};
    const endPointId = { method, pattern };

    const allRulesQuery = useAllRecordFilterRulesQuery();
    const allRules = useCreation(() => uniqBy(allRulesQuery.data, it => it.expression), [allRulesQuery.data]);

    const params = { endPoint, permission };
    const query = useRecordFilterRulesQuery(params);
    const { data: filterRules } = query;

    const allRuleOptions = useCreation(() => {
        if (!allRules?.length) return [];

        const diffRules = differenceBy(allRules, filterRules, rule => rule.expression);
        const sortedRules = sortBy(diffRules, rule => rule.name);

        return sortedRules.map(rule => {
            const desc = rule.description ? `（${rule.description}）` : '';
            return { label: `${rule.name}${desc}`, value: rule.id };
        });
    }, [allRules, filterRules]);

    const swaggerApiDocsQuery = useSwaggerApiDocsQuery();
    const { data: swaggerApiDocs } = swaggerApiDocsQuery;

    const createMutation = useCreateRecordFilterRuleMutation(params);
    const deleteMutation = useDeleteRecordFilterRuleMutation(params);

    const { mutate } = useSWRConfig();

    const onFinish = () => {
        const { filterId } = form.getFieldsValue();
        const rule = allRules?.find(rule => rule.id === filterId);
        if (!rule) {
            message.error('规则不存在');
            return;
        }

        const { name, description, expression } = rule;
        const data: FilterRuleWriteDTO = { endPointId, permissionId: permission.id, name, description, expression };
        createMutation.trigger({ data }).then(() => form.resetFields());
    };

    const onDelete = (rule: FilterRule) => {
        Modal.confirm({ centered: true, title: '确定要删除吗？', onOk: () => deleteMutation.trigger({ id: rule.id }) });
    };

    const onCancel = () => {
        const permissionQueryKey = getSinglePermissionQueryKey(permission?.id);
        updateRecordFilterRulesDialog.hide();

        if (!permissionQueryKey) return;
        mutate(permissionQueryKey);
    };

    const filterRuleOperationColumn: TableColumnType<FilterRule> = {
        title: '操作',
        key: 'operation',
        width: 44,
        render: (_, record) => (
            <a style={{ color: 'red' }} onClick={() => onDelete(record)}>
                删除
            </a>
        ),
    };

    return (
        <Modal
            title="修改字段过滤规则"
            open={open}
            onCancel={onCancel}
            maskClosable={false}
            footer={false}
            width="70vw"
            destroyOnClose
        >
            <Row gutter={[12, 0]}>
                {!!method && !!pattern && !!swaggerApiDocs && (
                    <>
                        <Col span={24}>
                            <SwaggerUI spec={swaggerApiDocs} method={method} path={pattern} />
                        </Col>
                        <Divider dashed />
                    </>
                )}
                {!!filterRules?.length && (
                    <>
                        <h3>已启用过滤规则</h3>
                        <Col span={24}>
                            <Table
                                dataSource={filterRules}
                                columns={columns.concat([filterRuleOperationColumn])}
                                size="small"
                                pagination={{ pageSize: 5 }}
                                rowKey="id"
                            />
                        </Col>
                        <Divider dashed />
                    </>
                )}
                <h3>添加过滤规则</h3>
                <Col span={24}>
                    <Form form={form} disabled={createMutation.isMutating} onFinish={onFinish}>
                        <Row gutter={[12, 0]}>
                            <Col span={24}>
                                <Form.Item name="filterId" label="过滤规则">
                                    <Select options={allRuleOptions} optionFilterProp="label" showSearch allowClear />
                                </Form.Item>
                            </Col>
                            <Col span={24}>
                                <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                                    <Button
                                        shape="circle"
                                        type="primary"
                                        icon={<CheckOutlined />}
                                        htmlType="submit"
                                        loading={createMutation.isMutating}
                                    />
                                </div>
                            </Col>
                        </Row>
                    </Form>
                </Col>
            </Row>
        </Modal>
    );
};

const columns: TableColumnType<FilterRule>[] = [
    { dataIndex: 'name', title: '名称' },
    { dataIndex: 'description', title: '描述' },
    {
        dataIndex: 'expression',
        title: '表达式',
        render: (_, { expression }) => {
            if (!expression) return '-';

            return (
                <a
                    onClick={() =>
                        Modal.info({
                            width: '50vw',
                            centered: true,
                            title: '表达式',
                            content: <code>{expression}</code>,
                        })
                    }
                >
                    查看
                </a>
            );
        },
    },
];
