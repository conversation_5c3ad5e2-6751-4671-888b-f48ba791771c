import CollapseItems from '@/components/CollapseItems';
import { Descriptions, Spin } from 'antd';
import { sortBy } from 'lodash';
import React from 'react';
import { FilterRule } from '../models/FilterRule';
import { useSingleEndPointQuery } from '../queries/endPoint';

type Props = {
    method: string | undefined;
    pattern: string | undefined;
    filterRules?: FilterRule[];
    recordFilterRules?: FilterRule[];
};

export default ({ method, pattern, filterRules, recordFilterRules }: Props) => {
    const query = useSingleEndPointQuery(method, pattern);
    const { data: endPoint, isLoading, isValidating } = query;
    const loading = isLoading || isValidating;

    return (
        <Spin spinning={loading}>
            <Descriptions column={1} bordered>
                <Descriptions.Item label="API 名称">{endPoint?.name || '-'}</Descriptions.Item>
                <Descriptions.Item label="API 描述">{endPoint?.description || '-'}</Descriptions.Item>
                <Descriptions.Item label="API URL">{endPoint?.pattern || '-'}</Descriptions.Item>
                <Descriptions.Item label="对所有人开放">{endPoint?.publicToAll ? '是' : '-'}</Descriptions.Item>
                {!!filterRules?.length && (
                    <Descriptions.Item label="字段过滤规则">
                        <CollapseItems
                            items={sortBy(filterRules, rule => rule.name).map((rule, index) => (
                                <span key={index}>{rule.name}</span>
                            ))}
                        />
                    </Descriptions.Item>
                )}
                {!!recordFilterRules?.length && (
                    <Descriptions.Item label="条目过滤规则">
                        <CollapseItems
                            items={sortBy(recordFilterRules, rule => rule.name).map((rule, index) => (
                                <span key={index}>{rule.name}</span>
                            ))}
                        />
                    </Descriptions.Item>
                )}
            </Descriptions>
        </Spin>
    );
};
