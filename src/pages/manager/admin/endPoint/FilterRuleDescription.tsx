import { Descriptions } from 'antd';
import React from 'react';
import { FilterRule } from '../models/FilterRule';

export default ({ data }: { data: FilterRule }) => {
    return (
        <Descriptions column={1} bordered>
            <Descriptions.Item label="名称">{data?.name || '-'}</Descriptions.Item>
            <Descriptions.Item label="描述">{data?.description || '-'}</Descriptions.Item>
            <Descriptions.Item label="表达式">
                {data?.expression ? <code>{data.expression}</code> : '-'}
            </Descriptions.Item>
        </Descriptions>
    );
};
