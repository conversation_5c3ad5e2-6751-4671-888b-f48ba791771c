import { createAtom, createPopup } from '@/utils/zustand';
import flatten from 'lodash/flatten';
import groupBy from 'lodash/groupBy';
import uniqBy from 'lodash/uniqBy';
import objectHash from 'object-hash';
import { EndPoint, EndPointCategoryWriteDTO } from '../models/EndPoint';
import { Permission } from '../models/Permission';

export type EndPointActionRef = {
    getSelectedEndPoints: () => EndPoint[];
    setSelectedEndPoints: (endPoints: EndPoint[]) => void;
};

export type EndPointProps = {
    actionRef?: React.MutableRefObject<EndPointActionRef>;
    permission?: Permission;
};

export const updateEndPointDialog = createPopup<EndPoint>();
export const setEndPointsCategoryDialog = createPopup<{ endPoints: EndPoint[] }>();

export const selectedEndPointCategory = createAtom<string>('全部');
export const selectedEndPointsAtom = (() => {
    const atom = createAtom<Record<string, EndPoint[]>>({});

    const useSelectedEndPoints = () =>
        atom.use(data =>
            uniqBy(flatten(Object.values(data)), ({ method, pattern }) => objectHash({ method, pattern })),
        );

    const getSelectedEndPoints = () =>
        uniqBy(flatten(Object.values(atom.store.getState())), ({ method, pattern }) => objectHash({ method, pattern }));

    const setSelectedEndPoints = (endPoints: EndPoint[]) =>
        atom.store.setState({
            ...groupBy(endPoints, endPoint => endPoint.category),
        });

    return { ...atom, useSelectedEndPoints, getSelectedEndPoints, setSelectedEndPoints };
})();

export const updateFilterRulesDialog = createPopup<{ endPoint: EndPoint; permission: Permission }>();
export const updateRecordFilterRulesDialog = createPopup<{ endPoint: EndPoint; permission: Permission }>();
