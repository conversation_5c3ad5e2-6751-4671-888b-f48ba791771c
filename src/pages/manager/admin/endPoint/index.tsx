import { Affix, Col, Row } from 'antd';
import React, { useEffect } from 'react';
import EndPointMenu from './EndPointMenu';
import EndPointTable from './EndPointTable';
import context from './context';
import { EndPointProps, selectedEndPointCategory, selectedEndPointsAtom } from './store';
import UpdateFilterRuleDialog from './UpdateFilterRuleDialog';
import UpdateRecordFilterRuleDialog from './UpdateRecordFilterRuleDialog';
import UpdateEndPointDialog from './UpdateEndPointDialog';
import SetEndPointsCategoryDialog from './SetEndPointsCategoryDialog';

const EndPoint = (props: EndPointProps) => {
    useEffect(() => {
        return () => {
            selectedEndPointCategory.store.setState('全部');
            selectedEndPointsAtom.store.setState({});
        };
    }, []);

    return (
        <context.Provider value={props}>
            <Row gutter={[12, 0]}>
                <Col span={4}>
                    <Affix offsetTop={104}>
                        <EndPointMenu />
                    </Affix>
                </Col>
                <Col span={20}>
                    <EndPointTable />
                </Col>
            </Row>
        </context.Provider>
    );
};

export default (props: EndPointProps) => (
    <React.Fragment>
        <EndPoint {...props} />
        <UpdateEndPointDialog />
        <SetEndPointsCategoryDialog />
        <UpdateFilterRuleDialog />
        <UpdateRecordFilterRuleDialog />
    </React.Fragment>
);
