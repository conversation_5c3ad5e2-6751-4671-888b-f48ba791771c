import { ModalForm, ProFormItem, ProFormText, ProFormTextArea, ProForm } from '@ant-design/pro-form';
import { Col, Row, FormInstance } from 'antd';
import React, { useEffect, useRef } from 'react';
import { EndPointWriteDTO } from '../models/EndPoint';
import { updateEndPointDialog } from './store';
import { useEndPointQuery, useUpdateEndpointMutation } from '../queries/endPoint';
import AutoCompleteSearch from '@/components/AutoCompleteSearch';
import uniq from 'lodash/uniq';

// 修改api基本信息的表单
export default () => {
    // 数据表单
    const formRef = useRef<FormInstance<EndPointWriteDTO>>();
    // 拿到dialog状态管理相关属性
    const { open, data } = updateEndPointDialog.use();

    const { trigger } = useUpdateEndpointMutation();
    // 有依赖的useEffect，在依赖值变化时触发
    useEffect(() => {
        const form = formRef.current;
        if (!form) return;
        // 清除旧数据
        form.resetFields();
        if (!open || !data) return;
        form.setFieldsValue(data);
    }, [open]);
    // 提交表单的方法
    const onFinish = async () => {
        const formData = formRef.current.getFieldsValue();
        console.log('form data: ', formData);
        await trigger({ data: formData });
        updateEndPointDialog.hide();
    };

    // 拿到分类信息的方法，filter(Boolean)可以过滤掉'' 0 null undefined ...
    const endPointQuery = useEndPointQuery();
    const categories = uniq(endPointQuery.data?.map(({ category }) => category)?.filter(Boolean)).sort();

    return (
        <ModalForm
            formRef={formRef}
            title="修改信息"
            visible={open}
            modalProps={{ onCancel: () => updateEndPointDialog.hide() }}
            onFinish={onFinish}
        >
            <Row>
                <Col span={24}>
                    <ProForm.Group>
                        <ProFormText name="method" label="请求方式" readonly={true} />
                        <ProFormText name="pattern" label="URL" readonly={true} />
                    </ProForm.Group>
                </Col>
                <Col span={24}>
                    <ProFormText name="name" label="API名称" />
                </Col>
                <Col span={24}>
                    <ProFormTextArea name="description" label="API描述" />
                </Col>
                <Col span={24}>
                    <ProFormItem name="category" label="分类">
                        <AutoCompleteSearch
                            placeholder="请选择或输入"
                            options={categories.map(category => ({ label: category, value: category }))}
                        />
                    </ProFormItem>
                </Col>
            </Row>
        </ModalForm>
    );
};
