import React, { useState } from 'react';
import { setEndPointsCategoryDialog } from './store';
import { endPointQueryKey, useSetEndPointsCategoryMutation } from '../queries/endPoint';
import { mutate } from 'swr';
import { Button, Drawer, message } from 'antd';
import { ProColumnType, ProTable } from '@ant-design/pro-components';
import { EndPoint, EndPointId } from '../models/EndPoint';
import CollapseItems from '@/components/CollapseItems';
import { useCreation } from 'ahooks';
import objectHash from 'object-hash';
import AutoCompleteSearch from '@/components/AutoCompleteSearch';

/**
 * 批量修改类别
 */
export default () => {
    const [messageApi, contextHolder] = message.useMessage();
    const { open, data } = setEndPointsCategoryDialog.use();
    const { trigger, isMutating } = useSetEndPointsCategoryMutation();
    const [category, setCategory] = useState('');
    const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

    const categories = useCreation(() => {
        return !!data?.endPoints ? [...new Set(data.endPoints.map(it => it?.category))] : [];
    }, [data?.endPoints]);

    const selectedAllEndPoints = () => {
        const res = data?.endPoints.map(it => hashEndPoint({ method: it.method, pattern: it.pattern }));
        setSelectedRowKeys(res);
    };

    const reset = () => {
        setCategory('');
        setSelectedRowKeys([]);
    };

    const setEndPointsCategory = async () => {
        const safeCategory = category.replace(/\s+/g, '');
        if (!safeCategory) {
            messageApi.open({
                type: 'warning',
                content: '请输入类别',
            });
            return;
        }
        const safeSelected = data?.endPoints
            .map(it => ({ method: it.method, pattern: it.pattern }))
            .filter(it => selectedRowKeys?.includes(hashEndPoint(it)));
        if (!safeSelected) {
            messageApi.open({
                type: 'warning',
                content: '请选择api',
            });
            return;
        }
        console.log('提交的数据', safeCategory, safeSelected);
        await trigger({ data: { category: safeCategory, endPoints: safeSelected } });
        setEndPointsCategoryDialog.hide();
        reset();
        mutate(endPointQueryKey);
    };

    return (
        <>
            {contextHolder}
            <Drawer
                width="80vw"
                title="修改API 类别"
                open={open}
                onClose={() => setEndPointsCategoryDialog.hide()}
                destroyOnClose
            >
                <ProTable
                    search={false}
                    dataSource={data?.endPoints}
                    columns={columns}
                    rowSelection={{
                        selectedRowKeys,
                        onChange: keys => {
                            console.log(keys);
                            setSelectedRowKeys(keys as string[]);
                        },
                    }}
                    rowKey={record => hashEndPoint(record)}
                    toolBarRender={() => [
                        <Button
                            type="primary"
                            style={{ backgroundColor: 'orange', borderColor: 'orange' }}
                            onClick={selectedAllEndPoints}
                        >
                            选择所有
                        </Button>,
                        <AutoCompleteSearch
                            style={{ width: '200px' }}
                            value={category}
                            onChange={setCategory}
                            placeholder="请选择或输入"
                            options={categories.map(it => ({ label: it, value: it }))}
                        />,
                        <Button onClick={reset}>重置</Button>,
                        <Button type="primary" onClick={() => setEndPointsCategory()} disabled={isMutating}>
                            提交
                        </Button>,
                    ]}
                />
            </Drawer>
        </>
    );
};

const hashEndPoint = ({ method, pattern }: EndPointId) => objectHash({ method, pattern });

const columns: ProColumnType<EndPoint>[] = [
    { dataIndex: 'name', title: 'API 名称', sorter: (a, b) => (a.name || '').localeCompare(b.name || '') },
    { dataIndex: 'description', title: 'API 描述' },
    { dataIndex: 'category', title: '分类' },
    { dataIndex: 'method', title: '请求方式' },
    { dataIndex: 'pattern', title: 'URL', render: dom => <pre>{dom}</pre> },
    {
        dataIndex: 'permissions',
        title: '所属权限',
        render: (_, { permissions }) => (permissions?.length ? <CollapseItems items={permissions} /> : '-'),
    },
    { dataIndex: 'publicToAll', title: '对所有人开放', render: (_, record) => (record.publicToAll ? '是' : '-') },
];
