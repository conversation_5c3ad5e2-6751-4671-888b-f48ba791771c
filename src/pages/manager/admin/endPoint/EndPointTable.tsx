import CollapseItems from '@/components/CollapseItems';
import DropdownOperation from '@/components/DropdownOperation';
import { ProColumnType, ProTable } from '@ant-design/pro-components';
import { useCreation } from 'ahooks';
import { Popover } from 'antd';
import groupBy from 'lodash/groupBy';
import sortBy from 'lodash/sortBy';
import objectHash from 'object-hash';
import React, { useContext } from 'react';
import { EndPoint, EndPointId } from '../models/EndPoint';
import { useEndPointQuery } from '../queries/endPoint';
import { useSinglePermissionQuery } from '../queries/permission';
import FilterRuleDescription from './FilterRuleDescription';
import context from './context';
import {
    selectedEndPointCategory,
    selectedEndPointsAtom,
    setEndPointsCategoryDialog,
    updateEndPointDialog,
    updateFilterRulesDialog,
    updateRecordFilterRulesDialog,
} from './store';
import useTableFilter from '../hooks/useTableFilter';

export default () => {
    const props = useContext(context);
    const { actionRef } = props;

    const permissionQuery = useSinglePermissionQuery(props?.permission?.id);
    const { data: permission } = permissionQuery;
    const { filterRules, recordFilterRules } = permission || {};

    const filterRulesByEndPointId = useCreation(() => {
        return groupBy(filterRules, ({ method, pattern }) => hashEndPoint({ method, pattern }));
    }, [filterRules]);

    const recordFilterRulesByEndPointId = useCreation(() => {
        return groupBy(recordFilterRules, ({ method, pattern }) => hashEndPoint({ method, pattern }));
    }, [recordFilterRules]);

    const endPointQuery = useEndPointQuery();
    const loading = endPointQuery.isLoading || endPointQuery.isValidating;

    const selectedEndPoints = selectedEndPointsAtom.use();
    const selectedCategory = selectedEndPointCategory.use();

    if (!!actionRef && !actionRef.current) {
        actionRef.current = {
            getSelectedEndPoints: selectedEndPointsAtom.getSelectedEndPoints,
            setSelectedEndPoints: selectedEndPointsAtom.setSelectedEndPoints,
        };
    }

    const selectedRowKeys = useCreation(() => {
        const endPoints =
            selectedCategory === '全部'
                ? selectedEndPointsAtom.getSelectedEndPoints()
                : selectedEndPoints[selectedCategory];

        return endPoints?.map(hashEndPoint) || [];
    }, [selectedEndPoints, selectedCategory]);

    const selectedRowKeySet = useCreation(() => new Set(selectedRowKeys), [selectedRowKeys]);

    let data = endPointQuery.data;

    data = useCreation(() => {
        if (!selectedCategory || selectedCategory === '全部') return data;

        return data?.filter(({ category }) => category === selectedCategory);
    }, [data, selectedCategory]);

    data = useCreation(() => {
        if (!selectedRowKeys?.length) return data;

        return sortBy(
            data,
            endPoint => !selectedRowKeySet.has(hashEndPoint(endPoint)),
            endPoint => endPoint.pattern,
        );
    }, [data, selectedRowKeySet]);

    const onSelect = (record: EndPoint, selected: boolean) => {
        const { category } = record;

        selectedEndPointsAtom.store.setState(prev => {
            const list = prev[category] || [];
            if (selected) return { ...prev, [category]: list.concat([record]) };

            return { ...prev, [category]: list.filter(it => hashEndPoint(it) !== hashEndPoint(record)) };
        });
    };

    const onChange = (_: React.Key[], rows: EndPoint[], { type }) => {
        if (type === 'none' || (type === 'all' && !rows.length)) {
            selectedEndPointsAtom.store.setState(prev => {
                if (selectedCategory === '全部') return {};
                return { ...prev, [selectedCategory]: [] };
            });

            return;
        }

        if (type === 'all') {
            selectedEndPointsAtom.store.setState(prev => ({ ...prev, ...groupBy(rows, row => row.category) }));

            return;
        }
    };

    const { filterColumns } = useTableFilter<EndPoint>(data, columns);

    const filterRuleColumns: ProColumnType<EndPoint>[] = [];
    if (!!permission) {
        // if (true) {
        filterRuleColumns.push(
            {
                title: '字段过滤规则',
                key: 'filterRules',
                render: (_, record) => {
                    const filterRules = filterRulesByEndPointId[hashEndPoint(record)];
                    if (!filterRules?.length) return '-';

                    return (
                        <CollapseItems
                            items={sortBy(filterRules, rule => rule.name).map((rule, index) => (
                                <Popover key={index} content={<FilterRuleDescription data={rule} />}>
                                    <a>{rule.name}</a>
                                </Popover>
                            ))}
                        />
                    );
                },
            },
            {
                title: '条目过滤规则',
                key: 'recordFilterRules',
                render: (_, record) => {
                    const recordFilterRules = recordFilterRulesByEndPointId[hashEndPoint(record)];
                    if (!recordFilterRules?.length) return '-';

                    return (
                        <CollapseItems
                            items={sortBy(recordFilterRules, rule => rule.name).map((rule, index) => (
                                <Popover key={index} content={<FilterRuleDescription data={rule} />}>
                                    <a>{rule.name}</a>
                                </Popover>
                            ))}
                        />
                    );
                },
            },
            {
                title: '操作',
                key: 'filterRuleOperation',
                render: (_, endPoint) => {
                    return (
                        <DropdownOperation
                            menu={{
                                items: [
                                    {
                                        label: '修改字段过滤规则',
                                        key: 'editFilterRule',
                                        onClick: () => updateFilterRulesDialog.show({ data: { endPoint, permission } }),
                                    },
                                    {
                                        label: '修改条目过滤规则',
                                        key: 'editRecordFilterRule',
                                        onClick: () =>
                                            updateRecordFilterRulesDialog.show({ data: { endPoint, permission } }),
                                    },
                                ],
                            }}
                        />
                    );
                },
            },
        );
    }

    // 操作栏
    const operationColumn: ProColumnType<EndPoint> = {
        key: 'operation',
        title: '操作',
        width: 44,
        render: (_, endPoint) => {
            return (
                <DropdownOperation
                    menu={{
                        items: [
                            {
                                label: '编辑信息',
                                key: 'editApiInfo',
                                onClick: () => updateEndPointDialog.show({ data: endPoint }),
                            },
                            {
                                label: '批量分类',
                                key: 'batchCategory',
                                // 这里将整个table的api数据存入store
                                onClick: () => setEndPointsCategoryDialog.show({ data: { endPoints: data } }),
                            },
                        ],
                    }}
                />
            );
        },
    };

    return (
        <ProTable
            headerTitle="API 列表"
            loading={loading}
            search={false}
            columns={actionRef ? filterColumns.concat(filterRuleColumns) : filterColumns.concat(operationColumn)}
            rowSelection={actionRef ? { selectedRowKeys, onSelect, onChange } : undefined}
            dataSource={data}
            rowKey={endPoint => hashEndPoint(endPoint)}
        />
    );
};

const hashEndPoint = ({ method, pattern }: EndPointId) => objectHash({ method, pattern });

const columns: ProColumnType<EndPoint>[] = [
    { dataIndex: 'name', title: 'API 名称', sorter: (a, b) => (a.name || '').localeCompare(b.name || '') },
    { dataIndex: 'description', title: 'API 描述' },
    { dataIndex: 'category', title: '分类' },
    { dataIndex: 'method', title: '请求方式' },
    { dataIndex: 'pattern', title: 'URL', render: dom => <pre>{dom}</pre> },
    {
        dataIndex: 'permissions',
        title: '所属权限',
        render: (_, { permissions }) => (permissions?.length ? <CollapseItems items={permissions} /> : '-'),
    },
    { dataIndex: 'publicToAll', title: '对所有人开放', render: (_, record) => (record.publicToAll ? '是' : '-') },
];
