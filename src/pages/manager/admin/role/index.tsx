// TODO 测试 废弃
// import React from 'react';
// import { Button } from 'antd';
// import { CrudTable, DeleteWrapper, useCrudTableAction } from '@/components/CrudTable';
// import { DrawerForm, FormTrigger } from '@/components/TriggerableForm';
// import { mapAntdTable } from '@/utils/fieldProperty';
// import service from '../services/authorizeManage.service';
// import { Role, roleFields } from '../models/Role';
// import FormItems from './FormItems';

// export default () => {
//     const actionRef = useCrudTableAction<Role>();

//     const columns = mapAntdTable(roleFields, [
//         { dataIndex: 'name' },
//         { dataIndex: 'description' },
//         { dataIndex: 'permList' },
//         {
//             title: '操作',
//             key: 'option',
//             valueType: 'option',
//             width: 100,
//             doNotExport: true,
//             render: (_, record) =>
//                 record.id === 1
//                     ? []
//                     : [
//                           <FormTrigger key="edit" id={record.id} data={record}>
//                               <a>编辑</a>
//                           </FormTrigger>,
//                           <DeleteWrapper key="delete" id={record.id} confirm={`是否确认删除角色${record.name}？`}>
//                               <a style={{ color: 'red' }} href="#">
//                                   删除
//                               </a>
//                           </DeleteWrapper>,
//                       ],
//         },
//     ]);

//     return (
//         <DrawerForm<Role>
//             name="roleForm"
//             createTitle="创建角色"
//             editTitle="编辑角色"
//             width={500}
//             labelWidth={100}
//             onCreate={service.create}
//             onUpdate={service.update}
//             afterCreate={actionRef.create}
//             afterUpdate={data => actionRef.update(data.id, data)}
//             formContents={<FormItems />}
//         >
//             <CrudTable<Role>
//                 title="角色"
//                 service={service}
//                 recordInsertAt="last"
//                 actionRef={actionRef}
//                 columns={columns}
//                 search={false}
//                 toolBarRender={() => [
//                     <FormTrigger key="create">
//                         <Button type="primary">创建角色</Button>
//                     </FormTrigger>,
//                 ]}
//                 rowKey="id"
//                 newVersion
//             />
//         </DrawerForm>
//     );
// };
