import AutoCompleteSearch from '@/components/AutoCompleteSearch';
import { ProFormItem, ProFormText, ProFormTextArea } from '@ant-design/pro-form';
import { Col, Row } from 'antd';
import uniq from 'lodash/uniq';
import React from 'react';
import { usePermissionQuery } from '../queries/permission';

export default () => {
    const permissionQuery = usePermissionQuery();
    const categories = uniq(permissionQuery.data?.map(({ category }) => category)?.filter(Boolean)).sort();

    return (
        <Row>
            <Col span={24}>
                <ProFormText name="name" label="权限名称" />
            </Col>
            <Col span={24}>
                <ProFormTextArea name="description" label="权限描述" />
            </Col>
            <Col span={24}>
                <ProFormItem name="category" label="分类">
                    <AutoCompleteSearch
                        placeholder="请选择或输入"
                        options={categories.map(category => ({ label: category, value: category }))}
                    />
                </ProFormItem>
            </Col>
        </Row>
    );
};
