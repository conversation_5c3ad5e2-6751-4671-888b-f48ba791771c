import { Col, Row, Affix } from 'antd';
import React, { useEffect } from 'react';
import PermissionMenu from './PermissionMenu';
import context from './context';
import PermissionTable from './PermissionTable';
import CreatePermissionDialog from './CreatePermissionDialog';
import UpdatePermissionDialog from './UpdatePermissionDialog';
import SetPermissionEndPointsDialog from './SetPermissionEndPointsDialog';
import { PermissionProps, selectedPermissionCategory, selectedPermissionsAtom } from './store';

export const Permission = (props: PermissionProps) => {
    useEffect(() => {
        return () => {
            selectedPermissionCategory.store.setState('全部');
            selectedPermissionsAtom.store.setState({});
        };
    }, []);

    return (
        <context.Provider value={props}>
            <Row gutter={[12, 0]}>
                <Col span={4}>
                    <Affix offsetTop={104}>
                        <PermissionMenu />
                    </Affix>
                </Col>
                <Col span={20}>
                    <PermissionTable />
                </Col>
            </Row>
        </context.Provider>
    );
};

export default () => {
    return (
        <React.Fragment>
            <Permission />
            <CreatePermissionDialog />
            <UpdatePermissionDialog />
            <SetPermissionEndPointsDialog />
        </React.Fragment>
    );
};
