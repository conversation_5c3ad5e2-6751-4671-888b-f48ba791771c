import CollapseItems from '@/components/CollapseItems';
import DropdownOperation from '@/components/DropdownOperation';
import { ProColumnType, ProTable } from '@ant-design/pro-components';
import { useCreation } from 'ahooks';
import { Button, Modal, Popover } from 'antd';
import groupBy from 'lodash/groupBy';
import sortBy from 'lodash/sortBy';
import React, { useContext, useEffect } from 'react';
import EndPointDescription from '../endPoint/EndPointDescription';
import { Permission } from '../models/Permission';
import { useDeletePermissionMutation, usePermissionQuery } from '../queries/permission';
import context from './context';
import {
    createPermissionDialog,
    selectedPermissionCategory,
    selectedPermissionsAtom,
    setPermissionEndPointsDialog,
    updatePermissionDialog,
} from './store';
import useTableFilter from '../hooks/useTableFilter';

export default () => {
    const props = useContext(context);
    const { actionRef, group } = props;

    const permissionQuery = usePermissionQuery();
    const loading = permissionQuery.isLoading || permissionQuery.isValidating;

    const deleteMutation = useDeletePermissionMutation();

    const selectedPermissions = selectedPermissionsAtom.use();
    const selectedCategory = selectedPermissionCategory.use();

    useEffect(() => {
        if (!actionRef) return;

        actionRef.current = {
            getSelectedPermissions: selectedPermissionsAtom.getSelectedPermissions,
            setSelectedPermissions: selectedPermissionsAtom.setSelectedPermissions,
        };
    }, [actionRef, selectedPermissions]);

    const selectedRowKeys = useCreation(() => {
        const permissions =
            selectedCategory === '全部'
                ? selectedPermissionsAtom.getSelectedPermissions()
                : selectedPermissions[selectedCategory] || [];

        return permissions.map(permission => permission.id);
    }, [selectedPermissions, selectedCategory]);

    const selectedRowKeySet = useCreation(() => new Set(selectedRowKeys), [selectedRowKeys]);

    let data = permissionQuery.data;

    data = useCreation(() => {
        if (!selectedCategory || selectedCategory === '全部') return data;

        return data?.filter(({ category }) => category === selectedCategory);
    }, [data, selectedCategory]);

    data = useCreation(() => {
        if (!selectedRowKeys?.length) return data;

        return sortBy(
            data,
            permission => !selectedRowKeySet.has(permission.id),
            permission => permission.name,
        );
    }, [data, selectedRowKeySet]);

    const onSelect = (record: Permission, selected: boolean) => {
        const { category } = record;

        selectedPermissionsAtom.store.setState(prev => {
            const list = prev[category] || [];
            if (selected) return { ...prev, [category]: list.concat([record]) };

            return { ...prev, [category]: list.filter(it => it.id !== record.id) };
        });
    };

    const onChange = (_: React.Key[], rows: Permission[], { type }) => {
        if (type === 'none' || (type === 'all' && !rows.length)) {
            selectedPermissionsAtom.store.setState(prev => {
                if (selectedCategory === '全部') return {};
                return { ...prev, [selectedCategory]: [] };
            });

            return;
        }

        if (type === 'all') {
            selectedPermissionsAtom.store.setState(prev => ({ ...prev, ...groupBy(rows, row => row.category) }));

            return;
        }
    };

    const operationColumn: ProColumnType<Permission> = {
        key: 'operation',
        title: '操作',
        width: 44,
        render: (_, record) => {
            return (
                <DropdownOperation
                    menu={{
                        items: [
                            {
                                label: '复制',
                                key: 'replicate',
                                onClick: () => createPermissionDialog.show({ data: record }),
                            },
                            {
                                label: '修改名称',
                                key: 'editName',
                                onClick: () => updatePermissionDialog.show({ data: record }),
                            },
                            {
                                label: '修改关联 API',
                                key: 'editAPI',
                                onClick: () => setPermissionEndPointsDialog.show({ data: record }),
                            },
                            { type: 'divider' },
                            {
                                label: '删除',
                                key: 'delete',
                                style: { color: 'red' },
                                onClick: () =>
                                    Modal.confirm({
                                        title: '确定要删除吗？',
                                        onOk: () => deleteMutation.trigger({ id: record.id }),
                                    }),
                            },
                        ],
                    }}
                />
            );
        },
    };

    const { filterColumns } = useTableFilter(data, fieldColumns);

    return (
        <ProTable
            headerTitle="权限列表"
            loading={loading}
            search={false}
            columns={actionRef ? filterColumns : filterColumns.concat([operationColumn])}
            rowSelection={actionRef ? { selectedRowKeys, onChange, onSelect } : undefined}
            dataSource={data}
            pagination={{ pageSize: 10 }}
            toolBarRender={
                group
                    ? undefined
                    : () => [
                          <Button type="primary" onClick={() => createPermissionDialog.show()}>
                              新增
                          </Button>,
                      ]
            }
            rowKey="id"
        />
    );
};

const fieldColumns: ProColumnType<Permission>[] = [
    { dataIndex: 'name', title: '权限名称', sorter: (a, b) => (a.name || '').localeCompare(b.name || '') },
    { dataIndex: 'description', title: '权限描述' },
    { dataIndex: 'category', title: '分类' },
    {
        dataIndex: 'endPoints',
        title: '关联 API',
        render: (_, record) => {
            const { endPoints } = record;
            if (!endPoints?.length) return '-';

            return (
                <CollapseItems
                    items={sortBy(endPoints, endPoint => endPoint.pattern).map((endPoint, index) => {
                        const filterRules = record.filterRules?.filter(
                            rule => rule.method === endPoint.method && rule.pattern == endPoint.pattern,
                        );
                        const recordFilterRules = record.recordFilterRules?.filter(
                            rule => rule.method === endPoint.method && rule.pattern == endPoint.pattern,
                        );

                        return (
                            <Popover
                                key={index}
                                content={
                                    <EndPointDescription
                                        method={endPoint.method}
                                        pattern={endPoint.pattern}
                                        filterRules={filterRules}
                                        recordFilterRules={recordFilterRules}
                                    />
                                }
                            >
                                <a>{endPoint.name || endPoint.pattern}</a>
                            </Popover>
                        );
                    })}
                />
            );
        },
    },
    {
        dataIndex: 'groups',
        title: '所属用户组',
        render: (_, { groups }) => (groups?.length ? <CollapseItems items={groups} /> : '-'),
    },
];
