import { createAtom, createPopup } from '@/utils/zustand';
import flatten from 'lodash/flatten';
import groupBy from 'lodash/groupBy';
import uniqBy from 'lodash/uniqBy';
import { Group } from '../models/Group';
import { Permission } from '../models/Permission';

export type PermissionActionRef = {
    getSelectedPermissions: () => Permission[];
    setSelectedPermissions: (permissions: Permission[]) => void;
};

export type PermissionProps = {
    actionRef?: React.MutableRefObject<PermissionActionRef>;
    group?: Group;
};

export const createPermissionDialog = createPopup<Permission | undefined>();
export const updatePermissionDialog = createPopup<Permission>();
export const setPermissionEndPointsDialog = createPopup<Permission>();

export const selectedPermissionCategory = createAtom<string>('全部');
export const selectedPermissionsAtom = (() => {
    const atom = createAtom<Record<string, Permission[]>>({});

    const useSelectedPermissions = () =>
        atom.use(data => uniqBy(flatten(Object.values(data)), permission => permission.id));

    const getSelectedPermissions = () =>
        uniqBy(flatten(Object.values(atom.store.getState())), permission => permission.id);

    const setSelectedPermissions = (permissions: Permission[]) =>
        atom.store.setState({
            ...groupBy(permissions, permission => permission.category),
        });

    return { ...atom, useSelectedPermissions, getSelectedPermissions, setSelectedPermissions };
})();
