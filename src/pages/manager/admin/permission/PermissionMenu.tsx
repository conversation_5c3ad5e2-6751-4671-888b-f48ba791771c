import { Menu, MenuProps, Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { usePermissionQuery } from '../queries/permission';
import { selectedPermissionCategory, selectedPermissionsAtom } from './store';
import uniq from 'lodash/uniq';
import { useCreation } from 'ahooks';

export default () => {
    const menu: React.CSSProperties = {
        maxHeight: 'calc(100vh - 142px)',
        overflowY: 'auto',
        display: 'flex',
        flexDirection: 'column',
    };

    const [searchValue, setSearchValue] = useState('');
    const permissionQuery = usePermissionQuery();
    const selectedCategory = selectedPermissionCategory.use();
    const selectedPermissions = selectedPermissionsAtom.use();

    const categories = uniq(permissionQuery.data?.map(({ category }) => category)?.filter(Boolean)).sort();
    const countAll = selectedPermissionsAtom.useSelectedPermissions().length;

    const items: MenuProps['items'] = [
        {
            label: !!countAll ? (
                <strong>
                    <span>全部</span>
                    <span>（{countAll}）</span>
                </strong>
            ) : (
                '全部'
            ),
            key: '全部',
        },
    ];

    const filterItems: MenuProps['items'] = useCreation(() => {
        const filterCategory = !!searchValue
            ? categories.filter(item => item.toLowerCase().includes(searchValue.toLowerCase()))
            : categories;
        const categoryItems = filterCategory.map(category => {
            const count = selectedPermissions[category]?.length;
            if (!!count)
                return {
                    label: (
                        <strong>
                            {category}
                            <span>（{count}）</span>
                        </strong>
                    ),
                    key: category,
                };
            return { label: category, key: category };
        });
        return !!searchValue ? [...categoryItems] : [...items, ...categoryItems];
    }, [categories, countAll, searchValue]);

    return (
        <div>
            <Input
                prefix={<SearchOutlined />}
                value={searchValue}
                onChange={e => setSearchValue(e.target.value)}
                placeholder="搜索类别"
                allowClear
            />
            <div style={menu}>
                <Menu
                    items={filterItems}
                    selectedKeys={[selectedCategory]}
                    onSelect={({ key }) => selectedPermissionCategory.store.setState(key)}
                />
            </div>
        </div>
    );
};
