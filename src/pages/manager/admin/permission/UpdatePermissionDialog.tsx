import { ModalForm } from '@ant-design/pro-form';
import { FormInstance } from 'antd';
import React, { useEffect, useRef } from 'react';
import { PermissionWriteDTO } from '../models/Permission';
import { useUpdatePermissionMutation } from '../queries/permission';
import PermissionFormItems from './PermissionFormItems';
import { updatePermissionDialog } from './store';

export default () => {
    const formRef = useRef<FormInstance<PermissionWriteDTO>>();
    const { open, data } = updatePermissionDialog.use();

    const { trigger } = useUpdatePermissionMutation();

    useEffect(() => {
        const form = formRef.current;
        if (!form) return;

        form.resetFields();
        if (!data || !open) return;

        form.setFieldsValue(data);
    }, [open]);

    const onFinish = async () => {
        const formData = formRef.current.getFieldsValue();
        await trigger({ id: data.id, data: formData });
        updatePermissionDialog.hide();
    };

    return (
        <ModalForm
            formRef={formRef}
            title="修改名称"
            visible={open}
            modalProps={{ onCancel: () => updatePermissionDialog.hide() }}
            onFinish={onFinish}
        >
            <PermissionFormItems />
        </ModalForm>
    );
};
