import CollapseItems from '@/components/CollapseItems';
import { Descriptions, Spin } from 'antd';
import React from 'react';
import { useSinglePermissionQuery } from '../queries/permission';

export default ({ permissionId }: { permissionId: number }) => {
    const query = useSinglePermissionQuery(permissionId);
    const { data: permission, isLoading, isValidating } = query;
    const loading = isLoading || isValidating;

    return (
        <Spin spinning={loading}>
            <Descriptions column={1} bordered>
                <Descriptions.Item label="权限名称">{permission?.name || '-'}</Descriptions.Item>
                <Descriptions.Item label="权限描述">{permission?.description || '-'}</Descriptions.Item>
                <Descriptions.Item label="关联 API">
                    {permission?.endPoints ? (
                        <CollapseItems
                            items={permission.endPoints.map((endPoint, index) => (
                                <span key={index}>{endPoint.name || endPoint.pattern}</span>
                            ))}
                        />
                    ) : (
                        <>-</>
                    )}
                </Descriptions.Item>
            </Descriptions>
        </Spin>
    );
};
