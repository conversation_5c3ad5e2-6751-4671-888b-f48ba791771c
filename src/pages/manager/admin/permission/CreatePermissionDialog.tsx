import { ModalForm } from '@ant-design/pro-form';
import { FormInstance } from 'antd';
import React, { useEffect, useRef } from 'react';
import { PermissionWriteDTO } from '../models/Permission';
import { useCreatePermissionMutation, useSetPermissionEndPointsMutation } from '../queries/permission';
import PermissionFormItems from './PermissionFormItems';
import { createPermissionDialog } from './store';

export default () => {
    const formRef = useRef<FormInstance<PermissionWriteDTO>>();
    const { open, data } = createPermissionDialog.use();

    useEffect(() => {
        const form = formRef.current;
        if (!form) return;

        if (!data || !open) return;
        form.resetFields();
        form.setFieldsValue(data);
    }, [data]);

    const { trigger } = useCreatePermissionMutation();
    const setPermissionEndPointsMutation = useSetPermissionEndPointsMutation();

    const onFinish = async () => {
        const formData = formRef.current.getFieldsValue();
        const result = await trigger({ data: formData });

        const { endPoints } = createPermissionDialog.store.getState().data || {};
        if (endPoints?.length) {
            const endPointIds = data.endPoints.map(({ method, pattern }) => ({ method, pattern }));
            await setPermissionEndPointsMutation.trigger({ id: result.id, data: { endPointIds } });
        }

        createPermissionDialog.hide();
    };

    return (
        <ModalForm
            formRef={formRef}
            title="修改名称"
            visible={open}
            modalProps={{ onCancel: () => createPermissionDialog.hide() }}
            onFinish={onFinish}
        >
            <PermissionFormItems />
        </ModalForm>
    );
};
