import { <PERSON>ton, ButtonProps, <PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd';
import React, { useEffect, useRef } from 'react';
import { useSWRConfig } from 'swr';
import EndPointComponent from '../endPoint';
import { EndPointActionRef } from '../endPoint/store';
import { useEndPointQuery } from '../queries/endPoint';
import { permissionQueryKey, useSetPermissionEndPointsMutation } from '../queries/permission';
import { setPermissionEndPointsDialog } from './store';

export default () => {
    const { open, data } = setPermissionEndPointsDialog.use();
    const actionRef = useRef<EndPointActionRef>();

    const { trigger, isMutating } = useSetPermissionEndPointsMutation();

    useEffect(() => {
        const action = actionRef.current;
        if (!action) return;

        action.setSelectedEndPoints([]);

        if (!data || !open) return;
        action.setSelectedEndPoints(data.endPoints);
    }, [open]);

    const { mutate } = useSWRConfig();

    const onFinish = async () => {
        const action = actionRef.current;
        if (!action) return;

        const selectedEndPoints = action.getSelectedEndPoints();
        const endPointIds = selectedEndPoints.map(({ method, pattern }) => ({ method, pattern }));
        await trigger({ id: data.id, data: { endPointIds } });
        setPermissionEndPointsDialog.hide();
        mutate(permissionQueryKey);
    };

    return (
        <Drawer
            width="80vw"
            title="修改关联 API"
            open={open}
            onClose={() => setPermissionEndPointsDialog.hide()}
            destroyOnClose
        >
            <EndPointComponent actionRef={actionRef} permission={data} />
            <Row justify="end" gutter={[12, 0]} style={{ padding: '0 24px' }}>
                <Col>
                    <SubmitButton type="primary" onClick={onFinish} loading={isMutating}>
                        提交
                    </SubmitButton>
                </Col>
            </Row>
        </Drawer>
    );
};

const SubmitButton = (props: ButtonProps) => {
    const { isLoading } = useEndPointQuery();

    return (
        <Button {...props} disabled={isLoading}>
            提交
        </Button>
    );
};
