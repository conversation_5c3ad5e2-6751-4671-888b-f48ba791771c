import { ProColumnType } from '@ant-design/pro-components';
import { useCreation } from 'ahooks';

// protable组件使用的过滤数据格式
export interface FilterData {
    [key: string]: { text: string; value: string }[];
}

const useTableFilter = <T extends Record<string, any>>(data: any[] | undefined, columns: ProColumnType[]) => {
    const generateFilterData: FilterData = useCreation(() => {
        if (!data) return {};
        // 1.提取出key，要求value不能是对象数组，对象数组的解析可能需要单独定义，不能通用
        const allKeys = [
            ...new Set(
                data.flatMap(obj => {
                    return Object.entries(obj)
                        .filter(([key, value]) => {
                            if (Array.isArray(value)) {
                                return value.length !== 0 && typeof value[0] !== 'object';
                            }
                            return true;
                        })
                        .map(([key]) => key);
                }),
            ),
        ];
        // 2.构造 FilterData 对象
        const ret = allKeys.reduce((acc, key) => {
            // 2.1拿到所有value的值
            const values = data.map(it => it[key]);
            // 2.2value可能是数组，把数组展平
            const uniqueValues = [
                ...new Set(
                    values.flatMap(value => {
                        // 处理空值情况：空数组、空对象、undefined 等替换为 undefined
                        if (
                            !value ||
                            (Array.isArray(value) && value.length === 0) ||
                            (typeof value === 'object' && Object.keys(value).length === 0)
                        ) {
                            return [undefined];
                        }
                        return Array.isArray(value) ? value : [value];
                    }),
                ),
            ];
            acc[key] = uniqueValues.map(it => ({ text: String(it), value: String(it) }));
            return acc;
        }, {} as { [key: string]: { text: string; value: string }[] });
        return ret;
    }, [data]);

    // 筛选规则，value是筛选表格中选中的value，render对应列数据
    const filterRule = (dataIndex: string) => (value: string, record: any) => {
        const label = record[dataIndex];
        return value != 'undefined'
            ? String(label).indexOf(String(value)) !== -1
            : !label ||
                  (Array.isArray(label) && label.length === 0) ||
                  (typeof label === 'object' && Object.keys(label).length === 0);
    };

    // 构造带筛选的colum
    const filterColumns: ProColumnType<T>[] = useCreation(() => {
        return columns.map(column => ({
            ...column,
            filters: generateFilterData?.[column.dataIndex as string],
            onFilter: filterRule(column.dataIndex as string),
        }));
    }, [generateFilterData]);

    return {
        generateFilterData,
        filterRule,
        filterColumns,
    };
};

export default useTableFilter;
