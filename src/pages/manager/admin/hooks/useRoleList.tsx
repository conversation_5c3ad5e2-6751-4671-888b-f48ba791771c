// TODO 测试
// import { useRequest } from 'ahooks';
// import { createContainer } from 'unstated-next';
// import { Role } from '../models/Role';
// import service from '../services/authorizeManage.service';

// export default createContainer(() => {
//     const req = useRequest(service.getList);
//     const data = req.data?.data || [];
//     const dict: Record<number, Role> = {};
//     for (let x of data) {
//         dict[x.id] = x;
//     }
//     return {
//         list: data,
//         value: dict,
//         loading: req.loading,
//     }
// })
