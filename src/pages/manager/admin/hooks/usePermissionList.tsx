// TODO 测试
// import { useRequest } from 'ahooks';
// import { createContainer } from 'unstated-next';
// import service from '../services/authorizeManage.service';
// import { useGlobalStateSetter } from '@/utils/globalStateTree';
// import { useEffect } from 'react';

// const usePermissionList = createContainer(() => {
//     const req = useRequest(service.getPermsList);
//     const [setGlobalState] = useGlobalStateSetter();

//     const result = {
//         dict: req.data?.data || {},
//         loading: req.loading,
//     };

//     useEffect(() => {
//         setGlobalState('permissionList', result);
//     }, [req.data]);

//     return result;
// })

// export default usePermissionList;
