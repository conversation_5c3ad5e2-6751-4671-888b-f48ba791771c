import { CustomResponse } from '@/models/common/CustomResponse';
import service from '@/utils/service';
import { useRequest } from 'ahooks';
import { createContainer } from 'unstated-next';
import { FeishuUser } from '../models/FeishuUser';

export default createContainer(() => {
    const req = useRequest(() => service.get<CustomResponse<FeishuUser[]>>('/feishu/users'));

    const data = req.data?.data?.sort((a, b) => a.name.localeCompare(b.name)) || [];
    return {
        list: data,
        loading: req.loading,
    };
});
