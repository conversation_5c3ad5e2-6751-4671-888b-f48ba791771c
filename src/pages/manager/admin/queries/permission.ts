import { message } from 'antd';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import { PermissionEndPointWriteDTO, PermissionWriteDTO } from '../models/Permission';
import service from '../services/permission.service';

const queryKey = 'permission';

export const usePermissionQuery = () => {
    return useSWR(queryKey, async () => {
        const response = await service.getList();
        return response.data;
    });
};

const getQueryKey = (id: number | undefined) => {
    let key: [string, number] | null = null;
    if (id != null) key = [queryKey, id];

    return key;
};

export const useSinglePermissionQuery = (id: number | undefined) => {
    return useSWR(getQueryKey(id), async ([_, id]) => {
        const response = await service.get(id);
        return response.data;
    });
};

export const useCreatePermissionMutation = () => {
    return useSWRMutation(
        queryKey,
        async (_, { arg }: { arg: { data: PermissionWriteDTO } }) => {
            const response = await service.create(arg.data);
            return response.data;
        },
        { onSuccess: () => message.success('创建成功') },
    );
};

export const useUpdatePermissionMutation = () => {
    return useSWRMutation(
        queryKey,
        async (_, { arg }: { arg: { id: number; data: PermissionWriteDTO } }) => {
            const response = await service.update(arg.id, arg.data);
            return response.data;
        },
        { onSuccess: () => message.success('更新成功') },
    );
};

export const useSetPermissionEndPointsMutation = () => {
    return useSWRMutation(
        queryKey,
        async (_, { arg }: { arg: { id: number; data: PermissionEndPointWriteDTO } }) => {
            const response = await service.setEndPoints(arg.id, arg.data);
            return response.data;
        },
        { onSuccess: () => message.success('更新成功') },
    );
};

export const useDeletePermissionMutation = () => {
    return useSWRMutation(queryKey, (_, { arg }: { arg: { id: number } }) => service.delete(arg.id), {
        onSuccess: () => message.success('删除成功'),
    });
};

export const permissionQueryKey = queryKey;
export const getSinglePermissionQueryKey = getQueryKey;
