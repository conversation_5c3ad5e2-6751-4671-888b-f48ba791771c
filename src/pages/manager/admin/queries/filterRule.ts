import { message } from 'antd';
import useSWRMutation from 'swr/mutation';
import { EndPoint } from '../models/EndPoint';
import { FilterRuleWriteDTO } from '../models/FilterRule';
import { Permission } from '../models/Permission';
import service from '../services/filterRule.service';
import { permissionQueryKey } from './permission';
import useSWR, { useSWRConfig } from 'swr';

const queryKey = 'filterRule' as const;

type FilterRuleParams = { endPoint: EndPoint; permission: Permission };

const getQueryKey = ({ endPoint, permission }: Partial<FilterRuleParams>) => {
    let key: [string, FilterRuleParams] | null = null;
    if (!!endPoint && !!permission) key = [queryKey, { endPoint, permission }];

    return key;
};

export const useAllFilterRulesQuery = () => {
    return useSWR(queryKey, async () => {
        const response = await service.getList();
        return response.data;
    });
};

export const useFilterRulesQuery = (params: Partial<FilterRuleParams>) => {
    return useSWR(getQueryKey(params), async ([_, { endPoint, permission }]) => {
        const response = await service.getList(endPoint.method, endPoint.pattern, permission.id);
        return response.data;
    });
};

export const useCreateFilterRuleMutation = (params: Partial<FilterRuleParams>) => {
    const { mutate } = useSWRConfig();

    return useSWRMutation(
        permissionQueryKey,
        async (_, { arg }: { arg: { data: FilterRuleWriteDTO } }) => {
            const response = await service.create(arg.data);
            return response.data;
        },
        {
            onSuccess: () => {
                message.success('创建成功');
                mutate(getQueryKey(params));
            },
        },
    );
};

export const useDeleteFilterRuleMutation = (params: Partial<FilterRuleParams>) => {
    const { mutate } = useSWRConfig();

    return useSWRMutation(permissionQueryKey, (_, { arg }: { arg: { id: number } }) => service.delete(arg.id), {
        onSuccess: () => {
            message.success('删除成功');
            mutate(getQueryKey(params));
        },
    });
};
