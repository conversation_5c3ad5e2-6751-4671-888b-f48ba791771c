import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import service from '../services/group.service';
import { message } from 'antd';
import { GroupPermissionWriteDTO, GroupUserWriteDTO, GroupWriteDTO, UserGroupWriteDTO } from '../models/Group';
import { sortBy } from 'lodash';

const queryKey = 'group';

export const useGroupQuery = () => {
    return useSWR(queryKey, async () => {
        const response = await service.getList();
        return response.data;
    });
};

export const useCreateGroupMutation = () => {
    return useSWRMutation(
        queryKey,
        async (_, { arg }: { arg: { data: GroupWriteDTO } }) => {
            const response = await service.create(arg.data);
            return response.data;
        },
        { onSuccess: () => message.success('创建成功') },
    );
};

export const useUpdateGroupMutation = () => {
    return useSWRMutation(
        queryKey,
        async (_, { arg }: { arg: { id: number; data: GroupWriteDTO } }) => {
            const response = await service.update(arg.id, arg.data);
            return response.data;
        },
        { onSuccess: () => message.success('更新成功') },
    );
};

export const useDeleteGroupMutation = () => {
    return useSWRMutation(queryKey, (_, { arg }: { arg: { id: number } }) => service.delete(arg.id), {
        onSuccess: () => message.success('删除成功'),
    });
};

export const useSetGroupPermissionMutation = () => {
    return useSWRMutation(
        queryKey,
        async (_, { arg }: { arg: { id: number; data: GroupPermissionWriteDTO } }) => {
            const response = await service.setGroupPermissions(arg.id, arg.data);
            return response.data;
        },
        { onSuccess: () => message.success('更新成功') },
    );
};

export const useSetGroupUsersMutation = () => {
    return useSWRMutation(
        queryKey,
        async (_, { arg }: { arg: { id: number; data: GroupUserWriteDTO } }) => {
            const response = await service.setGroupUsers(arg.id, arg.data);
            return response.data;
        },
        { onSuccess: () => message.success('更新成功') },
    );
};

export const useSetUserGroupsMutation = () => {
    return useSWRMutation(
        queryKey,
        async (_, { arg }: { arg: { data: UserGroupWriteDTO } }) => {
            const response = await service.setUserGroups(arg.data);
            return response.data;
        },
        { onSuccess: () => message.success('更新成功') },
    );
};

export const useGroupTagsQuery = () => {
    return useSWR('groupTag', async () => {
        const response = await service.getTagList();
        return response.data.sort();
    });
};

export const useUsersByGroupTagsQuery = (tags: string[] | undefined) => {
    let key: [string, string[]] | null = null;
    if (!!tags?.length) key = ['usersByTags', tags];

    return useSWR(key, async ([_, tags]) => {
        const response = await service.getUsersByTags(tags);
        return sortBy(response.data, it => it.username);
    });
};
