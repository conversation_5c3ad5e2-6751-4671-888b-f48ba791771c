import useSWR from 'swr';
import service from '../services/endPoint.service';
import useSWRMutation from 'swr/mutation';
import { EndPointWriteDTO, EndPointCategoryWriteDTO } from '../models/EndPoint';
import { message } from 'antd';

const queryKey = 'endPoint' as const;
const hasAccessQueryKey = 'endPointHasAccess' as const;

export const useEndPointQuery = () => {
    return useSWR(queryKey, async () => {
        const response = await service.getList();
        return response.data;
    });
};

export const useSingleEndPointQuery = (method: string | undefined, pattern: string | undefined) => {
    let key: [string, { method: string; pattern: string }] | null = null;
    if (!!method && !!pattern) key = [queryKey, { method, pattern }];

    return useSWR(key, async ([_, { method, pattern }]) => {
        const response = await service.get(method, pattern);
        return response.data;
    });
};

export const useHasAccessQuery = (method: string | undefined, pattern: string | undefined) => {
    let key: [string, { method: string; pattern: string }] | null = null;
    if (!!method && !!pattern) key = [hasAccessQueryKey, { method, pattern }];

    return useSWR(key, async ([_, { method, pattern }]) => {
        const response = await service.hasAccess(method, pattern);
        return response.data;
    });
};

// 更新api信息的请求
export const useUpdateEndpointMutation = () => {
    return useSWRMutation(
        queryKey,
        async (_, { arg }: { arg: { data: EndPointWriteDTO } }) => {
            const response = await service.update(arg.data);
            return response.data;
        },
        { onSuccess: () => message.success('更新成功') },
    );
};

// 批量设置分类的请求
export const useSetEndPointsCategoryMutation = () => {
    return useSWRMutation(
        queryKey,
        async (_, { arg }: { arg: { data: EndPointCategoryWriteDTO } }) => {
            const response = await service.setCategory(arg.data);
            return response.data;
        },
        { onSuccess: () => message.success('更新成功') },
    );
};

export const endPointQueryKey = queryKey;
