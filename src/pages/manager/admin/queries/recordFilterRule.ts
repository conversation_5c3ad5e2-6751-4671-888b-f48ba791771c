import { message } from 'antd';
import useSWR, { useSWRConfig } from 'swr';
import useSWRMutation from 'swr/mutation';
import { EndPoint } from '../models/EndPoint';
import { FilterRuleWriteDTO } from '../models/FilterRule';
import { Permission } from '../models/Permission';
import service from '../services/recordFilterRule.service';
import { permissionQueryKey } from './permission';

const queryKey = 'recordFilterRule' as const;

type FilterRuleParams = { endPoint: EndPoint; permission: Permission };

const getQueryKey = ({ endPoint, permission }: Partial<FilterRuleParams>) => {
    let key: [string, FilterRuleParams] | null = null;
    if (!!endPoint && !!permission) key = [queryKey, { endPoint, permission }];

    return key;
};

export const useAllRecordFilterRulesQuery = () => {
    return useSWR(queryKey, async () => {
        const response = await service.getList();
        return response.data;
    });
};

export const useRecordFilterRulesQuery = (params: Partial<FilterRuleParams>) => {
    return useSWR(getQueryKey(params), async ([_, { endPoint, permission }]) => {
        const response = await service.getList(endPoint.method, endPoint.pattern, permission.id);
        return response.data;
    });
};

export const useCreateRecordFilterRuleMutation = (params: Partial<FilterRuleParams>) => {
    const { mutate } = useSWRConfig();

    return useSWRMutation(
        permissionQueryKey,
        async (_, { arg }: { arg: { data: FilterRuleWriteDTO } }) => {
            const response = await service.create(arg.data);
            return response.data;
        },
        {
            onSuccess: () => {
                message.success('创建成功');
                mutate(getQueryKey(params));
            },
        },
    );
};

export const useDeleteRecordFilterRuleMutation = (params: Partial<FilterRuleParams>) => {
    const { mutate } = useSWRConfig();

    return useSWRMutation(permissionQueryKey, (_, { arg }: { arg: { id: number } }) => service.delete(arg.id), {
        onSuccess: () => {
            message.success('删除成功');
            mutate(getQueryKey(params));
        },
    });
};
