import useSWR from 'swr';
import service from '@/utils/service';

const queryKey = 'sample';

type SampleQueryParams = {
    url: string;
    method: string;
    params?: any;
    data?: any;
    enabled?: boolean;
};

const getQueryKey = ({ url, method, enabled, ...rest }: Partial<SampleQueryParams>) => {
    let key: [string, SampleQueryParams] | null = null;
    if (!!url && !!method && !!enabled) key = [queryKey, { url, method, enabled, ...rest }];

    return key;
};

export const useSampleQuery = (params: Partial<SampleQueryParams>) => {
    const key = getQueryKey(params);

    return useSWR(key, ([_, { url, method, params, data }]) => service[method.toLowerCase()](url, { data, params }));
};
