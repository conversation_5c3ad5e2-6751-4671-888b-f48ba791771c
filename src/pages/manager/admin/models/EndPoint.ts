export type EndPoint = {
    method: string;
    pattern: string;
    name?: string;
    description?: string;
    category?: string;
    publicToAll: boolean;
    allowFetchSample: boolean;
    permissions: string[];
};

export type EndPointId = {
    method: string;
    pattern: string;
};

// 更新api基本信息的表单
export type EndPointWriteDTO = {
    method: string;
    pattern: string;
    name?: string;
    description?: string;
    category: string;
};

// 批量更新category的表单
export type EndPointCategoryWriteDTO = {
    category: string;
    endPoints: EndPointId[];
};
