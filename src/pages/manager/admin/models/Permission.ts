import { EndPoint, EndPointId } from './EndPoint';
import { FilterRule } from './FilterRule';

export type Permission = {
    id?: number;
    name?: string;
    description?: string;
    category?: string;
    endPoints: EndPoint[];
    filterRules: FilterRule[];
    recordFilterRules: FilterRule[];
    groups: string[];
};

export type PermissionWriteDTO = {
    name?: string;
    description?: string;
};

export type PermissionEndPointWriteDTO = {
    endPointIds: EndPointId[];
};
