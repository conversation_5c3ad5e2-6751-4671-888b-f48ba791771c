import { Perm } from '@/models/account/Perm';
import { FieldTitle, getFields, Validate } from '@/utils/fieldProperty';
import * as Field from '@/utils/fieldUtils';
import { Convertor } from '@/utils/io';

@Convertor
export class Role {
    id: number;

    @FieldTitle('角色名称')
    @Field.Text()
    @Validate({ required: true })
    name: string;

    @FieldTitle('角色描述')
    @Field.Text()
    description: string;

    @FieldTitle('角色权限')
    @Field.Enum<Perm>({
        injector: 'permissionList',
        render: val => val.name,
        multiple: true,
    })
    permList: string[];
}

export const roleFields = getFields(Role);
