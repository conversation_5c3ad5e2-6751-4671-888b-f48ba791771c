import { User } from '@/models/account/User';
import { Permission } from './Permission';

export type Group = {
    id?: number;
    name?: string;
    description?: string;
    tags?: string[];
    ownerId?: number;
    ownerName?: string;
    permissions: Permission[];
    users: User[];
};

export type GroupWriteDTO = {
    name?: string;
    description?: string;
};

export type GroupPermissionWriteDTO = {
    permissionIds: number[];
};

export type GroupUserWriteDTO = {
    userIds: number[];
};

export type UserGroupWriteDTO = {
    userId: number;
    groupIds: number[];
};

export type GroupTag = {
    name: string;
    description: string;
};
