import React from 'react';
import { Tag } from 'antd';
import { FieldTitle, getFields, Render } from '@/utils/fieldProperty';
import * as Field from '@/utils/fieldUtils';
import { Convertor } from '@/utils/io';
import { DetailedUser } from '@/models/account/User';
import { Role } from './Role';
import { Group } from './Group';

const e = React.createElement;

const renderRoles = (val: Role[]) =>
    e(
        React.Fragment,
        null,
        val.map(x => e(Tag, { key: x.id }, x.name)),
    );

@Convertor
export class AdminUser extends DetailedUser {
    // @FieldTitle('角色')
    // @Render(renderRoles)
    // roles?: Role[];

    // TODO 测试
    @FieldTitle('用户组')
    @Field.Dict({ field: 'user.group', multiple: false, valueType: 'name' })
    groups: string[];

    @FieldTitle('权限')
    @Field.Text()
    authorities?: { authority: string }[];
}

export const adminUserFields = getFields(AdminUser);
