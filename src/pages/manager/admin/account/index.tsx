import React from 'react';
import { Button } from 'antd';
import { CrudTable, useCrudTableAction } from '@/components/CrudTable_V2';
import { DrawerForm, FormTrigger } from '@/components/TriggerableForm';
import { mapAntdTable } from '@/utils/fieldProperty';
import { AdminUser, adminUserFields } from '../models/AdminUser';
import UserCreateForm from './components/UserCreateForm';
import ContentForm from './components/ContentForm';
import service from '../services/adminAccount.service';
import useFeishuUserList from '../hooks/useFeishuUserList';
import CollapseItems from '@/components/CollapseItems';

export default () => {
    const actionRef = useCrudTableAction<AdminUser>();

    const columns = mapAntdTable(adminUserFields, [
        { dataIndex: 'username', allowSort: true, allowFilter: true },
        { dataIndex: 'realName', allowSort: true, allowFilter: true },
        { dataIndex: 'idCardNumber', allowSort: true, allowFilter: true },
        { dataIndex: 'idCardValidUntil', allowSort: true, allowFilter: true },
        { dataIndex: 'phone', allowSort: true, allowFilter: true },
        { dataIndex: 'mail', allowSort: true, allowFilter: true },
        { dataIndex: 'valid', allowSort: true, allowFilter: true },
        { dataIndex: 'groups', title: '用户组', allowSort: true, allowFilter: true },
        { dataIndex: 'createTime', allowSort: true, allowFilter: true },
        // TODO 测试
        // { dataIndex: 'roles' },
        {
            title: '操作',
            key: 'option',
            valueType: 'option',
            doNotExport: true,
            render: (_, record) => [
                <FormTrigger key="edit" id={record.id} data={record}>
                    <a>详情</a>
                </FormTrigger>,
            ],
        },
    ]);

    return (
        // TODO 测试 不需要传入roleList了，不再使用
        // <useRoleList.Provider>
        <useFeishuUserList.Provider>
            <DrawerForm<AdminUser>
                name="accountForm"
                createTitle=""
                editTitle={data => `用户 ${data?.realName || data?.username} 详情`}
                afterUpdate={data => actionRef.update(data.id, data)}
                formContents={<ContentForm />}
                width={500}
                partitioned
                // onGetDetail={async user => {
                //     user['roleIds'] = user.roles.map(x => x.id);
                //     return user;
                // }}
            >
                <CrudTable<AdminUser>
                    title="用户"
                    service={service}
                    recordInsertAt="last"
                    actionRef={actionRef}
                    columns={columns}
                    search={false}
                    fields={adminUserFields}
                    simpleSearches={
                        <>
                            {adminUserFields.searchItems.username({})}
                            {adminUserFields.searchItems.realName({})}
                        </>
                    }
                    toolBarRender={() => [
                        <UserCreateForm
                            key="create"
                            trigger={
                                <Button type="primary" key="create">
                                    创建用户
                                </Button>
                            }
                            onFinish={actionRef.create}
                        />,
                    ]}
                    rowKey="id"
                    newVersion
                />
            </DrawerForm>
        </useFeishuUserList.Provider>
        // </useRoleList.Provider>
    );
};
