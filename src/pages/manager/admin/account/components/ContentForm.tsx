import React from 'react';
import { Button, Form, Transfer, Badge, Space, message } from 'antd';
import { ModalForm, ProFormText } from '@ant-design/pro-form';
import { mapIsPromiseSucceed } from '@/utils';
import { AdminUser } from '../../models/AdminUser';
import service from '../../services/adminAccount.service';
import ProfileItems from './ProfileItems';
import styles from '../../../common.less';
import { FormLayout, MyForm, SubmitButton, useFormData } from '@/components/MyForm';
import { NormalFormOperation } from '@/components/ControlledForm';

function ResetPwdButton(props: { id: number }) {
    const { id } = props;

    return (
        <ModalForm
            title="重置密码"
            width={376}
            trigger={<Button>重置密码</Button>}
            onFinish={mapIsPromiseSucceed(async val => {
                await service.resetPwd(id, val.newPwd);
                message.success('重置密码成功');
            })}
        >
            <ProFormText isList={false} width="md" label="新密码" name="newPwd" />
        </ModalForm>
    );
}

export default function ContentForm() {
    const { data: user } = useFormData<AdminUser>();
    // TODO 测试 修改角色
    // const { list: roles } = useRoleList.useContainer();

    return (
        <div className={styles.formDrawer}>
            <h3>
                <span>用户状态</span>
                {user?.valid ? (
                    <span className={styles.subTitleAttach}>
                        <Badge status="processing" />
                        已激活
                    </span>
                ) : (
                    <span className={styles.subTitleAttach}>
                        <Badge status="default" />
                        未激活
                    </span>
                )}
            </h3>
            <MyForm name="accountStateForm">
                <Space>
                    {user?.id !== 1 && (
                        <SubmitButton
                            doNotClose
                            onSubmit={async () => {
                                const response = await service.setValid(user.id, !user.valid);
                                return [response.data, response.message];
                            }}
                        >
                            {user?.valid ? '反激活' : '激活'}
                        </SubmitButton>
                    )}
                    <ResetPwdButton id={user?.id} />
                </Space>
            </MyForm>

            <h3>个人信息</h3>
            <MyForm name="accountProfileForm">
                <FormLayout value={{ labelWidth: 100 }}>
                    <ProfileItems />
                    <NormalFormOperation doNotClose onUpdate={service.updateProfile} />
                </FormLayout>
            </MyForm>
            {/* TODO 测试 移除角色相关 */}
            {/* {user?.id !== 1 && (
                <>
                    <h3>角色分配</h3>
                    <MyForm name="accountRolesForm">
                        <Form.Item name="roleIds" valuePropName="targetKeys">
                            <Transfer
                                dataSource={roles}
                                rowKey={record => record.id as any}
                                render={record => record.name}
                            />
                        </Form.Item>
                        <NormalFormOperation doNotClose onUpdate={(id, val) => service.setRoles(id, val.roleIds)} />
                    </MyForm>
                </>
            )} */}
        </div>
    );
}
