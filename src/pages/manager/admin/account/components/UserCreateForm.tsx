import PortalDrawerForm from '@/components/PortalDrawerForm';
import { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { Checkbox, Form, message } from 'antd';
import React from 'react';
import useFeishuUserList from '../../hooks/useFeishuUserList';
import { AdminUser } from '../../models/AdminUser';
import service from '../../services/adminAccount.service';

export interface UserCreateFormProps {
    onFinish: (val: AdminUser) => Promise<void>;
    trigger: React.ReactNode;
}

export default (props: UserCreateFormProps) => {
    const { list, loading } = useFeishuUserList.useContainer();

    const { onFinish, trigger } = props;
    return (
        <PortalDrawerForm
            layout="horizontal"
            title="创建用户"
            width={500}
            trigger={trigger}
            onFinish={async (data: any) => {
                try {
                    const newUser = await service.createUserFeishu(data);
                    onFinish(newUser.data);
                    message.success('创建用户成功');
                    return true;
                } catch (e) {
                    message.error(e.message || e);
                    return false;
                }
            }}
        >
            <ProFormSelect
                showSearch
                width="l"
                name="openId"
                label="飞书用户"
                rules={[{ required: true }]}
                options={list?.map(({ openId, name }) => ({ label: name, value: openId }))}
                fieldProps={{ optionFilterProp: 'label', loading }}
            />
            <ProFormText isList={false} width="l" name="username" label="用户名" rules={[{ required: true }]} />
            <Form.Item name="valid" label="初始是否激活" valuePropName="checked" initialValue={true}>
                <Checkbox />
            </Form.Item>
        </PortalDrawerForm>
    );
};
