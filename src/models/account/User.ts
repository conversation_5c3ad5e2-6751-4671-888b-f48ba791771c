import { Dayjs as Moment } from 'dayjs';
import { FieldTitle, getFields } from '@/utils/fieldProperty';
import * as Field from '@/utils/fieldUtils';
import { Convertor } from '@/utils/io';

@Convertor
export class User {
    id: number;

    @FieldTitle('用户名')
    @Field.Text()
    username: string;

    @FieldTitle('真实姓名')
    @Field.Text()
    realName: string;

    @FieldTitle('证件号')
    @Field.Text()
    idCardNumber: string;

    @FieldTitle('证件有效期')
    @Field.Date({ searchRange: true })
    idCardValidUntil: Moment;

    @FieldTitle('联系电话')
    @Field.Text()
    phone: string;

    @FieldTitle('邮箱')
    @Field.Text()
    mail: string;
}

export const userFields = getFields(User);

@Convertor
export class DetailedUser extends User {
    @FieldTitle('密码是否需要重置')
    pwdResetRequired: boolean;

    @FieldTitle('是否激活')
    @Field.Boolean()
    valid: boolean;

    @FieldTitle('创建时间')
    @Field.Date({ searchRange: true })
    createTime: Moment;

    // @FieldTitle('拥有权限')
    // allPerms?: string[];
    // TODO 测试
    @FieldTitle('拥有标签')
    @Field.Text()
    allTags: string[];

    superAdmin: boolean;
}

export const detailedUserFields = getFields(DetailedUser);
