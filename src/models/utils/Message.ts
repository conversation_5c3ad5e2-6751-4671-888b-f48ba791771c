import { Dayjs as Moment } from 'dayjs';
import { Convertor } from '@/utils/io';
import * as Field from '@/utils/fieldUtils';

@Convertor
export class Message {
    id: number;

    title: string;

    type: string;

    content: string;

    link: string;

    @Field.Date()
    startDate: Moment;

    @Field.Date()
    expireDate: Moment;

    priority: number;

    requireHandle: boolean;

    read: boolean;

    handled: boolean;

    activityId: string;

    instanceId: string;

    handleable: boolean;

    createTime: string;

    updateTime: string;

    createUserId: number;
}
