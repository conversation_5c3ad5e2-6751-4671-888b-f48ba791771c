import { field } from '@/utils/newFieldProperty';
import { EntityBase, entityBaseField } from './EntityBase';

export class WfEntityBase extends EntityBase {
    instanceId: string;

    taskId: string;

    prevVersionId: number;

    isNewest: boolean;

    firstVersionId: number;

    open: boolean;

    isTempStore: boolean;

    initiatorId: number;

    initiatorName: string;

    processComplete: boolean;

    version: number;
}

export const wfEntityBaseField = field.extend(entityBaseField).extend({
    instanceId: field.text(),

    taskId: field.text(),

    prevVersionId: field.number(),

    isNewest: field.bool(),

    firstVersionId: field.number(),

    open: field.bool(),

    isTempStore: field.bool(),

    initiatorId: field.number(),

    initiatorName: field.text(),

    processComplete: field.bool(),

    version: field.number(),
}).field;
