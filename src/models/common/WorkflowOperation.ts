import { Convertor } from '@/utils/io';
import { EntityBase } from './EntityBase';

export type OperationType =
    | 'PROCESS_START'
    | 'PROCESS_COMPLETE'
    | 'TASK_COMPLETE'
    | 'CHANGE_ASSIGNEE'
    | 'CHANGE_UPLOADED_MATERIAL';

@Convertor
export class WorkflowOperation extends EntityBase {
    tableName: string;

    entityId: number;

    procInstanceId: string;

    taskId: string;

    taskDefKey: string;

    taskName: string;

    description: string;

    comment: string;

    type: OperationType;
}
