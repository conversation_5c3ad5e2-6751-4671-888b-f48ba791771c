import { Dayjs as Moment } from 'dayjs';
import { User } from '../account/User';
import { FieldTitle } from '@/utils/fieldProperty';
import * as Field from '@/utils/fieldUtils';
import { field } from '@/utils/newFieldProperty';

export class EntityBase {
    id: number;

    @FieldTitle('创建时间')
    @Field.DateTime()
    createTime: Moment;

    createUserId: number;

    readonly createUser: User;

    @FieldTitle('修改时间')
    @Field.DateTime()
    updateTime: Moment;

    updateUserId: number;

    readonly updateUser: User;
}

export const entityBaseField = field.combine({
    id: field.number(),

    createTime: field.dateTime({ title: '创建时间' }),

    createUserId: field.number(),

    createUser: field.any<User>(),

    updateTime: field.dateTime({ title: '修改时间' }),

    updateUserId: field.number(),

    updateUser: field.any<User>(),
});
