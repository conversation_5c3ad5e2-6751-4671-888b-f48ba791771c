import React, { useState } from 'react';
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';

const limit = 5;

export default ({ items }: { items: React.ReactNode[] }) => {
    const [collapsed, setCollapsed] = useState(true);

    const collapseButton = (() => {
        if (items.length <= limit) return <></>;

        const content = collapsed ? (
            <a>
                <CaretDownOutlined />
                <span>展开</span>
            </a>
        ) : (
            <a>
                <CaretUpOutlined />
                <span>收起</span>
            </a>
        );

        return <div onClick={() => setCollapsed(prev => !prev)}>{content}</div>;
    })();

    return (
        <React.Fragment>
            {(collapsed ? items.slice(0, limit) : items).map((item, index) => (
                <div key={index}>{item}</div>
            ))}
            {collapseButton}
        </React.Fragment>
    );
};
