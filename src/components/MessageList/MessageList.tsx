import { Dayjs as Moment } from 'dayjs';
import React from 'react';
import classnames from 'classnames';
import { List } from 'antd';
import { useCreation } from 'ahooks';
import { compareWithNull, cascadeCompare, compareDate } from '@/utils/comparer';
import { Message } from '@/models/utils/Message';
import { MessageListProps } from './MessageListProps';
import styles from './index.less';

export default function(props: MessageListProps) {
    const { messages, onClick, selectId, shallowIds } = props;

    const sorted = useCreation(() => {
        return [...messages].sort((a, b) =>
            cascadeCompare(
                a,
                b,
                compareWithNull<Message, Moment>(
                    (a, b) => -compareDate(a, b),
                    x => x.startDate,
                ),
                (a, b) => b.id - a.id,
            ),
        );
    }, [messages]);

    return (
        <List
            className={styles.wrapper}
            rowKey="id"
            dataSource={sorted}
            renderItem={item => (
                <List.Item
                    onClick={() => onClick?.(item)}
                    className={classnames(
                        item.id === selectId ? styles.selected : null,
                        shallowIds?.includes(item.id) ? styles.shallow : null,
                    )}
                >
                    <List.Item.Meta title={item.title} description={item.content} />
                </List.Item>
            )}
        />
    );
}
