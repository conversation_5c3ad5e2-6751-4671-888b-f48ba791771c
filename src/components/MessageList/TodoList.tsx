import { Message } from '@/models/utils/Message';
import { cascadeCompare, compareDate, compareWithNull } from '@/utils/comparer';
import { useCreation } from 'ahooks';
import { Badge, Menu } from 'antd';
import { Dayjs as Moment } from 'dayjs';
import { keyBy, uniq } from 'lodash';
import React, { useState } from 'react';
import { MessageListProps } from './MessageListProps';
import styles from './index.less';

const moduleTransform = {
    AppropriatenessManage: '投资人管理',
    BrokerOffline: '产品流程',
    ContractModify: '产品流程',
    DeliveryReceive: '快递管理',
    DigitalSeal: '电子用印',
    FileArchive: '文件归档',
    NewContractReceive: '产品流程',
    OpenDay: '开放日',
    PortfolioAddBrokerSubProc: '产品流程',
    PortfolioAppendBroker: '产品流程',
    PortfolioBuy: '交易管理',
    PortfolioCommonProcess: '产品流程',
    PortfolioContractPrint: '产品流程',
    PortfolioContractReceive: '产品流程',
    PortfolioContractSend: '产品流程',
    PortfolioEditing: '产品流程',
    PortfolioEstablish: '产品流程',
    PortfolioLiquidation: '产品流程',
    PortfolioRedeemDirect: '交易管理',
    PortfolioRedeemDistributed: '交易管理',
    PortfolioSell: '交易管理',
    PortfolioSubscribeDirect: '交易管理',
    PortfolioSubscribeDistributed: '交易管理',
    ReportApproval: '通用审批',
    DividendReport: '产品流程',
    IpoCalendar: '打新',
    NewDigitalSeal: '电子用印(与飞书联动)',
    AutoFileArchive: '电子用印(与飞书联动)',
    PortfolioAddTagsSubProc: '产品流程',
    StonecloudFileDelete: '客户管理',
};

const taskKeyTransform = {
    AppropriatenessManage: '适当性管理',
    BrokerOffline: '产品经纪端销户',
    ContractModify: '合同变更',
    DeliveryReceive: '快递接受',
    DigitalSeal: '电子用印',
    FileArchive: '文件归档',
    NewContractReceive: '合同回收',
    OpenDay: '开放日',
    PortfolioAddBrokerSubProc: '产品增加经纪端',
    PortfolioAppendBroker: '追加经纪商',
    PortfolioBuy: '加仓申请',
    PortfolioCommonProcess: '产品通用流程',
    PortfolioContractPrint: '产品合同印刷',
    PortfolioContractReceive: '产品合同回收子流程',
    PortfolioContractSend: '产品合同寄送',
    PortfolioEditing: '产品编辑',
    PortfolioEstablish: '产品成立',
    PortfolioLiquidation: '产品清盘',
    PortfolioRedeemDirect: '直销赎回申请',
    PortfolioRedeemDistributed: '代销赎回申请',
    PortfolioSell: '减仓申请',
    PortfolioSubscribeDirect: '直销申购申请',
    PortfolioSubscribeDistributed: '代销申购申请',
    ReportApproval: '投价报告审批',
    DividendReport: '生成分红公告',
    IpoCalendar: '打新日历',
    NewDigitalSeal: '电子用印(与飞书联动)',
    AutoFileArchive: '文件归档申请',
    PortfolioAddTagsSubProc: '产品三大标签',
    StonecloudFileDelete: '适当性页面石云同步文件删除',
};

const hiddenTasks = new Set([
    'PortfolioBuy',
    'PortfolioSell',
    'PortfolioSubscribeDistributed',
    'PortfolioRedeemDistributed',
]);

const modules = uniq(Object.values(moduleTransform));

export default function(props: MessageListProps) {
    const [openKeys, setOpenKeys] = useState<string[]>();

    const messages = useCreation(() => {
        if (!props.search) {
            setOpenKeys(modules);
            return props.messages;
        }

        const messages = props.messages?.filter(
            message => message.title.includes(props.search) || message.content.includes(props.search),
        );
        const taskKeys = messages.map(message => message.link?.split(';')?.[1]).filter(Boolean);
        const moduleKeys = uniq(taskKeys.map(taskKey => moduleTransform[taskKey]));
        setOpenKeys(taskKeys.concat(moduleKeys));

        return messages;
    }, [props.messages, props.search]);

    const messageById = useCreation(() => keyBy(messages, message => message.id), [messages]);

    const sorted = useCreation(
        () =>
            messages?.sort((a, b) =>
                cascadeCompare(
                    a,
                    b,
                    compareWithNull(compareDate, x => x.expireDate),
                    compareWithNull<Message, Moment>(
                        (a, b) => -compareDate(a, b),
                        x => x.startDate,
                    ),
                    (a, b) => b.id - a.id,
                ),
            ),
        [messages],
    );

    const grouped = useCreation(() => {
        const messageDict: Record<string, Record<string, Message[]>> = {};

        for (const message of sorted) {
            const links = message.link?.split(';');
            const [_, taskName] = links || [];
            if (!taskName) continue;

            const module = moduleTransform[taskName];
            if (!module) continue;

            if (!messageDict[module]) messageDict[module] = {};
            if (!messageDict[module][taskName]) messageDict[module][taskName] = [];
            messageDict[module][taskName].push(message);
        }

        return messageDict;
    }, [sorted]);

    return (
        <Menu
            openKeys={openKeys}
            onOpenChange={openKeys => setOpenKeys(openKeys)}
            className={styles.menu}
            mode="inline"
            onSelect={({ key }) => props.onClick?.(messageById[key])}
            items={Object.entries(grouped)
                .map(([module, modules]) => {
                    const filteredTasks = Object.entries(modules).filter(([taskName]) => !hiddenTasks.has(taskName));

                    if (filteredTasks.length === 0) return null;
                    return {
                        key: module,
                        label: (
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                <span>{module}</span>
                                <Badge
                                    count={
                                        Object.entries(modules)
                                            .filter(([taskName]) => !hiddenTasks.has(taskName))
                                            .flatMap(([_, msgs]) => msgs).length
                                    }
                                    style={{ marginLeft: 4 }}
                                />
                            </div>
                        ),
                        children: Object.entries(modules)
                            .filter(([taskName]) => !hiddenTasks.has(taskName))
                            .map(([taskName, messages]) => ({
                                key: taskName,
                                label: (
                                    <div style={{ display: 'flex', alignItems: 'center' }}>
                                        <span>{taskKeyTransform[taskName]}</span>
                                        <Badge count={messages.length} style={{ marginLeft: 4 }} />
                                    </div>
                                ),
                                children: messages.map(message => ({
                                    key: message.id,
                                    label: (
                                        <div>
                                            <div>{message.title}</div>
                                            <div
                                                style={{
                                                    lineHeight: '22px',
                                                    color: 'rgba(0, 0, 0, 0.45)',
                                                    paddingBottom: 9,
                                                }}
                                            >
                                                {message.content}
                                            </div>
                                        </div>
                                    ),
                                })),
                            })),
                    };
                })
                .filter(Boolean)}
        />
    );
}
