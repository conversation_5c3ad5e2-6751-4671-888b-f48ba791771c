@import '~antd/lib/style/themes/default.less';

.wrapper {
    :global(.ant-list-item) {
        padding-right: 16px;
        padding-left: 16px;
        cursor: pointer;
        transition: background 0.3s;
        &:hover {
            background: #fafafa;
        }
    }

    :global(.ant-list-item-action) {
        width: 24px;
        height: 68px;
        margin-left: 10px;
    }
}

.selected {
    padding-left: 14px !important;
    background: @blue-1;
    border-left: 2px solid @blue-6;
    &:hover {
        background: @blue-2 !important;
    }
}

.shallow {
    opacity: 50%;
}

.complete {
    position: absolute;
    top: 0;
    right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 100%;
    cursor: pointer;
    transition: background 0.3s;
}

.hover-selected {
    &:hover {
        background-color: @blue-2 !important;
    }
}

.hover {
    &:hover {
        background-color: #fafafa;
    }
}

.menu {
    :global(.ant-menu-item) {
        text-wrap: wrap !important;
        height: unset !important;
    }
}
