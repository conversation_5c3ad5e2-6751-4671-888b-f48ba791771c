import React from 'react';
import { <PERSON><PERSON>, Col, InputNumber, Radio, Row, Select, Space } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { useControllableValue } from 'ahooks';
import { OneSegment, SegmentedData } from '@/models/common/SegmentedData';
import { RowProps } from 'antd/lib/row';
import { ColProps } from 'antd/lib/col';
import { zip, dropRight } from 'lodash';

interface OneSegmentInputProps {
    value: OneSegment;
    before: OneSegment;
    onChange: (val: OneSegment) => void;
    rangeUnit: string;
    valueUnit: string | string[];
    defaultValueUnit?: string;
    readonly?: boolean;
}

const baseAddValue: OneSegment = Object.freeze({
    rangeTo: null,
    includes: false,
    value: null,
});

function ValueInput(props: OneSegmentInputProps) {
    const { value, onChange, valueUnit, defaultValueUnit, readonly } = props;

    const trueValue = value || { ...baseAddValue, valueType: defaultValueUnit };

    if (readonly) {
        return (
            <>
                {trueValue.value}
                {Array.isArray(valueUnit) ? trueValue.valueType || defaultValueUnit : valueUnit}
            </>
        );
    }

    return Array.isArray(valueUnit) ? (
        <Radio.Group
            value={trueValue.valueType || defaultValueUnit}
            onChange={val => onChange({ ...trueValue, value: null, valueType: val.target.value })}
        >
            {valueUnit.map(x => (
                <Radio key={x} value={x}>
                    <InputNumber
                        style={{ marginRight: 6 }}
                        value={trueValue.valueType == x ? trueValue.value : null}
                        onChange={(val: number) => onChange({ ...trueValue, value: val, valueType: x })}
                    />
                    {x}
                </Radio>
            ))}
        </Radio.Group>
    ) : (
        <Space>
            <InputNumber value={trueValue.value} onChange={(val: number) => onChange({ ...trueValue, value: val })} />
            <div>{valueUnit}</div>
        </Space>
    );
}

function OneSegmentInput(props: OneSegmentInputProps) {
    const { value, before, onChange, rangeUnit, readonly } = props;

    const trueValue = value || { ...baseAddValue };

    return (
        <Space>
            <div>
                {before.rangeTo}
                {before.rangeTo ? `${rangeUnit}（${before.includes ? '不含' : '含'}）` : ''} 至
            </div>
            {readonly ? (
                <span>{trueValue.rangeTo}</span>
            ) : (
                <InputNumber
                    value={trueValue.rangeTo}
                    onChange={(val: number) => onChange({ ...trueValue, rangeTo: val })}
                />
            )}
            <div>{rangeUnit}</div>
            {readonly ? (
                <span>（{trueValue.includes ? '含' : '不含'}）</span>
            ) : (
                <Select
                    value={trueValue.includes ? '1' : '0'}
                    onChange={val => onChange({ ...trueValue, includes: val !== '0' })}
                >
                    <Select.Option value="0">不含</Select.Option>
                    <Select.Option value="1">含</Select.Option>
                </Select>
            )}
            <div>：</div>
            <ValueInput {...props} />
        </Space>
    );
}

function TailInput(props: OneSegmentInputProps) {
    const { before, rangeUnit } = props;

    return (
        <Space>
            {before.rangeTo ? (
                <>
                    <div>
                        {before.rangeTo}
                        {rangeUnit}以上（{before.includes ? '不含' : '含'}）
                    </div>
                    <div>：</div>
                </>
            ) : null}
            <ValueInput {...props} />
        </Space>
    );
}

interface SegmentedDataInput {
    value?: SegmentedData;
    defaultValue?: SegmentedData;
    onChange?: (val: SegmentedData) => void;
    rangeUnit?: string;
    valueUnit?: string | string[];
    defaultValueUnit?: string;
    rowProps?: RowProps;
    colProps?: ColProps;
    readonly?: boolean;
}

const baseValue = Object.freeze({
    rangeTo: 0,
    includes: false,
    value: null,
});

function SegmentedDataInput(props: SegmentedDataInput) {
    const { rangeUnit, valueUnit, defaultValueUnit, rowProps, colProps, readonly } = props;
    const [state, setState] = useControllableValue<SegmentedData>(props);
    const realState = state || [{ ...baseAddValue }];
    const pairState = zip(realState, [baseValue, ...dropRight(realState)]);
    const headsState = dropRight(pairState);
    const tail = pairState[pairState.length - 1];

    if (readonly && !state) {
        return null;
    }

    return (
        <Row {...(rowProps || {})}>
            {headsState.map(([x, before], idx) => (
                <Col style={{ marginBottom: 8 }} {...(colProps || {})}>
                    <Space>
                        {!readonly && (
                            <a onClick={() => setState(realState.filter((_, j) => j != idx))}>
                                <DeleteOutlined />
                            </a>
                        )}
                        <OneSegmentInput
                            key={idx}
                            value={x}
                            before={before}
                            onChange={val => setState(realState.map(y => (y !== x ? y : val)))}
                            rangeUnit={rangeUnit}
                            valueUnit={valueUnit}
                            defaultValueUnit={defaultValueUnit}
                            readonly={readonly}
                        />
                    </Space>
                </Col>
            ))}
            <Col style={{ marginBottom: 8 }} {...(colProps || {})}>
                <TailInput
                    value={tail[0]}
                    before={tail[1]}
                    onChange={val => setState([...dropRight(realState), val])}
                    rangeUnit={rangeUnit}
                    valueUnit={valueUnit}
                    defaultValueUnit={defaultValueUnit}
                    readonly={readonly}
                />
            </Col>
            {!readonly && (
                <Col {...(colProps || {})}>
                    <Button onClick={() => setState([...realState, { ...baseAddValue }])}>新增分段</Button>
                </Col>
            )}
        </Row>
    );
}

export default SegmentedDataInput;
