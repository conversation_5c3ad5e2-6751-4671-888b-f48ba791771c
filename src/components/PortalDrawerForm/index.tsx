import React from 'react';
import ReactDOM from 'react-dom';
import { useControllableValue } from 'ahooks';
import { DrawerForm } from '@ant-design/pro-form';
import { DrawerFormProps } from '@ant-design/pro-form/lib/layouts/DrawerForm';

const Portal: React.FunctionComponent<DrawerFormProps> = (props) => {
    return ReactDOM.createPortal(
        <DrawerForm {...props} />,
        document.body
    );
}

const PortalDrawerForm: React.FunctionComponent<DrawerFormProps> = (props) => {
    const { trigger, ...restProps } = props;

    const [visible, setVisible] = useControllableValue<boolean>(
        props,
        { defaultValue: false, valuePropName: 'visible', trigger: 'onVisibleChange' }
    );

    return (<>
        <Portal
            {...restProps}
            visible={visible}
            onVisibleChange={setVisible}
        />
        <div onClick={() => setVisible(!visible)}>{trigger}</div>
    </>)
}

export default PortalDrawerForm;
