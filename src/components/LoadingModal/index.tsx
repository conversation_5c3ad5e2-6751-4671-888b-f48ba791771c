import { message, Modal, ModalProps } from 'antd';
import React, { useState } from 'react';

export type LoadingModalProps = Omit<ModalProps, 'onOk'> & { onOk: () => Promise<any>; onError?: (e: any) => void };

export function LoadingModal(props: LoadingModalProps) {
    const { onOk, onError, onCancel, okButtonProps, ...otherProps } = props;
    const [loading, setLoading] = useState(false);

    return (
        <Modal
            {...otherProps}
            onOk={async () => {
                setLoading(true);
                try {
                    await onOk();
                } catch (e) {
                    onError?.(e);
                }
                setLoading(false);
            }}
            okButtonProps={{
                loading,
                disabled: loading,
                ...(okButtonProps || {}),
            }}
            onCancel={e => {
                if (!loading) {
                    onCancel?.(e);
                }
            }}
        />
    );
}

export type LoadingModalWithButtonProps = LoadingModalProps & {
    renderButton: (onClick: () => void) => React.ReactNode;
};

export function LoadingModalWithButton(props: LoadingModalWithButtonProps) {
    const { renderButton, onOk, ...otherProps } = props;
    const [visible, setVisible] = useState(false);

    return (
        <>
            <LoadingModal
                {...otherProps}
                onOk={async () => {
                    try {
                        await onOk();
                        setVisible(false);
                    } catch (e) {
                        message.error(typeof e === 'string' ? e : e?.message || '提交失败');
                    }
                }}
                onCancel={() => setVisible(false)}
                visible={visible}
            />
            {renderButton(() => setVisible(true))}
        </>
    );
}
