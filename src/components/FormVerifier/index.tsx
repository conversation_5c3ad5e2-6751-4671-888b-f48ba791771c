import React from 'react';
import { Checkbox } from 'antd';
import { MyFormItem } from '@/components/MyForm';

/**
 * 加在表单里，用以完成确认表单的交互
 */
export default function(props: { verifyText: string }) {
    const { verifyText } = props;

    return (
        <MyFormItem
            label={verifyText}
            name="__verify"
            required
            rules={[
                () => ({
                    validator(_, value) {
                        if (value) {
                            return Promise.resolve();
                        } else {
                            return Promise.reject('请确认');
                        }
                    },
                }),
            ]}
            valuePropName="checked"
        >
            <Checkbox />
        </MyFormItem>
    );
}
