import React from 'react';
import { Select, Spin, Tag } from 'antd';
import { useRequest } from 'ahooks';
import { UserOutlined } from '@ant-design/icons';
import { SelectProps } from 'antd/lib/select';
import styles from './index.less';
import { User } from '@/models/account/User';
import useUserList from '@/hooks/useUserList';
import uniqBy from 'lodash/uniqBy';

export type UserSelectProps = SelectProps<number> & {
    permFilter?: string[];
    onChangeObject?: (val: User) => void;
    readonly?: boolean;
};

export default function(props: UserSelectProps) {
    const { permFilter, onChange, onChangeObject, readonly, ...otherProps } = props;

    const userReq = useUserList.useContainer().useUserList(readonly ? [] : permFilter);

    if (readonly) {
        const { value } = props;
        const user = userReq.data?.find(x => x.id === value);

        if (userReq.loading) {
            return <Spin />;
        }

        if (!user) {
            return null;
        } else {
            return <Tag icon={<UserOutlined />}>{user.realName || user.username}</Tag>;
        }
    }

    return (
        <Select
            {...otherProps}
            showSearch
            optionFilterProp="children"
            filterOption={(val, option) => {
                const user: User = option.data;
                return user.username.includes(val) || user.realName?.includes?.(val);
            }}
            loading={userReq.loading}
            onChange={(val: number, option) => {
                onChange?.(val, option);
                if (onChangeObject) {
                    onChangeObject(userReq.data.find(x => x.id === val));
                }
            }}
        >
            {uniqBy(userReq.data, it => it.id)?.map(x => (
                <Select.Option key={x.id} value={x.id} data={x}>
                    <span className={styles.username}>@{x.username}</span>
                    <span>{x.realName}</span>
                </Select.Option>
            ))}
        </Select>
    );
}
