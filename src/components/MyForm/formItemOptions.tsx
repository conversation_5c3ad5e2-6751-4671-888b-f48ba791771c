import { createContext, useContext } from 'react';
import { mixContext } from '@/utils/componentUtils';

export interface FormItemOptions {
    readonly?: boolean;
    alwaysReadonly?: boolean;
}

export const formItemOptionsContext = createContext<FormItemOptions>({
    readonly: false,
    alwaysReadonly: false,
});

export const FormItemSetter = mixContext(
    formItemOptionsContext,
    (outer, inner) => {
        if (outer.alwaysReadonly || inner.alwaysReadonly) {
            return {
                readonly: true,
                alwaysReadonly: true,
            };
        } else {
            return {
                alwaysReadonly: false,
                readonly:
                    inner.readonly == null ? outer.readonly : inner.readonly,
            };
        }
    },
);

export function FormItemGetter(props: {
    children: (value: FormItemOptions) => JSX.Element;
}) {
    const val = useContext(formItemOptionsContext);
    return props.children({
        alwaysReadonly: val.alwaysReadonly,
        readonly: val.alwaysReadonly ? true : val.readonly,
    });
}
