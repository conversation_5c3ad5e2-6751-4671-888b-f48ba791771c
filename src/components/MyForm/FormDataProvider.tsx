import React, { useContext } from 'react';
import { get } from 'lodash';
import subFormContext from './subFormContext';
import { FormOperations, formOperationsContext } from './myFormContext';

export interface FormDataProviderProps<T, TKey = number> {
    id: TKey;
    data: T;
    dataLoading?: boolean;
}

const formDataContext = React.createContext<FormDataProviderProps<any, any>>({
    id: null,
    data: null,
    dataLoading: false,
});

export function FormDataProvider<T, TKey = number>(
    props: React.PropsWithChildren<FormDataProviderProps<T, TKey> & Partial<FormOperations<T>>>,
) {
    const { children, id, data, dataLoading, onCreate, onUpdate } = props;

    return (
        <formDataContext.Provider value={{ id, data, dataLoading }}>
            <formOperationsContext.Provider value={{ onCreate, onUpdate }}>{children}</formOperationsContext.Provider>
        </formDataContext.Provider>
    );
}

export function useFormData<T = any, TKey = number>() {
    const { id, data, dataLoading } = useContext(formDataContext);
    const { fields } = useContext(subFormContext);

    return {
        id: id as TKey,
        data: data as T,
        dataLoading,
        localData: fields.length == 0 ? data : get(data, fields),
    };
}
