import { defaults, get } from 'lodash';
import React, { useContext, useState } from 'react';
import { Button, message, Popconfirm } from 'antd';
import { ButtonProps } from 'antd/lib/button';
import { PopconfirmProps } from 'antd/lib/popconfirm';
import { useParentForm } from './useParentForm';
import { formOperationsContext, formControlContext } from './myFormContext';
import { useFormData } from './FormDataProvider';
import subFormContext from './subFormContext';

export type SubmitBaseProps<T> = {
    validate?: boolean;
    onSubmit: (formData: any, id: number, oldData: T) => Promise<[T, string?]>;
    doNotClose?: boolean;
    afterSubmit?: () => void | Promise<any>;
};

export type SubmitWrapperProps<T> = SubmitBaseProps<T> & {
    children: (onSubmit: () => Promise<void>, loading: boolean) => React.ReactElement;
};

export type SubmitButtonProps<T> = Omit<ButtonProps, 'onClick' | 'onSubmit'> &
    SubmitBaseProps<T> & { confirm?: Omit<PopconfirmProps, 'onConfirm'> };

export function SubmitWrapper<T>(props: SubmitWrapperProps<T>) {
    const { validate, onSubmit, afterSubmit, doNotClose, children } = defaults({ ...props }, { validate: true });

    const { data, id } = useFormData();

    const form = useParentForm();
    const { onCreate, onUpdate } = useContext(formOperationsContext);
    const { setLoading } = useContext(formControlContext);
    const { fields } = useContext(subFormContext);
    const [innerLoading, setInnerLoading] = useState(false);

    const onClick = async () => {
        const afterValidate = async (val: any) => {
            const [result, successMessage] = await onSubmit(fields.length == 0 ? val : get(val, fields), id, data);
            if (message) {
                message.success(successMessage);
            }
            if (!id) {
                onCreate && onCreate(result, { doNotClose });
            } else {
                onUpdate && onUpdate(result, { doNotClose });
            }
        };
        setLoading(true);
        setInnerLoading(true);
        try {
            form.removeCache();
            if (validate) {
                const val = await form.validateFields();
                await afterValidate(val);
            } else {
                await afterValidate(form.getFieldsValue());
            }
            await Promise.resolve(afterSubmit?.());
        } catch (e) {
            if (typeof e === 'string') {
                message.error(e);
            } else if (e.message) {
                message.error(e.message);
            } else {
                message.error('提交出错');
            }
        } finally {
            setLoading(false);
            setInnerLoading(false);
        }
    };

    return children(onClick, innerLoading);
}

export default function<T>(props: SubmitButtonProps<T>) {
    const { validate, onSubmit, afterSubmit, doNotClose, confirm, ...otherProps } = props;

    return (
        <SubmitWrapper validate={validate} onSubmit={onSubmit} afterSubmit={afterSubmit} doNotClose={doNotClose}>
            {(onSubmit, loading) =>
                confirm ? (
                    <Popconfirm {...confirm} onConfirm={onSubmit}>
                        <Button {...otherProps} loading={loading} disabled={loading} />
                    </Popconfirm>
                ) : (
                    <Button {...otherProps} onClick={onSubmit} loading={loading} disabled={loading} />
                )
            }
        </SubmitWrapper>
    );
}
