import React, { useState } from 'react';
import { Form, Spin } from 'antd';
import { FormProps } from 'antd/lib/form';
import { ParentFormProvider } from './useParentForm';
import { useForm, ExtendedFormInstance } from './useForm';
import { useFormData } from './FormDataProvider';
import { useMemorizeForm } from './useMemorizeForm';
import SubForm from './SubForm';
import { formControlContext } from './myFormContext';

export type MyFormProps<T> = Omit<FormProps<T>, 'form' | 'initialValues' | 'name' | 'onFinish'> & {
    name: string;
    part?: string;
    form?: ExtendedFormInstance;
    loading?: boolean;
};

function MyForm<T, TKey = number>(props: React.PropsWithChildren<MyFormProps<T>>) {
    const { children, form, name, part, loading, ...otherProps } = props;

    const { data, id } = useFormData<T, TKey>();

    const formInstance = form || useForm();

    const connectForm = useMemorizeForm();

    const [onValuesChange] = connectForm(id, name, data, formInstance);

    const [innerLoading, setLoading] = useState(false);

    return (
        <ParentFormProvider value={formInstance}>
            <formControlContext.Provider value={{ setLoading }}>
                <Spin spinning={loading || innerLoading}>
                    <Form {...otherProps} form={formInstance} name={name} onValuesChange={onValuesChange}>
                        {!part ? children : <SubForm field={part}>{children}</SubForm>}
                    </Form>
                </Spin>
            </formControlContext.Provider>
        </ParentFormProvider>
    );
}

export default MyForm;
