import { createContext } from 'react';
import { AfterEditParams } from '../TriggerableForm/TriggerableForm';

export type FormOperations<T> = {
    onCreate: (val: T, params: AfterEditParams) => void;
    onUpdate: (val: T, params: AfterEditParams) => void;
};
export const formOperationsContext = createContext<FormOperations<any>>({
    onCreate: () => {},
    onUpdate: () => {},
});

export const formControlContext = createContext({
    setLoading: (val: boolean) => {},
});
