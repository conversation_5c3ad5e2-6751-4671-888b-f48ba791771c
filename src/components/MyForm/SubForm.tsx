import { get } from 'lodash';
import React, { useContext } from 'react';
import subFormContext from './subFormContext';
import ExtraDataProvider, { extraDataContext } from './ExtraDataProvider';

export interface SubFormProps {
    field: string;
}

function SubForm(props: React.PropsWithChildren<SubFormProps>) {
    const { field, children } = props;
    const { fields, formList } = useContext(subFormContext);
    const { enableDataCompare, prevData, nextData } = useContext(extraDataContext);

    return (
        <subFormContext.Provider value={{ fields: [...fields, field], formList }}>
            <ExtraDataProvider
                enableDataCompare={enableDataCompare}
                prevData={get(prevData, field)}
                nextData={get(nextData, field)}
            >
                {children}
            </ExtraDataProvider>
        </subFormContext.Provider>
    );
}

export default SubForm;
