import React, { useContext } from 'react';
import { useEffect, useRef } from 'react';
import { usePersistFn, usePrevious } from 'ahooks';
import { ExtendedFormInstance } from './useForm';

const creatingSymbol = '__reserve_creating_*&^%$#@Z!';
const split = '^&*()%^$#@!~';

type ConnectFormType<TKey = number> = (
    id: TKey,
    formName: string,
    data: any,
    form: ExtendedFormInstance,
) => [() => void];

function getCacheKey<TKey = number>(id: TKey, formName: string): string {
    return (id != null ? id.toString() : creatingSymbol) + split + formName;
}

const context = React.createContext<{ connectForm: ConnectFormType<any> }>({
    connectForm: null,
});

function getHook<TKey = number>() {
    const cache = useRef<any>({});

    const connectForm: ConnectFormType<TKey> = (id, formName, data, form) => {
        const previousId = usePrevious(id);
        const previousFormName = usePrevious(formName);
        if (previousFormName != null && previousFormName !== formName) {
            throw '表单名必须保持一致！';
        }

        // 只有当表单被编辑后才需要做缓存，这里记录一下
        const isDirty = useRef<boolean>(false);

        const onValuesChange = () => {
            isDirty.current = true;
        };

        const cacheById = (id: TKey) => {
            if (isDirty) {
                cache.current[getCacheKey(id, formName)] = form.getFieldsValue();
            }
        };

        useEffect(() => {
            // id变化
            // 将之前数据缓存
            cacheById(previousId);
            // 读取缓存数据，或者直接用data填充表单
            const cacheKey = getCacheKey(id, formName);
            const cached = cache.current[cacheKey];
            form.resetFields();
            if (cached != null) {
                form.setFieldsValue(cached);
                isDirty.current = true;
            } else {
                form.setFieldsValue(data);
                isDirty.current = false;
            }
        }, [id]);

        useEffect(() => {
            return () => {
                // 销毁时缓存当前数据
                cacheById(id);
            };
        }, []);

        useEffect(() => {
            // 如果当前form未被编辑，则表单数据随data变化而变化
            if (!isDirty.current) {
                const cacheKey = getCacheKey(id, formName);
                const cached = cache.current[cacheKey];
                if (cached == null) {
                    form.resetFields();
                    form.setFieldsValue(data);
                }
            }
        }, [data]);

        const removeCache = usePersistFn(() => {
            isDirty.current = false;
            cache.current[getCacheKey(id, formName)] = null;
        });

        const resetAllFields = usePersistFn(() => {
            removeCache();
            form.resetFields();
            form.setFieldsValue(data);
        });

        form.removeCache = removeCache;
        form.resetAllFields = resetAllFields;
        return [onValuesChange];
    };
    return { connectForm };
}

export const MemorizeFormProvider = (props: React.PropsWithChildren<{}>) => {
    const { children } = props;
    const { connectForm } = getHook();

    return <context.Provider value={{ connectForm }}>{children}</context.Provider>;
};

export const useMemorizeForm = () => {
    const { connectForm } = useContext(context);
    return connectForm || getHook().connectForm;
};
