import { get, isEqual } from 'lodash';
import classNames from 'classnames';
import React, { useContext } from 'react';
import { Form, Space } from 'antd';
import { FormItemProps } from 'antd/lib/form';
import subFormContext from './subFormContext';
import { formItemOptionsContext } from './formItemOptions';
import { formLayoutContext } from './FormLayout';
import { extraDataContext } from './ExtraDataProvider';
import styles from './myFormItem.less';

type ReadonlyRendererProps = {
    value?: any;
    checked?: any;
    prevData?: any;
    nextData?: any;
    showCompare?: boolean;
    render?: (val: any) => React.ReactNode;
};

const ReadonlyRenderer = (props: ReadonlyRendererProps) => {
    const { value, checked, prevData, nextData, showCompare, render } = props;
    if (showCompare) {
        return (
            <Space>
                <div className={styles.prevValue}>{render?.(prevData)}</div>
                <div className={styles.afterValue}>{render?.(nextData)}</div>
            </Space>
        );
    } else {
        return <>{render?.(value != null ? value : checked) || null}</>;
    }
};

export type MyFormItemProps = FormItemProps & {
    readonlyRender?: (val: any) => React.ReactNode;
};

const MyFormItem: React.FunctionComponent<MyFormItemProps> = props => {
    const { name, readonlyRender, children, className, getValueProps, rules, ...otherProps } = props;
    const { fields, formList } = useContext(subFormContext);
    const suffix = [...fields, ...(Array.isArray(name) ? name : [name])];
    const { readonly } = useContext(formItemOptionsContext);
    const { labelWidth } = useContext(formLayoutContext);
    const { enableDataCompare, prevData, nextData } = useContext(extraDataContext);

    const prevItem = get(prevData, name);
    const nextItem = get(nextData, name);
    const showCompare = enableDataCompare && !isEqual(prevItem, nextItem);

    return (
        <Form.Item
            {...(formList || {})}
            name={formList ? [formList.name, ...suffix] : suffix}
            fieldKey={formList ? [formList.fieldKey, ...suffix] : null}
            labelCol={labelWidth ? { flex: `${labelWidth}px` } : {}}
            className={classNames(className, showCompare ? styles.compareItem : null, styles.myFormItem)}
            getValueProps={readonly ? null : getValueProps}
            rules={readonly ? null : rules}
            {...otherProps}
        >
            {readonly ? (
                <ReadonlyRenderer
                    showCompare={showCompare}
                    prevData={prevItem}
                    nextData={nextItem}
                    render={readonlyRender || (x => (typeof x === 'string' || typeof x === 'number' ? x : null))}
                />
            ) : (
                children
            )}
        </Form.Item>
    );
};

export default MyFormItem;
