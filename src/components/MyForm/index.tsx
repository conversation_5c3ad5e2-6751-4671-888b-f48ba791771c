export { default as SubForm } from './SubForm';
export { default as MyFormList } from './MyFormList';
export { default as MyFormItem } from './MyFormItem';
export { default as MyForm } from './MyForm';
export { default as SubmitButton, SubmitWrapper } from './SubmitButton';
export { default as FormLayout } from './FormLayout';
export { useForm } from './useForm';
export { FormDataProvider, useFormData } from './FormDataProvider';
export { MemorizeFormProvider } from './useMemorizeForm';
export { useParentForm } from './useParentForm';
export { FormItemSetter, FormItemGetter } from './formItemOptions';
export { default as ExtraDataProvider } from './ExtraDataProvider';

import { useContext } from 'react';
import { FormOperations, formOperationsContext } from './myFormContext';
export function useFormOperations<T>(): FormOperations<T> {
    return useContext(formOperationsContext);
}
