import { Field } from '@/utils/newFieldProperty';
import { Col, ColProps } from 'antd';
import React from 'react';
import MyFormItem, { MyFormItemProps } from './MyFormItem';

export type FieldFormItemProps<T> = MyFormItemProps & {
    field: Field<T>;
    colProps?: ColProps;
    formItemProps?: any;
};

export default function FieldFormItem<T>(props: React.PropsWithChildren<FieldFormItemProps<T>>) {
    const { field, colProps, formItemProps, ...otherProps } = props;

    const el = (
        <MyFormItem
            label={field.title}
            readonlyRender={field.render}
            children={field.formItem?.(formItemProps)}
            name={field.name}
            {...otherProps}
        />
    );

    if (!colProps) {
        return el;
    }

    return <Col {...colProps}>{el}</Col>;
}
