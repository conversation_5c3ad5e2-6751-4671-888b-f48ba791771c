import { Button, Form } from 'antd';
import { get } from 'lodash';
import { ValidatorRule } from 'rc-field-form/lib/interface';
import React, { useContext } from 'react';
import ExtraDataProvider, { extraDataContext } from './ExtraDataProvider';
import { FormItemGetter } from './formItemOptions';
import { formLayoutContext } from './FormLayout';
import { SubFormProps } from './SubForm';
import subFormContext from './subFormContext';

export type MyFormListProps = SubFormProps & {
    children: (remove: () => void, idx: number, length: number) => React.ReactNode;
    createRecord?: Record<string | number, string | number>;
    initialValue?: any;
    rules?: ValidatorRule[];
};

// TODO: data compare 对应关系获取
const MyFormList = (props: MyFormListProps) => {
    const { field, children, createRecord, initialValue, rules } = props;
    const { fields } = useContext(subFormContext);
    const { enableDataCompare, prevData, nextData } = useContext(extraDataContext);
    const { labelWidth } = useContext(formLayoutContext);

    return (
        <Form.List name={[...fields, field]} initialValue={initialValue} rules={rules}>
            {(fields, { add, remove }, { errors }) => (
                <>
                    {fields.map((x, idx) => (
                        <subFormContext.Provider key={x.key} value={{ fields: [], formList: x }}>
                            <ExtraDataProvider
                                enableDataCompare={enableDataCompare}
                                prevData={get(prevData, [field, idx])}
                                nextData={get(nextData, [field, idx])}
                            >
                                {children(() => remove(x.name), idx, fields.length)}
                            </ExtraDataProvider>
                        </subFormContext.Provider>
                    ))}
                    <div style={{ paddingLeft: labelWidth }}>
                        <Form.ErrorList errors={errors} />
                    </div>
                    <FormItemGetter>
                        {({ readonly }) => !readonly && <Button onClick={() => add(createRecord)}>新增</Button>}
                    </FormItemGetter>
                </>
            )}
        </Form.List>
    );
};

export default MyFormList;
