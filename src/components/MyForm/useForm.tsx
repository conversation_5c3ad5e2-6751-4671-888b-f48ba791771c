import { Form } from 'antd';
import { FormInstance } from 'antd/lib/form';

export type ExtendedFormInstance<T = any> = FormInstance<T> & {
    resetAllFields: () => void;
    removeCache: () => void;
};

// 组件外部调用useForm获得一个instance，组件内部再把这个instance和内部状态连接起来
export const useForm = () => {
    const [form] = Form.useForm();
    return {
        ...form,
        resetAllFields: form.resetFields,
        removeCache: () => {},
    } as ExtendedFormInstance;
};
