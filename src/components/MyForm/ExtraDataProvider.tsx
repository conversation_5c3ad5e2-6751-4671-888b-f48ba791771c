import { mixContext } from '@/utils/componentUtils';
import React, { createContext } from 'react';

// T是限定在当前SubForm范围之下的
export interface ExtraDataProviderProps<T> {
    // 是否进行新旧比较展示
    enableDataCompare?: boolean;
    // 旧数据，用于数据新旧比较展示
    prevData?: T;
    // 新数据，用于数据新旧比较展示
    nextData?: T;
}

export const extraDataContext = createContext<ExtraDataProviderProps<any>>({
    enableDataCompare: false,
    prevData: null,
    nextData: null,
});

export default mixContext(extraDataContext);
