import React, { memo } from 'react';
import Swagger<PERSON> from 'swagger-ui-react';
import './index.less';

type Props = {
    path: string;
    method: string;
    spec?: any;
};

const OperationsLayout = ({ getComponent }) => {
    const Operations = getComponent('operations', true);

    return (
        <div className="swagger-ui">
            <Operations />
        </div>
    );
};

const ResponsesPlugin = (props: Props) => ({
    statePlugins: {
        spec: {
            wrapSelectors: {
                taggedOperations: (oriSelector: any) => (state: any, ...args: any) => {
                    const taggedOperations = oriSelector(state, ...args);
                    const { method, path } = props;
                    if (!method || !path) return taggedOperations;

                    const result = taggedOperations
                        .map((taggedOperation: any) => {
                            return taggedOperation.update('operations', (operations: any) =>
                                operations.filter(
                                    (operation: any) =>
                                        operation.get('path') === path &&
                                        operation.get('method').toLowerCase() === method.toLowerCase(),
                                ),
                            );
                        })
                        .filter((taggedOperation: any) => taggedOperation.get('operations').size > 0);

                    return result;
                },
            },
        },
    },
    wrapComponents: { parameters: () => () => <></> },
    components: { OperationsLayout },
});

export default memo(({ spec, ...props }: Props) => {
    return <SwaggerUI spec={spec} docExpansion="full" plugins={[ResponsesPlugin(props)]} layout="OperationsLayout" />;
});
