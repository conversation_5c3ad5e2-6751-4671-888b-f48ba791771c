import { CaretDownFilled, CaretUpFilled } from '@ant-design/icons';
import React, { useState } from 'react';

type Props = {
    min?: number;
    elements: React.ReactNode[];
};

export default (props: Props) => {
    const [collapsed, setCollapsed] = useState(true);

    const min = props.min == null ? 1 : props.min;
    const elements = collapsed ? props.elements.slice(0, min) : props.elements;

    const showCaret = props.elements.length > min;

    return (
        <div>
            {elements.map((element, index) => (
                <div key={index}>{element}</div>
            ))}
            {showCaret && (
                <div>
                    <a onClick={() => setCollapsed(prev => !prev)}>
                        {collapsed ? <CaretDownFilled /> : <CaretUpFilled />}
                        <span>{collapsed ? '展开' : '收起'}</span>
                    </a>
                </div>
            )}
        </div>
    );
};
