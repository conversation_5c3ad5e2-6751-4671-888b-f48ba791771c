import { Ta<PERSON>, Button } from 'antd';
import React, { memo, useEffect, useRef, useState } from 'react';
import { history } from 'umi';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { DeleteTwoTone } from '@ant-design/icons';

interface BreadcrumbsProps {
    openedRoute: {
        path: string;
        title: string;
    }[];
    deleteOpenedRoutes: Function;
    numData: number;
    nowClickTitle: string;
    startFinding: number;
}

interface IOpenedRoute {
    title: string;
    path: string;
}
interface DraggableTabPaneProps extends React.HTMLAttributes<HTMLDivElement> {
    index: React.Key;
    moveNode: (dragIndex: React.Key, hoverIndex: React.Key) => void;
}

// type A = Pick<BreadcrumbsProps,"openedRoute">;
// type B = {
//     openedRoute: {
//         path: string;
//         title: string;
//     }[];
// }
// type C = BreadcrumbsProps['openedRoute']

export default function Breadcrumbs(props: BreadcrumbsProps) {
    const { openedRoute, deleteOpenedRoutes, numData, nowClickTitle, startFinding } = props;
    const [activeKey, setActiveKey] = useState<number>();
    // const [openedRouteArr, setOpenedRouteArr] = useState<IOpenedRoute[]>([]);
    const [openedRouteArr, setOpenedRouteArr] = useState<BreadcrumbsProps['openedRoute']>([]);
    useEffect(() => {
        setOpenedRouteArr(openedRoute);
    }, [openedRoute]);
    useEffect(() => {
        for (let i = 0; i < openedRoute.length; i++) {
            if (openedRoute[i].title == nowClickTitle) {
                setActiveKey(i);
            }
        }
    }, [startFinding]);
    useEffect(() => {
        setActiveKey(openedRoute.length - 1);
    }, [numData]);
    // function onChange(strKey) {
    //     setActiveKey(strKey)
    //     const numValue = parseInt(strKey) - 1
    //     const path = openedRouteArr[numValue].path
    //     history.push(path)
    // }
    // function add() {

    // }
    // function remove(nowKey) {
    //     let finalIndex = ""
    //     deleteOpenedRoutes(nowKey)
    //     const newOpenedRouteArr = openedRouteArr.filter((item, index) => index !== parseInt(nowKey) - 1);
    //     setOpenedRouteArr(newOpenedRouteArr)
    //     if (activeKey < nowKey) {
    //         finalIndex = nowKey
    //     } else {
    //         finalIndex = activeKey
    //     }
    //     setActiveKey(finalIndex - 1)
    //     // onChange(lastIndex)
    // }
    // function onEdit(targetKey: string, action: 'add' | 'remove') {
    //     if (action === 'add') {
    //         add();
    //     } else {
    //         remove(targetKey);
    //     }

    // }
    function onDragEnd(result) {
        const from = result.source.index;
        const to = result.destination?.index;
        if (to == undefined) {
            return;
        }

        const differenceValue = to - from;
        if (to >= activeKey && differenceValue > 0 && from < activeKey) {
            setActiveKey(activeKey - 1);
        } else if (to <= activeKey && from > activeKey) {
            setActiveKey(activeKey + 1);
        }
        if (from == activeKey) {
            setActiveKey(to);
        }
        const changedData = changeArritem(openedRoute, from, to);
        setOpenedRouteArr(changedData);
    }
    function clickTabs(wantShowindex: number) {
        setActiveKey(wantShowindex);
        const path = openedRouteArr[wantShowindex].path;
        history.push(path);
    }
    function changeArritem(array: IOpenedRoute[], startIndex: number, endIndex: number) {
        if (startIndex < 0 || startIndex >= array.length || endIndex < 0 || endIndex >= array.length) {
            throw new Error('超出边界');
        }
        if (startIndex == endIndex) {
            return array;
        }
        const temp = array[startIndex];
        array.splice(startIndex, 1);
        array.splice(endIndex, 0, temp);
        // array[startIndex] = array[endIndex];
        // array[endIndex] = temp;
        return array;
    }

    function deleteTabs(wantDeleteindex: number) {
        let finalIndex;
        deleteOpenedRoutes(wantDeleteindex + 1);
        const newOpenedRouteArr = openedRouteArr.filter((item, index) => index !== wantDeleteindex);
        setOpenedRouteArr(newOpenedRouteArr);
        if (activeKey < wantDeleteindex) {
            finalIndex = activeKey;
        } else {
            finalIndex = activeKey - 1;
        }
        setActiveKey(finalIndex);
    }
    return (
        // <Breadcrumb style={{marginTop:-20}}>
        //     {
        //         openedRoute.map((item)=>
        //             <Breadcrumb.Item style={{backgroundColor:  'lightgray' }}>
        //   <a onClick={()=>{history.push(item.path)} } >{item.title}</a>
        // </Breadcrumb.Item>
        //         )

        //    }
        //   </Breadcrumb>
        <DragDropContext onDragEnd={onDragEnd}>
            <Droppable droppableId="dropable" direction="horizontal">
                {(provided, snapshot) => (
                    <div
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        style={{
                            height: 50,
                            display: 'flex',
                            flexDirection: 'row',
                            overflowX: 'scroll',
                            overflowY: 'hidden',
                        }}
                    >
                        {openedRouteArr.map((item, index) => (
                            <Draggable key={index} draggableId={index + 'd'} index={index}>
                                {provided => (
                                    <div
                                        ref={provided.innerRef}
                                        {...provided.draggableProps}
                                        {...provided.dragHandleProps}
                                        onClick={() => clickTabs(index)}
                                    >
                                        <div
                                            style={{
                                                width: 130,
                                                border: '1px solid #d9d9d9',
                                                marginLeft: 10,
                                                height: '100%',
                                                cursor: 'pointer',
                                                background: activeKey == index ? '#fff' : 'rgba(0, 0, 0, 0.02)',
                                                display: 'flex',
                                                alignItems: 'center',
                                                flexWrap: 'nowrap',
                                                borderRadius: 4,
                                                color: activeKey == index ? '#1677ff' : '',
                                                flexShrink: 0,
                                                justifyContent: 'center',
                                            }}
                                            className=""
                                        >
                                            {item.title}
                                            <span
                                                onClick={e => {
                                                    e.stopPropagation();
                                                    e.nativeEvent.stopImmediatePropagation();
                                                    return deleteTabs(index);
                                                }}
                                                style={{ marginLeft: 7 }}
                                            >
                                                {activeKey == index ? '' : <DeleteTwoTone twoToneColor="red" />}
                                            </span>
                                        </div>
                                    </div>
                                )}
                            </Draggable>
                        ))}
                    </div>
                )}
            </Droppable>
        </DragDropContext>
        // <Tabs
        //     onChange={onChange}
        //     type='editable-card'
        //     items={openedRouteArr.map((item, index) => {
        //         let edit = true
        //         if (activeKey == index + 1) {
        //             edit = false
        //         }
        //         return {
        //             label: item.title,
        //             key: (index + 1).toString(),
        //             closable: edit,
        //         };
        //     })}
        //     activeKey={activeKey}
        //     onEdit={onEdit}
        // >
        // </Tabs>
    );
}
