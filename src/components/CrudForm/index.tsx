import React, { createContext, useContext, useRef, useState } from 'react';
import { Button, Space, Form, Affix, Drawer } from 'antd';
import { SubForm } from '../MyForm';
import useMemorizeForm from './useMemorizeForm';
import { ContextType, getFormTrigger, useTriggered } from './FormTrigger';
import { ParentFormProvider } from '../MyForm/useParentForm';
import styles from './index.less';

const FormContext = React.createContext<ContextType<any>>(null);

export const FormTrigger = getFormTrigger(FormContext);

type FormContainerProps<T> = {
    createTitle: string;
    editTitle: string;
    transparent?: boolean;
    width?: number;
    labelWidth?: number;
    onCreateFinish: (data: T) => Promise<void>;
    onEditFinish: (id: number, data: T) => Promise<void>;
    formContents: React.ReactNode;
    partitioned?: boolean;
};

interface FormContainerComp {
    <T>(
        props: React.PropsWithChildren<FormContainerProps<T>>,
    ): React.ReactElement<any, any>;
}

// 将表单提交数据映射为要提交到服务器的数据
// 第一项为要提交的数据，第二项为是否关闭表单
type SubmitDataTransfer<T> = (data: T, id: number) => [any, boolean];

export const formOperationsContext = createContext<{
    submit: (transfer?: SubmitDataTransfer<any>) => void;
    cancel: () => void;
    reset: () => void;
    oldData: any;
    id: number;
}>({
    submit: () => {},
    cancel: () => {},
    reset: () => {},
    oldData: null,
    id: null,
});

export function useEditing<T = any>(): {
    submit: (transfer?: SubmitDataTransfer<T>) => void;
    cancel: () => void;
    reset: () => void;
    oldData: T;
    id: number;
} {
    return useContext(formOperationsContext);
}

export const FormOperation = () => {
    const { submit, cancel, reset } = useContext(formOperationsContext);

    return (
        <Space>
            <Button onClick={cancel}>取消</Button>
            <Button onClick={reset}>重置</Button>
            <Button type="primary" onClick={() => submit()}>
                确认
            </Button>
        </Space>
    );
};

/**
 * 满屏展示的表单
 */
export const FullScreenFormContainer: FormContainerComp = props => {
    const {
        createTitle,
        editTitle,
        transparent,
        formContents,
        onCreateFinish,
        onEditFinish,
        children,
        labelWidth,
        partitioned,
    } = props;

    const [visible, setVisible] = useState(false);
    const [title, setTitle] = useState('');
    const [editing, showForm] = useTriggered<any>((id, _, fromUserInput) => {
        setTitle(id != null ? editTitle : createTitle);
        if (fromUserInput) {
            setVisible(true);
        }
    });

    const [form, onValuesChange] = useMemorizeForm(editing.id, editing.data);
    const submitTransfer = useRef<SubmitDataTransfer<any>>(null);

    return (
        <FormContext.Provider value={{ editing, showForm }}>
            <div style={visible ? { display: 'none' } : {}}>{children}</div>
            {visible && (
                <Form
                    style={
                        transparent
                            ? { marginBottom: -24 }
                            : {
                                  marginBottom: -24,
                                  background: 'white',
                                  padding: '32px 20px',
                              }
                    }
                    className={styles.form}
                    labelCol={labelWidth ? { flex: `${labelWidth}px` } : {}}
                    form={form}
                    onValuesChange={onValuesChange}
                    onFinish={async (val: any) => {
                        try {
                            const [val1, closeForm] = submitTransfer.current
                                ? submitTransfer.current(val, editing.id)
                                : [val, true];
                            const p =
                                editing.id == null
                                    ? onCreateFinish(val1)
                                    : onEditFinish(editing.id, val1);
                            await p;
                            form.removeCache();
                            if (closeForm) setVisible(false);
                            return true;
                        } catch (e) {
                            return false;
                        }
                    }}
                >
                    <div className={styles.header}>
                        <h2 style={{ display: 'inline-block' }}>{title}</h2>
                    </div>
                    <ParentFormProvider value={form}>
                        <formOperationsContext.Provider
                            value={{
                                submit: transfer => {
                                    submitTransfer.current = transfer;
                                    form.submit();
                                },
                                cancel: () => setVisible(false),
                                reset: () => form.resetFields(),
                                oldData: editing.data,
                                id: editing.id,
                            }}
                        >
                            {formContents}
                            <Affix offsetBottom={0}>
                                <div className={styles.fixedFooter}>
                                    <FormOperation />
                                </div>
                            </Affix>
                        </formOperationsContext.Provider>
                    </ParentFormProvider>
                </Form>
            )}
        </FormContext.Provider>
    );
};

/**
 * 右侧抽屉展示的表单
 */
export const FormContainer: FormContainerComp = props => {
    const {
        createTitle,
        editTitle,
        width,
        formContents,
        onCreateFinish,
        onEditFinish,
        children,
        labelWidth,
        partitioned,
    } = props;

    const [visible, setVisible] = useState(false);
    const [title, setTitle] = useState('');

    const [editing, showForm] = useTriggered<any>((id, _, fromUserInput) => {
        setTitle(id != null ? editTitle : createTitle);
        if (fromUserInput) {
            setVisible(true);
        }
    });

    const [form, onValuesChange] = useMemorizeForm(editing.id, editing.data);
    const submitTransfer = useRef<SubmitDataTransfer<any>>(null);

    return (
        <FormContext.Provider value={{ editing, showForm }}>
            {children}

            <formOperationsContext.Provider
                value={{
                    submit: transfer => {
                        submitTransfer.current = transfer;
                        form.submit();
                    },
                    cancel: () => setVisible(false),
                    reset: () => form.resetFields(),
                    oldData: editing.data,
                    id: editing.id,
                }}
            >
                <Drawer
                    title={title}
                    width={width || 800}
                    visible={visible}
                    onClose={() => setVisible(false)}
                    footer={
                        partitioned ? (
                            <Button onClick={() => setVisible(false)}>
                                关闭
                            </Button>
                        ) : (
                            <FormOperation />
                        )
                    }
                >
                    <Form
                        className={styles.form}
                        labelCol={labelWidth ? { flex: `${labelWidth}px` } : {}}
                        form={form}
                        onValuesChange={onValuesChange}
                        onFinish={async (val: any) => {
                            try {
                                const [val1, closeForm] = submitTransfer.current
                                    ? submitTransfer.current(
                                          { ...editing.data, ...val },
                                          editing.id,
                                      )
                                    : [{ ...editing.data, ...val }, true];
                                const p =
                                    editing.id == null
                                        ? onCreateFinish(val1)
                                        : onEditFinish(editing.id, val1);
                                await p;
                                form.removeCache();
                                if (closeForm) setVisible(false);
                                return true;
                            } catch (e) {
                                return false;
                            }
                        }}
                    >
                        <ParentFormProvider value={form}>
                            {formContents}
                        </ParentFormProvider>
                    </Form>
                </Drawer>
            </formOperationsContext.Provider>
        </FormContext.Provider>
    );
};

export interface FormPartProps<T> {
    // 分部表单编辑数据的什么部分
    part?: string;
    // TODO: reset分部表单
    // boolean: 是否允许该部分reset，若是则按照part来reset；string[]: 这部分要reset的表单项
    // reset?: boolean | string[];
    transferSubmit?: SubmitDataTransfer<T>;
}

/**
 * 分部表单。每一分部可单独提交。
 * TODO: 分部表单部分的dirty mark, 更新等等
 */
export function FormPart<T>(props: React.PropsWithChildren<FormPartProps<T>>) {
    const { part, transferSubmit, children } = props;

    const context = useContext(formOperationsContext);
    const { submit } = context;

    const dom = (
        <formOperationsContext.Provider
            value={{
                ...context,
                submit: childTransfer =>
                    childTransfer
                        ? submit(childTransfer)
                        : submit(transferSubmit),
            }}
        >
            {children}
        </formOperationsContext.Provider>
    );
    return part ? <SubForm field={part}>{dom}</SubForm> : dom;
}
