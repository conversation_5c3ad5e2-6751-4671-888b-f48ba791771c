import { useEffect, useRef } from 'react';
import ProForm from '@ant-design/pro-form';
import { FormInstance } from 'antd/lib/form';
import { usePersistFn, usePrevious } from 'ahooks';

// 以id为key，可以记住已填报数据的form

export type ExtendedFormInstance = FormInstance & {
    removeCache: () => void,
}

const creatingSymbol = Symbol('creating');

function getCacheKey<T>(id: T) {
    return id != null ? id : creatingSymbol;
}

export default function useMemorizeForm<T, TKey = number>(id: TKey, data: T): [ExtendedFormInstance, () => void] {
    const previousId = usePrevious(id);
    const [form] = ProForm.useForm();

    // 对当前正在编辑的数据做缓存
    const cache = useRef<any>({});
    // 只有当表单被编辑后才需要做缓存，这里记录一下
    const isDirty = useRef<boolean>(false);

    const onValuesChange = () => {
        isDirty.current = true;
    }

    useEffect(() => {
        // 缓存当前id所填写数据
        if (isDirty) {
            cache.current[getCacheKey(previousId)] = form.getFieldsValue();
        }
        isDirty.current = false;
        const cacheKey = getCacheKey(id);
        const cached = cache.current[cacheKey];
        if (cached != null) {
            form.resetFields();
            form.setFieldsValue(cached);
        }
    }, [id]);

    useEffect(() => {
        if (!isDirty.current) {
            const cacheKey = getCacheKey(id);
            const cached = cache.current[cacheKey];
            if (cached == null) {
                form.resetFields();
                form.setFieldsValue(data);
            }
        }
    }, [data]);

    const removeCache = usePersistFn(() => {
        isDirty.current = false;
        cache.current[getCacheKey(id)] = null;
        form.resetFields();
        form.setFieldsValue(data);
    });

    const resetFields = removeCache;

    return [{ ...form, removeCache, resetFields }, onValuesChange];
}
