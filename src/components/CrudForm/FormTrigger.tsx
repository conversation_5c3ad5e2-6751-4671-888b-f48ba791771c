import React, { useContext, useEffect, useState } from 'react';
import { usePersistFn } from 'ahooks';

export interface Editing<T, TKey> {
    id: TKey,
    data: T,
}

export type ShowFormFn<T, TKey> = (id: TKey, data: T, fromUserInput: boolean) => void;

export type ContextType<T, TKey = number> = {
    editing: Editing<T, TKey>,
    showForm: ShowFormFn<T, TKey>,
}

export type FormTriggerProps<T, TKey = number> = {
    id?: TKey,
    data?: T,
};

interface FormTriggerComp {
    <T>(props: React.PropsWithChildren<FormTriggerProps<T>>): React.ReactElement<any, any>
}

export function getFormTrigger(context: React.Context<ContextType<any>>): FormTriggerComp {
    return (props) => {
        const { id, data, children } = props;
        const c = useContext(context);

        useEffect(() => {
            if (c?.showForm && c?.editing) {
                const { editing, showForm } = c;
                if (editing.id === id) {
                    showForm(id, data, false);
                }
            }
        }, [id, data]);

        if (c?.showForm) {
            const { showForm } = c;
            return <div onClick={() => showForm(id, data, true)}>{ children }</div>;
        } else {
            return null;
        }
    }
}

export function useTriggered<T, TKey = number>(afterTriggered?: (id: TKey, data: T, fromUserInput: boolean) => void): [Editing<T, TKey>, ShowFormFn<T, TKey>] {
    const [editing, setEditing] = useState<Editing<T, TKey>>({ id: null, data: null });
    const showForm = usePersistFn((id: TKey, data: T, fromUserInput: boolean) => {
        setEditing({ id, data });
        afterTriggered?.(id, data, fromUserInput);
    });
    return [editing, showForm];
}
