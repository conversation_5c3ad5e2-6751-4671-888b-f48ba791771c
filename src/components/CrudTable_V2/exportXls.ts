import xlsx, { utils } from 'xlsx';
import _, { get } from 'lodash';
import { ColumnsState, ProColumns } from '@ant-design/pro-table';

export function getExportData(data: any[], headerFilter: Record<string, any>, columns: ProColumns[]) {
    let result = data;
    if (headerFilter != null) {
        for (let [k, v] of Object.entries(headerFilter)) {
            const splitIndex = k.split(',');
            const column = columns.find(x => x.dataIndex == k || _.isEqual(splitIndex, x.dataIndex) || x.key == k);
            const currentFilter = column.onFilter;
            if (typeof currentFilter == 'function' && v != null) {
                result = result.filter(x => {
                    if (!Array.isArray(v)) {
                        return currentFilter(v, x);
                    } else {
                        for (let item of v) {
                            if (currentFilter(item, x)) {
                                return true;
                            }
                        }
                        return false;
                    }
                });
            }
        }
    }
    return result;
}

export function exportXls(
    columns: ProColumns[],
    colState: Record<string, ColumnsState>,
    data: any[],
    title: string,
    renderNullRequired = false,
    merge = false,
) {
    const filtered = columns.filter(
        (x, idx) => (colState[x.key]?.show == null || colState[x.key].show) && !x['doNotExport'],
    );
    const table: any[][] = [[]];
    for (let x of filtered) {
        table[0].push(x.title?.toString() || '');
    }

    table.push(
        ...data.map((x, idx) => {
            const current = [];
            for (let y of filtered) {
                const val = y.dataIndex ? get(x, y.dataIndex) : y.key ? x[y.key] : null;
                if (val != null || renderNullRequired) {
                    if (y['renderRaw']) {
                        current.push(y['renderRaw'](val, x, idx));
                    } else if (y.render && (val != null || renderNullRequired)) {
                        const rendered = y.render(val, x, idx, null, null);
                        if (typeof rendered !== 'object') {
                            current.push(rendered);
                        } else {
                            current.push(val);
                        }
                    } else {
                        current.push(val);
                    }
                } else {
                    current.push(val);
                }
            }
            return current;
        }),
    );

    const wb = utils.book_new();
    const ws = utils.aoa_to_sheet(table);
    if (merge) {
        let merges = [];
        for (let c = 0; c < 3; c++) {
            // 只处理前三列
            let startRow = 0;
            while (startRow < table.length - 1) {
                let endRow = startRow;
                while (endRow < table.length - 1 && table[endRow + 1][c] === table[startRow][c]) {
                    endRow++;
                }
                if (endRow > startRow) {
                    merges.push({ s: { r: startRow, c: c }, e: { r: endRow, c: c } });
                }
                startRow = endRow + 1;
            }
        }
        ws['!merges'] = merges;
    }
    utils.book_append_sheet(wb, ws, `${title?.slice(0, 29)}列表`);
    const result = xlsx.write(wb, {
        bookType: 'xlsx',
        type: 'base64',
        compression: true,
    });
    // if (merge) {
    //     // let merges = [];
    //     // for (let c = 0; c < table[0].length; c++) {
    //     //     let startRow = 1;
    //     //     while (startRow < table.length) {
    //     //         let endRow = startRow;
    //     //         while (endRow < table.length - 1 && table[endRow][c] === table[endRow + 1][c]) {
    //     //             endRow++;
    //     //         }
    //     //         if (endRow > startRow) {
    //     //             merges.push({ s: { r: startRow, c: c }, e: { r: endRow, c: c } });
    //     //         }
    //     //         startRow = endRow + 1;
    //     //     }
    //     // }
    //     // ws['!merges'] = merges;
    // }

    const link = document.createElement('a');
    link.download = `${title}列表.xlsx`;
    link.href = `data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,${result}`;
    link.click();
    URL.revokeObjectURL(result);
}
