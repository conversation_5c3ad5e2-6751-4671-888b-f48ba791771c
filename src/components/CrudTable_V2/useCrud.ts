import { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { PaginationProps } from 'antd/lib/pagination';
import { usePersistFn } from 'ahooks';
import { useFilterable, Fields } from '@/utils/fieldProperty';
import { StandardService } from '@/utils/StandardService';
import { useGlobalStateSetter } from '@/utils/globalStateTree';
import { SorterResult } from 'antd/lib/table/interface';

export interface UseCrudOptions<T, TKey> {
    title: string;
    service: StandardService<T, TKey>;
    dependencies?: any[];
    fields?: Fields<T>;
    rowKey: string;
    // 新增记录插在第一条还是最后一条
    recordInsertAt: 'first' | 'last';
    // 将数据注册到全局hook
    globalDataHookName?: string;
    // newVersion 为 true，代表create和update的服务端交互已移出table
    newVersion?: boolean;
    // 是否后端分页
    serverPagination?: boolean;
    // 自定义传递给service的过滤项
    customFilters?: any;
    // 自定义前端过滤
    frontendFilters?: ((val: T) => boolean)[];
}

export default function useCrud<T, TKey = number>(options: UseCrudOptions<T, TKey>) {
    const {
        title,
        service,
        dependencies,
        fields,
        recordInsertAt,
        rowKey,
        globalDataHookName,
        newVersion,
        serverPagination,
        customFilters,
        frontendFilters,
    } = options;

    const [loading, setLoading] = useState(false);
    const [pagination, _setPagination] = useState<PaginationProps>({
        pageSize: 20,
        current: 1,
    });
    const [sorter, setSorter] = useState<SorterResult<T>>(null);

    const [data, setData] = useState<T[]>(null);

    const [setGlobalState] = useGlobalStateSetter();
    useEffect(() => {
        if (globalDataHookName) {
            const dict: Record<any, T> = {};
            if (data) {
                for (let x of data) {
                    dict[x[rowKey]] = x;
                }
            }
            setGlobalState(globalDataHookName, {
                dict,
                list: data || [],
            });
        }
    }, [data]);

    const loadData = usePersistFn(async (newPagination?: PaginationProps) => {
        setLoading(true);
        try {
            const submitPagination = newPagination || pagination;
            const response = await service.getList(
                {
                    page: submitPagination.current - 1,
                    pageSize: submitPagination.pageSize,
                    sortKey: sorter?.columnKey as string,
                    sortDesc: sorter?.order == 'descend',
                },
                customFilters,
            );
            setData(response.data);

            // 后端分页需要更新总数
            if (serverPagination) {
                _setPagination({ ...submitPagination, total: response.amount });
            }
        } catch (e) {
            message.error(e?.message || '获取数据失败');
            setData(null);
        } finally {
            setLoading(false);
        }
    });

    const setPagination = usePersistFn((val: PaginationProps) => {
        if (serverPagination) {
            loadData(val);
        } else {
            _setPagination(val);
        }
    });

    const filterableRef = useFilterable<T>({
        dataSource: data,
        filters: fields?.searchFilters || ({} as any),
        frontendFilters,
        onFilterParamsChange: filtered => {
            setPagination({
                ...pagination,
                current: 1,
                total: filtered?.length,
            });
        },
    });

    const onCreate = usePersistFn(async (val: T) => {
        if (!newVersion) {
            setLoading(true);
            try {
                const response = await service.create(val);
                const created = response.data;
                const newDataLen = data.length + 1;
                if (recordInsertAt === 'first') {
                    if (!serverPagination) {
                        setData([created, ...data]);
                    }
                    setPagination({
                        ...pagination,
                        total: newDataLen,
                        current: 1,
                    });
                } else {
                    if (!serverPagination) {
                        setData([...data, created]);
                    }
                    setPagination({
                        ...pagination,
                        total: newDataLen,
                        current: Math.ceil(newDataLen / pagination.pageSize),
                    });
                }
                message.success(response.message || `创建${title}成功`);
            } catch (e) {
                message.error(e?.message || `创建${title}失败`);
                throw e;
            } finally {
                filterableRef.reset();
                setLoading(false);
            }
        } else {
            const created = val;
            const newDataLen = data.length + 1;
            if (recordInsertAt === 'first') {
                if (!serverPagination) {
                    setData([created, ...data]);
                }
                setPagination({ ...pagination, total: newDataLen, current: 1 });
            } else {
                if (!serverPagination) {
                    setData([...data, created]);
                }
                setPagination({
                    ...pagination,
                    total: newDataLen,
                    current: Math.ceil(newDataLen / pagination.pageSize),
                });
            }
        }
    });

    const onUpdate = usePersistFn(async (id: TKey, val: T) => {
        if (!newVersion) {
            setLoading(true);
            try {
                const response = await service.update(id, val);
                const updated = response.data;
                setData(data.map(x => (x?.[rowKey] === id ? updated : x)));
                message.success(response.message || `编辑${title}成功`);
            } catch (e) {
                message.error(e?.message || `编辑${title}失败`);
                throw e;
            } finally {
                setLoading(false);
            }
        } else {
            const updated = val;
            setData(data.map(x => (x?.[rowKey] === id ? updated : x)));
        }
    });

    const onDelete = usePersistFn(async (id: TKey) => {
        setLoading(true);
        try {
            const response = service.delete ? await service.delete(id) : null;
            const isLast = data[data.length - 1]?.[rowKey] === id;
            const newDataLen = data.length - 1;
            setPagination({
                ...pagination,
                total: newDataLen,
                current: isLast ? Math.ceil(newDataLen / pagination.pageSize) : pagination.current,
            });
            if (!serverPagination) {
                setData(data.filter(x => x?.[rowKey] !== id));
            }
            message.success(response?.message || `删除${title}成功`);
        } catch (e) {
            message.error(e?.message || `删除${title}失败`);
            throw e;
        } finally {
            filterableRef.reset();
            setLoading(false);
        }
    });

    const prevCustomFilters = useRef<any>(customFilters);

    useEffect(() => {
        if (prevCustomFilters.current != customFilters) {
            loadData({ ...pagination, current: 1 });
            prevCustomFilters.current = customFilters;
        } else {
            loadData();
        }
    }, [...(dependencies || []), customFilters]);

    const resetSearch = usePersistFn(() => {
        filterableRef.reset();

        const newPagination = { ...pagination, current: 1 };
        _setPagination(newPagination);

        setSorter(null);

        loadData(newPagination);
    });

    return {
        loading,
        pagination,
        setPagination,
        sorter,
        setSorter,
        loadData,
        wholeData: data,
        create: onCreate,
        update: onUpdate,
        delete: onDelete,
        filterable: filterableRef,
        resetSearch,
    };
}
