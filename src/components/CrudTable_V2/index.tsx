import { proxyMethods } from '@/utils';
import { SimpleFilterForm } from '@/utils/fieldProperty';
import { ExportOutlined, ArrowUpOutlined } from '@ant-design/icons';
import ProTable, { ColumnsState, ProTableProps } from '@ant-design/pro-table';
import { useCreation, usePersistFn } from 'ahooks';
import { Button, message, Popconfirm, Tooltip } from 'antd';
import React, { createContext, useContext, useRef, useState } from 'react';
import useCrud, { UseCrudOptions } from './useCrud';

export interface CrudActionRef<T, TKey = number> {
    create: (data: unknown) => Promise<void>;
    update: (id: TKey, data: unknown) => Promise<void>;
    delete: (id: TKey) => Promise<void>;
    refresh: (reset?: boolean) => Promise<void>;
    triggerSearch: () => void;
    resetSearch: () => void;
    resetFilters: () => void;
}

export interface DeleteWrapperProps<TKey = number> {
    id: TKey;
    confirm: React.ReactNode;
    delete?: (id: TKey) => Promise<void>;
}

export type CrudTableProps<T, TKey = number> = {
    actionRef?: CrudActionRef<T, TKey>;
    rowKey?: string;
    simpleSearches?: React.ReactNode;
    newVersion?: boolean;
    serverPagination?: boolean;
    customFilters?: any;
    frontendFilters?: ((val: T) => boolean)[];
} & Omit<UseCrudOptions<T, TKey>, 'rowKey'> &
    Omit<ProTableProps<T, any>, 'title' | 'rowKey' | 'actionRef'>;

type ContextType<T, TKey = number> = {
    actions: CrudActionRef<T, TKey>;
    title: string;
};

const CrudTableContext = createContext<ContextType<any, any>>({
    actions: null,
    title: '',
});

const actionsSymbol = Symbol('crudTableActions');

export { useCrud };

// 随便赋一个初始值，组件渲染之后替换掉
export function useCrudTableAction<T, TKey = number>(): CrudActionRef<T, TKey> {
    return proxyMethods(actionsSymbol);
}

export function DeleteWrapper<TKey = number>(props: React.PropsWithChildren<DeleteWrapperProps<TKey>>) {
    const context = useContext(CrudTableContext);
    if (!context) {
        return null;
    }

    const { actions, title } = context;
    const { id, confirm, children } = props;
    const onDelete = props.delete || actions.delete;

    return (
        <Popconfirm
            title={confirm}
            placement="topRight"
            onConfirm={async () => {
                try {
                    await onDelete(id);
                } catch (e) {
                    message.error(e?.message || `删除${title}错误`);
                }
            }}
        >
            {children}
        </Popconfirm>
    );
}

export function CrudTable<T, TKey = number>(props: CrudTableProps<T, TKey>) {
    const {
        service,
        dependencies,
        title,
        actionRef,
        recordInsertAt,
        rowKey,
        simpleSearches,
        toolBarRender,
        fields,
        globalDataHookName,
        columns,
        newVersion,
        serverPagination,
        customFilters,
        frontendFilters,
        onChange,
        ...tableProps
    } = props;

    const newRowKey = rowKey || 'id';

    const {
        loading,
        loadData,
        pagination,
        setPagination,
        setSorter,
        create,
        update,
        delete: remove,
        filterable,
        resetSearch: resetCrudSearch,
    } = useCrud({
        title,
        service,
        dependencies,
        fields,
        rowKey: newRowKey,
        recordInsertAt,
        globalDataHookName,
        newVersion,
        serverPagination,
        customFilters,
        frontendFilters,
    });

    const handleResetSearch = usePersistFn(() => {
        resetCrudSearch(); // 重置搜索条件
        resetAllFilters(); // 重置筛选器
    });

    const actions = useCreation<CrudActionRef<T, TKey>>(
        () => ({
            create,
            update,
            delete: remove,
            refresh: reset => {
                return loadData(reset ? { ...pagination, current: 1 } : null);
            },
            triggerSearch: filterable.triggerSearch,
            resetSearch: handleResetSearch,
            resetFilters: resetAllFilters,
        }),
        [],
    );

    if (actionRef) {
        actionRef[actionsSymbol] = actions;
    }

    const [columnsState, setColumnsState] = useState<Record<string, ColumnsState>>({});

    const [exporting, setExporting] = useState(false);

    const [headerFilter, setHeaderFilter] = useState<Record<string, any>>({});

    const proTableRef = useRef<ActionType>();

    // 创建重置所有筛选器的方法
    const resetAllFilters = usePersistFn(() => {
        // 1. 重置 ProTable 的筛选状态
        if (proTableRef.current) {
            proTableRef.current.reset();
        }

        // 2. 重置表头筛选状态
        setHeaderFilter({});
    });

    const processedColumns = columns.map(column => {
        const key = column.key || column.dataIndex;
        if (!key) return column;

        return {
            ...column,
            filteredValue: headerFilter[key] || null,
        };
    });

    function getExportData() {
        let result = filterable.filtered;
        if (headerFilter != null) {
            for (let [k, v] of Object.entries(headerFilter)) {
                const column = columns.find(x => x.dataIndex == k || x.key == k);
                const currentFilter = column.onFilter;
                if (typeof currentFilter == 'function' && v != null) {
                    result = result.filter(x => {
                        if (!Array.isArray(v)) {
                            return currentFilter(v, x);
                        } else {
                            for (let item of v) {
                                if (currentFilter(item, x)) {
                                    return true;
                                }
                            }
                            return false;
                        }
                    });
                }
            }
        }
        return result;
    }

    const distributionFeeRateColunmns = [
        ...columns,
        {
            title: '代销机构',
            dataIndex: 'distributorName',
            key: 'distributorName',
        },
        {
            title: '固定销售服务费类型',
            dataIndex: 'fixedSaleServiceType',
            key: 'fixedSaleServiceType',
        },
        {
            title: '固定销售服务费费率',
            dataIndex: 'fixedSaleServiceRate',
            key: 'fixedSaleServiceRate',
        },
        {
            title: '浮动销售服务费分成费率',
            dataIndex: 'floatingSaleServiceRate',
            key: 'floatingSaleServiceRate',
        },
        {
            title: '赎回费返费费率',
            dataIndex: 'redemptionServiceRate',
            key: 'redemptionServiceRate',
        },
        {
            title: '备注',
            dataIndex: 'remark',
            key: 'remark',
        },
    ];

    return (
        <CrudTableContext.Provider value={{ actions, title }}>
            <filterable.Provider>
                <ProTable<T>
                    ref={proTableRef}
                    headerTitle={`${title}列表`}
                    {...tableProps}
                    columns={processedColumns}
                    rowKey={newRowKey}
                    dataSource={filterable.filtered}
                    loading={loading}
                    options={{
                        reload: () => loadData(),
                    }}
                    pagination={pagination}
                    onChange={(pagination, filter, sorter) => {
                        setPagination(pagination);
                        setHeaderFilter(filter);
                        setSorter(Array.isArray(sorter) ? sorter[0] : sorter);
                    }}
                    toolBarRender={
                        toolBarRender === false
                            ? false
                            : (action, rows) => [
                                  <SimpleFilterForm key="search">{simpleSearches}</SimpleFilterForm>,
                                  ...(toolBarRender?.(action, rows) || []),
                                  <Tooltip title="导出" key="reserve__export">
                                      <ExportOutlined
                                          onClick={async () => {
                                              if (!exporting) {
                                                  setExporting(true);
                                                  const { exportXls } = await import('./exportXls');
                                                  const exportData = getExportData();
                                                  const exportDataResult = exportData.flatMap(item => {
                                                      if (item.manyNewAgencyRate?.length > 0) {
                                                          return item.manyNewAgencyRate.map(agency => ({
                                                              ...item,
                                                              floatingSaleServiceRate: agency?.floatingSale,
                                                              distributorName: agency?.name,
                                                              redemptionServiceRate: agency?.redemption,
                                                              fixedSaleServiceType: agency?.select,
                                                              fixedSaleServiceRate: agency?.selectData,
                                                              remark: agency?.remark,
                                                          }));
                                                      }
                                                      return [item];
                                                  });
                                                  exportXls(
                                                      distributionFeeRateColunmns,
                                                      columnsState,
                                                      exportDataResult,
                                                      title,
                                                      true,
                                                  );

                                                  setExporting(false);
                                              }
                                          }}
                                          style={{
                                              fontSize: 17,
                                              cursor: exporting ? 'none' : 'pointer',
                                              padding: '2.5px 0',
                                          }}
                                      />
                                  </Tooltip>,
                                  <Tooltip title="导出代销费率信息">
                                      <ArrowUpOutlined
                                          onClick={async () => {
                                              const filterData = filterable.filtered;

                                              const processFilterData = data => {
                                                  return data.flatMap(item => {
                                                      if (item.manyNewAgencyRate && item.manyNewAgencyRate.length > 0) {
                                                          return item.manyNewAgencyRate.map(agency => ({
                                                              floatingSaleServiceRate: agency?.floatingSale,
                                                              distributorName: agency?.name,
                                                              redemptionServiceRate: agency?.redemption,
                                                              fixedSaleServiceType: agency?.select,
                                                              fixedSaleServiceRate: agency?.selectData,
                                                              remark: agency?.remark,
                                                              productName: item?.name,
                                                              productNo: item?.no,
                                                          }));
                                                      }
                                                      return [];
                                                  });
                                              };

                                              const res = processFilterData(filterData);

                                              const { exportXls } = await import('./exportXls');
                                              exportXls(
                                                  distributionFeeRateColunmns,
                                                  columnsState,
                                                  processFilterData(filterData),
                                                  '代销费率信息导出',
                                                  true,
                                              );
                                          }}
                                          style={{
                                              fontSize: 17,
                                              cursor: exporting ? 'none' : 'pointer',
                                              padding: '2.5px 0',
                                          }}
                                      />
                                  </Tooltip>,
                              ]
                    }
                    onColumnsStateChange={setColumnsState}
                />
            </filterable.Provider>
        </CrudTableContext.Provider>
    );
}
