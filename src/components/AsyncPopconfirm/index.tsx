import React, { cloneElement, useState } from 'react';
import { Popconfirm } from 'antd';
import { PopconfirmProps } from 'antd/lib/popconfirm';

export type AsyncPopconfirmProps = Omit<PopconfirmProps, 'onConfirm'> & {
    onConfirm: (e?: React.MouseEvent<HTMLElement>) => Promise<void>;
    children: React.ReactElement;
};

export default function AsyncPopconfirm(props: AsyncPopconfirmProps) {
    const { onConfirm, children, ...otherProps } = props;

    const [visible, setVisible] = useState(false);
    const [loading, setLoading] = useState(false);

    const newChildren = cloneElement(children, { onClick: () => setVisible(true) });

    return (
        <Popconfirm
            {...otherProps}
            visible={visible}
            onConfirm={async () => {
                setLoading(true);
                await onConfirm();
                setLoading(false);
                setVisible(false);
            }}
            okButtonProps={{ loading }}
            onCancel={() => setVisible(false)}
        >
            {newChildren}
        </Popconfirm>
    );
}
