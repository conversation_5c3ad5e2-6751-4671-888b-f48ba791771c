import { Field } from '@/utils/newFieldProperty';
import { useCreation, useDebounceFn, usePersistFn } from 'ahooks';
import { Form, FormProps } from 'antd';
import { uniqueId } from 'lodash';
import React, { createContext, useContext, useEffect, useRef, useState } from 'react';

const setFilterSymbol = Symbol('setFilter');
const triggerFilterSymbol = Symbol('triggerFilter');

interface FilterFuncs<T> {
    filter: (val: T[]) => T[];
    resetFields: () => void;
}

export interface FilteredData<T> {
    filtered: T[];
    resetFields: () => void;
    [setFilterSymbol]: (funcs: FilterFuncs<T>) => void;
    [triggerFilterSymbol]: () => void;
}

export function useSimpleTableFilter<T>(data: T[]): FilteredData<T> {
    const [filtered, setFiltered] = useState<T[]>(null);
    const filterFunc = useRef<FilterFuncs<T>>();

    function triggerFilter() {
        data ? setFiltered(filterFunc.current?.filter?.(data) || data) : setFiltered(null);
    }

    useEffect(triggerFilter, [data]);

    return {
        filtered,
        resetFields: () => {
            filterFunc.current?.resetFields?.();
        },
        [setFilterSymbol]: func => {
            filterFunc.current = func;
        },
        [triggerFilterSymbol]: triggerFilter,
    };
}

export interface SimpleTableFilterProps<T> extends FormProps<T> {
    filter: FilteredData<T>;
}

export type SimpleTableFilterItemProps<T> = {
    // 多个field时，取并集
    field?: Field<any> | Field<any>[];
    label?: React.ReactNode;
} & (
    | {
          // filterFunc过滤全部数据列表还是过滤单个数据项
          filterAll: true;
          filterFunc?: (data: T, filter: any) => T;
      }
    | {
          filterAll?: false;
          filterFunc?: (record: T, filter: any) => boolean;
      }
);

interface FilterContextType {
    filterFunc: Record<string, (data: any, filter: any) => any>;
}

const filterContext = createContext<FilterContextType>({ filterFunc: {} });

export function SimpleTableFilter<T = any>(props: React.PropsWithChildren<SimpleTableFilterProps<T>>) {
    const { form: outerForm, onValuesChange, filter, layout, ...otherProps } = props;

    const [innerForm] = Form.useForm();
    const form = outerForm || innerForm;

    const filters = useRef<FilterContextType>({ filterFunc: {} });

    const onFilter = usePersistFn((val: T[]) => {
        // 多个filter取交集
        const formVal = form.getFieldsValue();

        const { filterFunc } = filters.current;
        let result = val;
        for (let [k, v] of Object.entries(formVal)) {
            if (v != null && v != '' && filterFunc[k]) {
                result = filterFunc[k](result, v);
            }
        }
        return result;
    });

    const resetFields = usePersistFn(() => {
        form.resetFields();
        filter[triggerFilterSymbol]();
    });

    useEffect(() => {
        filter[setFilterSymbol]({
            filter: onFilter,
            resetFields,
        });
    }, []);

    const onSearch = useDebounceFn(filter[triggerFilterSymbol], { wait: 500 });

    return (
        <filterContext.Provider value={filters.current}>
            <Form
                layout={layout || 'inline'}
                form={form}
                onValuesChange={(changed, val) => {
                    onSearch.run();
                    onValuesChange?.(changed, val);
                }}
                {...otherProps}
            />
        </filterContext.Provider>
    );
}

export function SimpleTableFilterItem<T = any>(props: React.PropsWithChildren<SimpleTableFilterItemProps<T>>) {
    const { label, field, children } = props;

    const context = useContext(filterContext);

    const fieldKey = useRef<string>();

    useEffect(() => {
        fieldKey.current = uniqueId().toString();
    }, []);

    useEffect(() => {
        if (props.filterFunc != null) {
            if (props.filterAll) {
                context.filterFunc[fieldKey.current] = props.filterFunc;
            } else {
                context.filterFunc[fieldKey.current] = (data, filter) => {
                    return data.filter(x => props.filterFunc(x, filter));
                };
            }
            return;
        }
        if (props.field == null) {
            context.filterFunc[fieldKey.current] = null;
            return;
        }
        const fields = Array.isArray(props.field) ? props.field : [props.field];
        context.filterFunc[fieldKey.current] = (data: any[], filter: any) =>
            data.filter(record => {
                for (let x of fields) {
                    if (x.filterFunc != null && x.filterFunc(record[x.name], filter)) {
                        return true;
                    }
                }
                return false;
            });
    }, [props.field, props.filterFunc]);

    const realLabel = useCreation(() => {
        if (label) {
            return label;
        }
        if (field && !Array.isArray(field)) {
            return field.title;
        }
        return null;
    }, [label, field]);

    return (
        <Form.Item label={realLabel} name={fieldKey.current}>
            {children || (field && !Array.isArray(field) && field.formItem?.())}
        </Form.Item>
    );
}
