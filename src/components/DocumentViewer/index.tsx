import { DocumentEditor } from '@onlyoffice/document-editor-react';
import React, { useMemo } from 'react';
import { v4 as uuid } from 'uuid';

export type DocumentViewerProps = {
    filename: string;
    fileType: string;
    url: string;
    documentType: string;
};

const server = 'http://192.168.211.230:5412';

export default (props: DocumentViewerProps) => {
    const { filename, fileType, url, documentType } = props;
    const key = useMemo(() => uuid(), [url]);

    return (
        <DocumentEditor
            id={`${props.documentType}Editor`}
            documentServerUrl={server}
            config={{
                type: 'embedded',
                document: { fileType, key, title: filename, url },
                documentType,
            }}
        />
    );
};
