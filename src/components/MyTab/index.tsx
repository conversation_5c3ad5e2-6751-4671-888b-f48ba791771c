import React, { createContext, useContext, useState } from 'react';
import { Tag } from 'antd';
import styles from './index.less';

const { CheckableTag } = Tag;

export type TabProps = {
    defaultTab: string;
    style?: React.CSSProperties;
    onChange?: (key: string) => void;
}

export type TabItemProps = {
    ky: string;
}

type ContextType = {
    selected: string;
    onChange: (key: string) => void;
}

const TabContext = createContext<ContextType>(null);

export const Tab: React.FunctionComponent<TabProps> = (props) => {
    const { defaultTab, style, onChange, children } = props;
    const [ selected, setSelected ] = useState<string>(defaultTab);

    const newOnChange = (key: string) => {
        setSelected(key);
        onChange?.(key);
    }

    return (
        <TabContext.Provider value={{ selected, onChange: newOnChange }}>
            <div style={style} className={ styles.wrapper }>
                { children }
            </div>
        </TabContext.Provider>
    )
}

export const TabItem: React.FunctionComponent<TabItemProps> = (props) => {
    const { ky, children } = props;
    const { selected, onChange } = useContext(TabContext);

    return (
        <CheckableTag checked={ky === selected} onChange={() => onChange(ky)}>{ children }</CheckableTag>
    )
}
