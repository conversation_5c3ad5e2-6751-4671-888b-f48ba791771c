import React from 'react';
import { Spin } from 'antd';
import { useRequest } from 'ahooks';
import { FormDataProvider, FormLayout, MemorizeFormProvider } from '../MyForm';
import triggerContext from './triggerContext';
import { ContextType, getFormTrigger, useTriggered } from './FormTrigger';

const FormContext = React.createContext<ContextType<any>>(null);
export const FormTrigger = getFormTrigger(FormContext);

export interface TriggeredFormProps<T> {
    labelWidth?: number;
    onGetDetail?: (data: T) => Promise<T>;
    afterCreate?: (data: T) => void;
    afterUpdate?: (data: T, oldData: T) => void;
}

export interface AfterEditParams {
    doNotClose?: boolean;
}

export interface TriggerableFormProps<T> {
    formName: string;
    labelWidth: number;
    formContent: React.ReactNode;
    setVisible: (visible: boolean) => void;
    onGetDetail?: (data: T) => Promise<T>;
    afterCreate?: (data: T, params?: AfterEditParams) => void;
    afterUpdate?: (data: T, oldData: T, params?: AfterEditParams) => void;
}

export default function<T>(props: React.PropsWithChildren<TriggerableFormProps<T>>) {
    const { formName, labelWidth, setVisible, onGetDetail, afterCreate, afterUpdate, formContent, children } = props;

    const [editing, showForm] = useTriggered<any>(formName, FormContext, (_, __, fromUserInput) => {
        if (fromUserInput) {
            setVisible(true);
        }
    });

    const detailedDataReq = useRequest(
        async () => {
            if (editing.id && editing.data && onGetDetail) return await onGetDetail(editing.data);
            else return editing.data;
        },
        { refreshDeps: [editing.data] },
    );

    return (
        <FormContext.Provider value={{ editing, showForm }}>
            {children}
            <triggerContext.Provider
                initialState={{
                    closeForm: () => {
                        setVisible(false);
                    },
                }}
            >
                <MemorizeFormProvider>
                    <FormLayout value={{ labelWidth }}>
                        <FormDataProvider
                            data={detailedDataReq.data}
                            dataLoading={detailedDataReq.loading}
                            id={editing.id}
                            onCreate={afterCreate}
                            onUpdate={(data, params) => {
                                afterUpdate(data, detailedDataReq.data, params);
                                showForm(formName, editing.id, data, false);
                            }}
                        >
                            {formContent}
                        </FormDataProvider>
                    </FormLayout>
                </MemorizeFormProvider>
            </triggerContext.Provider>
        </FormContext.Provider>
    );
}
