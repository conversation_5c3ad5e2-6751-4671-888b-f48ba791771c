import { Button } from 'antd';
import React from 'react';
import { NormalFormOperation } from '../ControlledForm';
import { MyForm } from '../MyForm';
import { CommonProps } from './CommonProps';
import styles from './index.less';

export function getFooter<T>(props: CommonProps<T>, setVisible: (visible: boolean) => void, data: any) {
    const { partitioned, displayOnly, onCreate, onUpdate } = props;
    const isDisplayOnly = partitioned || (typeof displayOnly === 'function' ? displayOnly(data) : displayOnly);

    return isDisplayOnly ? (
        <Button onClick={() => setVisible(false)}>关闭</Button>
    ) : (
        <NormalFormOperation onCancel={() => setVisible(false)} onCreate={onCreate} onUpdate={onUpdate} />
    );
}

export function getFormDom<T>(props: CommonProps<T>, children: JSX.Element): JSX.Element {
    const { name, partitioned } = props;

    return partitioned ? (
        children
    ) : (
        <MyForm component="div" className={styles.form} name={name}>
            {children}
        </MyForm>
    );
}
