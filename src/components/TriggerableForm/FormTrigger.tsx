import React, { useContext, useEffect, useState } from 'react';
import { usePersistFn } from 'ahooks';

export interface Editing<T, TKey> {
    id: TKey;
    data: T;
}

export type ShowFormFn<T, TKey> = (formName: string, id: TKey, data: T, fromUserInput: boolean) => void;

export type ContextType<T, TKey = number> = {
    editing: Editing<T, TKey>;
    showForm: ShowFormFn<T, TKey>;
};

export type FormTriggerProps<T, TKey = number> = Omit<React.HTMLProps<HTMLDivElement>, 'id' | 'data' | 'onClick'> & {
    formName?: string;
    id?: TKey;
    data?: T;
};

interface FormTriggerComp {
    <T>(props: React.PropsWithChildren<FormTriggerProps<T>>): React.ReactElement<any, any>;
}

export function getFormTrigger(context: React.Context<ContextType<any>>): FormTriggerComp {
    return props => {
        const { formName, id, data, children, ...otherProps } = props;
        const c = useContext(context);

        useEffect(() => {
            if (c?.showForm && c?.editing) {
                const { editing, showForm } = c;
                if (editing.id === id) {
                    showForm(formName, id, data, false);
                }
            }
        }, [id, data]);

        if (c?.showForm) {
            const { showForm } = c;
            return (
                <div {...otherProps} onClick={() => showForm(formName, id, data, true)}>
                    {children}
                </div>
            );
        } else {
            return null;
        }
    };
}

export function useTriggered<T, TKey = number>(
    formName: string,
    Context: React.Context<ContextType<any>>,
    afterTriggered?: (id: TKey, data: T, fromUserInput: boolean) => void,
): [Editing<T, TKey>, ShowFormFn<T, TKey>] {
    const outerContext = useContext(Context);

    const [editing, setEditing] = useState<Editing<T, TKey>>({
        id: null,
        data: null,
    });

    const showForm = usePersistFn((receivedFormName: string, id: TKey, data: T, fromUserInput: boolean) => {
        if (receivedFormName == null || receivedFormName === formName) {
            setEditing({ id, data });
            afterTriggered?.(id, data, fromUserInput);
        } else {
            outerContext?.showForm?.(receivedFormName, id as any, data, fromUserInput);
        }
    });
    return [editing, showForm];
}
