import React, { ReactNode, useState } from 'react';
import { CommonProps } from './CommonProps';
import TriggerableForm from './TriggerableForm';

export type WithVisibleState<T> = T & {
    visible: boolean;
    setVisible: (visible: boolean) => void;
};

function Identity(props: React.PropsWithChildren<{}>) {
    return <>{props.children}</>;
}

export default function<T extends CommonProps<any>>(
    CForm: React.ComponentType<WithVisibleState<T>>,
    COutside?: React.ComponentType<WithVisibleState<T>>,
) {
    return (props: React.PropsWithChildren<T>) => {
        const [visible, setVisible] = useState(false);
        const { name, labelWidth, onGetDetail, afterCreate, afterUpdate, children } = props;

        const Outside = COutside || Identity;

        return (
            <TriggerableForm<T>
                formName={name}
                labelWidth={labelWidth}
                setVisible={setVisible}
                onGetDetail={onGetDetail}
                afterCreate={(val, params) => {
                    const { doNotClose } = params || {};
                    !doNotClose && setVisible(false);
                    afterCreate?.(val);
                }}
                afterUpdate={(val, oldVal, params) => {
                    const { doNotClose } = params || {};
                    !doNotClose && setVisible(false);
                    afterUpdate?.(val, oldVal);
                }}
                formContent={<CForm {...props} visible={visible} setVisible={setVisible} />}
            >
                <Outside {...props} visible={visible} setVisible={setVisible}>
                    {children}
                </Outside>
            </TriggerableForm>
        );
    };
}
