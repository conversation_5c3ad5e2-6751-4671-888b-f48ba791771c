import React from 'react';
import { Affix, Spin } from 'antd';
import { useFormData } from '../MyForm';
import { PortalProvider, PortalReceiver } from '../PortalRender';
import { CommonProps } from './CommonProps';
import { getFooter, getFormDom } from './utils';
import footerContext from './footerContext';
import wrapTrigger from './wrapTrigger';
import styles from './index.less';

export interface FullScreenFormProps<T> extends CommonProps<T> {
    transparent?: boolean;
}

const FullScreenForm = wrapTrigger<FullScreenFormProps<any>>(
    props => {
        const { transparent, createTitle, editTitle, formContents, customFooter, visible, setVisible } = props;
        const { data, id, dataLoading } = useFormData();
        const title = id == null ? createTitle : typeof editTitle === 'function' ? editTitle(data) : editTitle;
        const isCustomFooter = customFooter
            ? typeof customFooter == 'function'
                ? data
                    ? customFooter(data)
                    : false
                : customFooter
            : false;

        return (
            <PortalProvider>
                {getFormDom(
                    props,
                    visible && (
                        <div
                            style={
                                transparent
                                    ? { marginBottom: -24 }
                                    : {
                                          marginBottom: -24,
                                          background: 'white',
                                          padding: '32px 20px',
                                      }
                            }
                        >
                            <div className={styles.header}>
                                <h2 style={{ display: 'inline-block' }}>{title}</h2>
                            </div>
                            <footerContext.Provider value={{ onCancel: () => setVisible(false) }}>
                                <Spin spinning={dataLoading}>{formContents}</Spin>
                            </footerContext.Provider>
                            <Affix offsetBottom={0}>
                                <div className={styles.fixedFooter}>
                                    {isCustomFooter ? <PortalReceiver /> : getFooter(props, setVisible, data)}
                                </div>
                            </Affix>
                        </div>
                    ),
                )}
            </PortalProvider>
        );
    },
    props => {
        const { children, visible } = props;

        return <div style={visible ? { display: 'none' } : {}}>{children}</div>;
    },
);

export default FullScreenForm as <T>(props: React.PropsWithChildren<FullScreenFormProps<T>>) => JSX.Element;
