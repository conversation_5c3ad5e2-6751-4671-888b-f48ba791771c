import { CustomResponse } from '@/models/common/CustomResponse';
import { TriggeredFormProps } from './TriggerableForm';

export interface CommonProps<T> extends TriggeredFormProps<T> {
    name: string;
    createTitle: string;
    editTitle: string | ((data: T) => string);
    formContents: React.ReactNode;
    partitioned?: boolean;
    displayOnly?: boolean | ((data: T) => boolean);
    customFooter?: boolean | ((data: T) => boolean);
    onCreate?: (data: any) => Promise<CustomResponse<T>>;
    onUpdate?: (id: number, data: any) => Promise<CustomResponse<T>>;
}
