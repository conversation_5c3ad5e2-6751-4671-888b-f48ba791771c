import React, { useState } from 'react';
import { Drawer, Spin } from 'antd';
import { PortalProvider, PortalReceiver } from '../PortalRender';
import { useFormData } from '../MyForm';
import { CommonProps } from './CommonProps';
import { getFooter, getFormDom } from './utils';
import footerContext from './footerContext';
import wrapTrigger from './wrapTrigger';

export interface DrawerFormProps<T> extends CommonProps<T> {
    width?: number;
}

const DrawerForm = wrapTrigger<DrawerFormProps<any>>(props => {
    const { width, createTitle, editTitle, formContents, customFooter, visible, setVisible } = props;

    const { id, data, dataLoading } = useFormData();
    const title = id == null ? createTitle : typeof editTitle === 'function' ? editTitle(data) : editTitle;
    const isCustomFooter = customFooter
        ? typeof customFooter == 'function'
            ? data
                ? customFooter(data)
                : false
            : customFooter
        : false;

    return (
        <PortalProvider>
            {getFormDom(
                props,
                <Drawer
                    title={title}
                    width={width || 800}
                    visible={visible}
                    onClose={() => setVisible(false)}
                    footer={isCustomFooter ? <PortalReceiver /> : getFooter(props, setVisible, data)}
                >
                    <footerContext.Provider value={{ onCancel: () => setVisible(false) }}>
                        <Spin spinning={dataLoading}>{formContents}</Spin>
                    </footerContext.Provider>
                </Drawer>,
            )}
        </PortalProvider>
    );
});

export default DrawerForm as <T>(props: React.PropsWithChildren<DrawerFormProps<T>>) => JSX.Element;
