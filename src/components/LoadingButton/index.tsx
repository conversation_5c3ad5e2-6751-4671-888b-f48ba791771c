import React, { useState } from 'react';
import { Button } from 'antd';
import { ButtonProps } from 'antd/lib/button';
import { usePersistFn } from 'ahooks';

export type LoadingButtonProps = Omit<ButtonProps, 'loading' | 'onClick'> & {
    onClick: (e: React.MouseEvent<HTMLElement, MouseEvent>) => Promise<void>
}

export default function(props: LoadingButtonProps) {
    const { onClick, ...otherProps } = props;

    const [loading, setLoading] = useState(false);

    const rawOnClick = usePersistFn((e) => {
        (async () => {
            setLoading(true);
            try {
                await onClick(e);
            } finally {
                setLoading(false);
            }
        })();
    });

    return <Button {...otherProps} loading={loading} disabled={loading} onClick={rawOnClick} />
}
