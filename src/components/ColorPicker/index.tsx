import React, { useEffect, useState } from 'react';
import { useControllableValue } from 'ahooks';
import { Popover, Button } from 'antd';
import { ChromePicker } from 'react-color';
import styles from './index.less';

export interface ColorPickerProps {
    defaultValue?: string;
    value?: string;
    onChange: (value: string) => void;
}

export default function(props: ColorPickerProps) {
    const [state, setState] = useControllableValue<string>(props);
    const [innerState, setInnerState] = useState(state);

    useEffect(() => {
        if (state !== innerState) {
            setInnerState(state);
        }
    }, [state]);

    const content = (
        <div onClick={e => e.stopPropagation()}>
            <ChromePicker color={innerState || undefined} onChange={val => setInnerState(val.hex)} />
        </div>
    );

    return (
        <Popover
            placement="bottomLeft"
            content={content}
            trigger="click"
            overlayClassName={styles.colorPopover}
            onOpenChange={val => {
                if (!val) setState(innerState);
            }}
        >
            <Button className={styles.trigger} onClick={e => e.stopPropagation()}>
                {innerState ? (
                    <div className={styles.colorDisplay} style={{ background: innerState }}></div>
                ) : (
                    '选择颜色'
                )}
            </Button>
        </Popover>
    );
}
