import { AutoComplete, AutoCompleteProps } from 'antd';
import React, { useState } from 'react';

export default (props: AutoCompleteProps) => {
    const [options, setOptions] = useState<typeof props.options>();

    return (
        <AutoComplete
            {...props}
            options={options || props.options}
            onSearch={value => {
                if (!value) setOptions(undefined);
                setOptions(props.options?.filter(option => option.value?.toString()?.includes(value)));
            }}
        />
    );
};
