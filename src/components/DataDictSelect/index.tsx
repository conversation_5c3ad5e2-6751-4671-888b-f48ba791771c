import React, { useEffect, useRef, useState } from 'react';
import { useControllableValue, useCreation } from 'ahooks';
import Select, { SelectProps } from 'antd/lib/select';
import { PlusOutlined } from '@ant-design/icons';
import { DataDict } from '@/models/utils/DataDict';
import service from '@/services/dataDict.service';
import { DataDictFields } from './DataDictFields';

export { DataDictFields } from './DataDictFields';

// value可以为字典项，字典项id或字典项name
type SelectValue<T> = DataDict<T> | DataDict<T>[] | number | number[] | string | string[];

export type DataDictSelectProps<T> = Omit<SelectProps<any>, 'loading' | 'value' | 'defaultValue' | 'onChange'> & {
    valueType?: 'item' | 'id' | 'name';
    field: DataDictFields;
    value?: SelectValue<T>;
    defaultValue?: SelectValue<T>;
    onChange?: (val: SelectValue<T>) => void;
    disableCreate?: boolean;
};

const addSymbol = '!@#$%^&*(()';

export default function DataDictSelect<T>(props: DataDictSelectProps<T>) {
    const { valueType, field, mode, disableCreate, ...otherProps } = props;

    const [state, setState] = useControllableValue<SelectValue<T>>(props);
    const [items, setItems] = useState<DataDict<T>[]>([]);
    // 后端未存储字典项，但出现在value中的项目。
    const [extraItems, setExtraItems] = useState<DataDict<T>[]>([]);
    // 给 extra item 赋 id
    const extraItemCount = useRef(0);
    const itemDict = useRef<Record<string, DataDict<T>>>({});
    const [loading, setLoading] = useState(false);
    const [searchValue, setSearchValue] = useState('');

    const selectItems = useCreation(
        () =>
            (Array.isArray(state) ? state : [state])
                .map(x => {
                    switch (typeof x) {
                        case 'object':
                            return x;
                        case 'number':
                            return items.find(y => y.id === x);
                        case 'string':
                            return items.find(y => y.name === x) || extraItems.find(y => y.name === x);
                    }
                })
                .filter(x => x),
        [state, items, extraItems],
    );

    function setSelectItems(newItems: DataDict<T> | DataDict<T>[]) {
        switch (valueType || 'item') {
            case 'item':
                setState(newItems);
                break;
            case 'id':
                setState(Array.isArray(newItems) ? newItems.map(x => x.id) : newItems.id);
                break;
            case 'name':
                setState(Array.isArray(newItems) ? newItems.map(x => x.name) : newItems.name);
                break;
        }
    }

    useEffect(() => {
        // 过滤掉出现在后端存储字典项中的
        const newExtraItems = extraItems.filter(x => !items.find(y => y.name === x.name));
        if (state) {
            for (let x of Array.isArray(state) ? state : [state]) {
                if (typeof x === 'number') {
                    continue;
                }
                const name = typeof x === 'string' ? x : x.name;
                // 未出现过的加进extra item
                if (!items.find(x => x.name === name) && !extraItems.find(x => x.name === name)) {
                    if (typeof x === 'object') {
                        newExtraItems.splice(0, 0, x);
                    } else {
                        extraItemCount.current += 1;
                        // id是负数，和items的id区别
                        newExtraItems.splice(0, 0, {
                            id: -extraItemCount.current,
                            field,
                            name,
                            key: null,
                            value: null,
                        });
                    }
                }
            }
        }
        setExtraItems(newExtraItems);
    }, [items, state]);

    useEffect(() => {
        setLoading(true);
        service
            .getList(field)
            .then(response => {
                if (response.data) {
                    response.data.sort((a, b) => a.name.localeCompare(b.name));
                    itemDict.current = {};
                    for (let x of response.data) {
                        itemDict.current[x.id] = x;
                    }
                    setItems(response.data);
                }
            })
            .finally(() => setLoading(false));
    }, [field]);

    const addItem = (name: string) => {
        setLoading(true);
        service
            .create({ field, name, key: null, value: null })
            .then(response => {
                if (response.data) {
                    setItems([response.data, ...items]);
                    setSearchValue('');
                    if (!mode) {
                        setSelectItems(response.data);
                    } else {
                        setSelectItems([...selectItems, response.data]);
                    }
                    itemDict.current[response.data.id] = response.data;
                }
            })
            .finally(() => setLoading(false));
    };

    // 为了保证组件受控，需要做一些处理
    const selectValue = selectItems.map(x => x.id);

    return (
        <Select<any>
            {...otherProps}
            mode={mode}
            loading={loading}
            showSearch
            filterOption={(val, opt) => {
                return (
                    !opt.label ||
                    opt.label
                        .toString()
                        .toLowerCase()
                        .includes(val.toLowerCase())
                );
            }}
            searchValue={searchValue}
            onSearch={setSearchValue}
            value={selectValue}
            onChange={val => {
                function onAddSymbolSelected() {
                    // 添加项目
                    if (searchValue) {
                        addItem(searchValue);
                    }
                }

                if (!Array.isArray(val)) {
                    if (val !== addSymbol) {
                        setSelectItems(itemDict.current[val]);
                    } else {
                        onAddSymbolSelected();
                    }
                } else {
                    if (!val.includes(addSymbol)) {
                        setSelectItems(val.map(x => itemDict.current[x]).filter(x => x));
                    } else {
                        onAddSymbolSelected();
                    }
                }
            }}
        >
            {!disableCreate && (
                <Select.Option label="" value={addSymbol} disabled={!searchValue}>
                    <div>
                        <PlusOutlined style={{ marginRight: 4 }} />
                        添加项目
                        {searchValue ? `“${searchValue}”` : '：请在搜索框输入要添加的项目'}
                    </div>
                </Select.Option>
            )}
            {[...extraItems, ...items].map(x => (
                <Select.Option key={x.id} value={x.id} label={x.name}>
                    {x.name}
                </Select.Option>
            ))}
        </Select>
    );
}
