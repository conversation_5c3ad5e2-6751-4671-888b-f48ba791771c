import React from 'react';
import { Collapse } from 'antd';
import { MyFormItem } from '../MyForm';
import styles from './index.less';

// 可折叠的表单项

export interface CollapsedItemProps {
    name: string;
    title: React.ReactNode;
}

const CollapsedItem: React.FunctionComponent<CollapsedItemProps> = props => {
    const { name, title, children } = props;
    return (
        <>
            <MyFormItem hidden={true} name={name} />
            <Collapse className={styles.noBorder} defaultActiveKey={[]} ghost>
                <Collapse.Panel header={title} key="1">
                    <MyFormItem noStyle name={name}>
                        {children}
                    </MyFormItem>
                </Collapse.Panel>
            </Collapse>
        </>
    );
};

export default CollapsedItem;
