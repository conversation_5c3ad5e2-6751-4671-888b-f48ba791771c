import { Message } from '@/models/utils/Message';
import { useDebounceFn } from 'ahooks';
import { Button, Drawer, Input, Row, Spin } from 'antd';
import React, { useState } from 'react';
import { TodoList } from '../MessageList';
import styles from './index.less';
import { MessageType, WorkflowFormProps } from './WorkflowFormProps';
import { useIsSmallDevice } from '@/utils/mediaQuery';
import { createPopup } from '@/utils/zustand';
import { MenuOutlined } from '@ant-design/icons';

export interface MessageCenterProps {
    id: number;
    messages: Message[];
    onClick: (id: number) => void;
    afterSubmit: (id: number) => void;
    forms: Record<string, React.LazyExoticComponent<React.ComponentType<WorkflowFormProps>>>;
}

const categoryDrawer = createPopup();

export default function(props: MessageCenterProps) {
    const isSmallDevice = useIsSmallDevice();

    const { id, messages, forms, afterSubmit } = props;

    const current = messages.find(x => x.id == id);
    const links = current?.link?.split(';');
    const Form = links && forms[links[1]];
    const isWorkflow = links && links[0] && links[0].split('.')[0] === 'workflow';
    const isJiraIssue = links && links[0] === 'jira';
    const isOpenDayMessage = links && links[0] === 'openDay';
    const type: MessageType = links && links[0] === 'workflow.notify' ? 'notify' : 'task';

    return (
        <React.Fragment>
            <Row justify="end" style={{ paddingRight: 12, marginBottom: 24 }}>
                <Button onClick={() => categoryDrawer.show()} icon={<MenuOutlined />} type="primary" />
            </Row>
            <div className={styles.wrapper}>
                {!isSmallDevice && (
                    <div className={styles.left}>
                        <Left {...props} />
                    </div>
                )}
                <div className={styles.right}>
                    <div className={styles.rightInner}>
                        {current && (isWorkflow || isJiraIssue || isOpenDayMessage) && Form && (
                            <React.Suspense fallback={<Spin />}>
                                <Form
                                    key={current?.link}
                                    type={type}
                                    link={links.slice(2)}
                                    content={current.content}
                                    title={current.title}
                                    afterSubmit={() => afterSubmit(current.id)}
                                    currentId={current.id}
                                    currentInstanceId={current.instanceId}
                                />
                            </React.Suspense>
                        )}
                    </div>
                </div>
            </div>
            {isSmallDevice && <CategoryDrawer {...props} />}
        </React.Fragment>
    );
}

const Left = (props: MessageCenterProps) => {
    const { id, messages, onClick } = props;

    const [search, setSearch] = useState<string>();
    const setSearchKeyword = useDebounceFn(value => setSearch(value), { wait: 300 });

    return (
        <React.Fragment>
            <div style={{ padding: '8px 16px' }}>
                <Input placeholder="搜索关键字" onChange={e => setSearchKeyword.run(e.target.value)} />
            </div>
            <div style={{ height: '100%', overflow: 'auto' }}>
                <TodoList selectId={id} search={search} messages={messages} onClick={message => onClick(message.id)} />
            </div>
        </React.Fragment>
    );
};

const CategoryDrawer = (props: MessageCenterProps) => {
    const { open } = categoryDrawer.use();

    return (
        <Drawer open={open} placement="right" onClose={() => categoryDrawer.hide()}>
            <Left
                {...props}
                onClick={id => {
                    categoryDrawer.hide();
                    props.onClick?.(id);
                }}
            />
        </Drawer>
    );
};
