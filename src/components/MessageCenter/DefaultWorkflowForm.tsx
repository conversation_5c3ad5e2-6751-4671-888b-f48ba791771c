import React from 'react';
import { useRequest } from 'ahooks';
import { Spin } from 'antd';
import { MessageType, WorkflowFormProps } from './WorkflowFormProps';
import { FormDataProvider, FormLayout } from '../MyForm';

export interface InnerWorkflowProps {
    title: string;
    type: MessageType;
    step: string;
    taskId: string;
    afterSubmit: () => void;
    hideTitle: boolean;
    onCancel: () => void;
}

export default function<T>(service: (taskId: string, step: string) => T, C: React.ComponentType<InnerWorkflowProps>) {
    return (props: WorkflowFormProps) => {
        const { title, type, link, afterSubmit, hideTitle, onCancel } = props;
        const [step, taskId] = link;

        const req = useRequest(() => service(taskId, step), {
            refreshDeps: [link?.join('~!@#$%^&*(')],
        });

        return (
            <FormLayout value={{ labelWidth: 132 }}>
                <FormDataProvider<any, string> id={req.loading ? null : taskId} data={req.data}>
                    <Spin spinning={req.loading}>
                        <C
                            title={title}
                            type={type}
                            step={step}
                            taskId={taskId}
                            afterSubmit={afterSubmit}
                            hideTitle={hideTitle}
                            onCancel={onCancel}
                        />
                    </Spin>
                </FormDataProvider>
            </FormLayout>
        );
    };
}
