import React from 'react';
import { useRequest } from 'ahooks';
import { Button, message, Space, Spin } from 'antd';
import { LinkOutlined } from '@ant-design/icons';
import fileService from '@/services/file.service';
import styles from './index.less';

export interface AsyncFileDisplayProps {
    value?: string;
    filenameRender?: (dom: string, hash: string) => React.ReactNode;
}

export default function AsyncFileDisplay(props: AsyncFileDisplayProps) {
    const { value, filenameRender } = props;

    const hash = (value || '').split(',').filter(x => x);
    const req = useRequest(
        async () => {
            if (!hash || hash.length == 0) {
                return [];
            } else {
                try {
                    const response = await fileService.findDetailByHash(hash);
                    return response.data;
                } catch (e) {
                    message.error(e.message || '获取文件详情失败');
                    return [];
                }
            }
        },
        { refreshDeps: [value] },
    );

    if (req.loading) {
        return <Spin />;
    } else {
        return (
            <Space className={styles.fileDisplayer}>
                {req.data.map(x => (
                    <Button
                        key={x.hash}
                        type="link"
                        href={fileService.downloadUrl(x.url)}
                        icon={<LinkOutlined />}
                        style={{ padding: 0 }}
                    >
                        {filenameRender ? filenameRender(x.filename, x.hash) : x.filename}
                    </Button>
                ))}
            </Space>
        );
    }
}
