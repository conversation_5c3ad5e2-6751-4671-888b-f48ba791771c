import React, { useEffect, useState } from 'react';
import { DeleteOutlined, LinkOutlined } from '@ant-design/icons';
import { message, Upload, Button } from 'antd';
import { UploadProps } from 'antd/lib/upload';
import { UploadFile } from 'antd/lib/upload/interface';
import { UploadOutlined } from '@ant-design/icons';
import { CustomResponse } from '@/models/common/CustomResponse';
import { FileUpload } from '@/models/utils/FileUpload';
import fileService from '@/services/file.service';

export { default as AsyncUpload } from './AsyncUpload';
export { default as AsyncFileDisplay } from './AsyncFileDisplay';
export { default as FileDisplay } from './FileDisplay';

export type MyUploadProps = Omit<UploadProps, 'value'> & {
    value?: FileUpload | FileUpload[];
    onChange?: (val: FileUpload | FileUpload[]) => void;
};

export default function MyUpload(props: MyUploadProps) {
    const { value, onChange, multiple, ...otherProps } = props;
    const [loading, setLoading] = useState(false);
    const [innerFile, setInnerFile] = useState<UploadFile[]>([]);
    const valueArr = Array.isArray(value) ? value : value ? [value] : [];

    function mapFile(value: FileUpload): UploadFile {
        return {
            uid: value.id.toString(),
            size: 0,
            type: '',
            name: value.filename,
            url: fileService.downloadUrl(value.url),
        };
    }

    useEffect(() => {
        if (value) {
            setInnerFile(valueArr.map(mapFile));
        } else {
            setInnerFile([]);
        }
    }, [value]);

    return (
        <Upload
            {...otherProps}
            multiple={false}
            action={fileService.uploadUrl}
            withCredentials={true}
            fileList={innerFile}
            itemRender={(_, file, __, { remove }) => {
                return (
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Button
                            key={file.uid}
                            type="link"
                            href={file.url}
                            icon={<LinkOutlined />}
                            style={{ padding: 0 }}
                        >
                            {file.name}
                        </Button>
                        <DeleteOutlined onClick={remove} />
                    </div>
                );
            }}
            onChange={e => {
                if (e.file.status === 'uploading') {
                    setLoading(true);
                    if (innerFile.find(x => x.uid === e.file.uid)) {
                        setInnerFile(innerFile.map(x => (x.uid === e.file.uid ? e.file : x)));
                    } else {
                        setInnerFile([...innerFile, e.file]);
                    }
                }
                if (e.file.response) {
                    setLoading(false);
                    const response: CustomResponse<FileUpload> = e.file.response;
                    message.success(response.message);
                    if (multiple) {
                        onChange([...valueArr, response.data]);
                    } else {
                        onChange(response.data);
                    }
                }
            }}
            onRemove={file => {
                if (!multiple) {
                    onChange(null);
                    setInnerFile([]);
                } else {
                    onChange(valueArr.filter(x => x.id.toString() !== file.uid));
                    setInnerFile(innerFile.filter(x => x.uid !== file.uid));
                }
            }}
        >
            {!loading && (!value || multiple) && <Button icon={<UploadOutlined />}>点击上传</Button>}
        </Upload>
    );
}
