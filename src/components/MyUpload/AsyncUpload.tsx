import React, { useEffect, useRef, useState } from 'react';
import { message, Upload, Button } from 'antd';
import { UploadProps } from 'antd/lib/upload';
import { UploadFile } from 'antd/lib/upload/interface';
import { UploadOutlined } from '@ant-design/icons';
import { CustomResponse } from '@/models/common/CustomResponse';
import { FileUpload } from '@/models/utils/FileUpload';
import fileService from '@/services/file.service';

// 异步文件操作，value为逗号分隔的文件hash
export type AsyncUploadProps = Omit<UploadProps, 'value' | 'onChange'> & {
    value?: string;
    onChange?: (val: string) => void;
};

export default function AsyncUpload(props: AsyncUploadProps) {
    const { value, onChange, multiple, ...otherProps } = props;
    const [loading, setLoading] = useState(false);
    const [innerFile, setInnerFile] = useState<UploadFile[]>(null);
    const { current: fileDict } = useRef<Record<string, FileUpload>>({});

    useEffect(() => {
        (async () => {
            const hashes = (value || '').split(',').filter(x => x);
            const toFetch = hashes.filter(x => fileDict[x] == null);
            if (toFetch.length > 0) {
                setLoading(true);
                const fileDetails = await fileService.findDetailByHash(toFetch);
                for (let x of fileDetails.data) {
                    fileDict[x.hash] = x;
                }
                setLoading(false);
            }
            setInnerFile(
                hashes
                    .map(x => {
                        const upload = fileDict[x];
                        if (!upload) {
                            return null;
                        }
                        return {
                            uid: upload.hash,
                            size: 0,
                            type: '',
                            name: upload.filename,
                            url: fileService.downloadUrl(upload.url),
                        };
                    })
                    .filter(x => x),
            );
        })();
    }, [value]);

    return (
        <Upload
            {...otherProps}
            multiple={false}
            action={fileService.uploadUrl}
            withCredentials={true}
            fileList={innerFile}
            onChange={e => {
                if (e.file.status === 'uploading') {
                    setLoading(true);
                    if (innerFile.find(x => x.uid === e.file.uid)) {
                        setInnerFile(innerFile.map(x => (x.uid === e.file.uid ? e.file : x)));
                    } else {
                        setInnerFile([...innerFile, e.file]);
                    }
                }
                if (e.file.response) {
                    setLoading(false);
                    const response: CustomResponse<FileUpload> = e.file.response;
                    message.success(response.message);
                    fileDict[response.data.hash] = response.data;
                    onChange(
                        [
                            ...innerFile.map(x => (x.uid === e.file.uid ? null : x.uid)).filter(x => x),
                            response.data.hash,
                        ].join(','),
                    );
                }
            }}
            onRemove={file => {
                const hash = file.uid;
                onChange(
                    value
                        .split(',')
                        .filter(x => x && x !== hash)
                        .join(','),
                );
                setInnerFile(innerFile.filter(x => x.uid !== hash));
            }}
        >
            {!loading && (!value || multiple) && <Button icon={<UploadOutlined />}>点击上传</Button>}
        </Upload>
    );
}
