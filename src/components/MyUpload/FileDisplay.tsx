import React from 'react';
import { Button, Space } from 'antd';
import { LinkOutlined } from '@ant-design/icons';
import fileService from '@/services/file.service';
import styles from './index.less';
import { FileUpload } from '@/models/utils/FileUpload';

export interface AsyncFileDisplayProps {
    value?: FileUpload | FileUpload[];
}

export default function AsyncFileDisplay(props: AsyncFileDisplayProps) {
    const { value } = props;
    const valueArr = Array.isArray(value) ? value : value ? [value] : [];

    return (
        <Space className={styles.fileDisplayer}>
            {valueArr.map(x => (
                <Button
                    key={x.hash}
                    type="link"
                    href={fileService.downloadUrl(x.url)}
                    icon={<LinkOutlined />}
                    style={{ padding: 0 }}
                >
                    {x.filename}
                </Button>
            ))}
        </Space>
    );
}
