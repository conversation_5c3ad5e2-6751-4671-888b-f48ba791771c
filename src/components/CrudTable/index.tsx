import { proxyMethods } from '@/utils';
import { SimpleFilterForm } from '@/utils/fieldProperty';
import { ExportOutlined } from '@ant-design/icons';
import ProTable, { ColumnsState, ProTableProps, ProColumnType } from '@ant-design/pro-table';
import { useCreation } from 'ahooks';
import { message, Popconfirm, Tooltip } from 'antd';
import React, { createContext, useContext, useEffect, useState } from 'react';
import useCrud, { UseCrudOptions } from './useCrud';

export interface CrudActionRef<T, TKey = number> {
    create: (data: unknown) => Promise<void>;
    update: (id: TKey, data: unknown) => Promise<void>;
    delete: (id: TKey) => Promise<void>;
    refresh: (reset?: boolean) => Promise<void>;
    triggerSearch: () => void;
}

export interface DeleteWrapperProps<TKey = number> {
    id: TKey;
    confirm: React.ReactNode;
    delete?: (id: TKey) => Promise<void>;
}

export type CrudTableProps<T, TKey = number> = {
    actionRef?: CrudActionRef<T, TKey>;
    rowKey?: string;
    simpleSearches?: React.ReactNode;
    newVersion?: boolean;
    serverPagination?: boolean;
    customFilters?: any;
    frontendFilters?: ((val: T) => boolean)[];
} & Omit<UseCrudOptions<T, TKey>, 'rowKey'> &
    Omit<ProTableProps<T, any>, 'title' | 'rowKey' | 'actionRef'>;

type ContextType<T, TKey = number> = {
    actions: CrudActionRef<T, TKey>;
    title: string;
};

const CrudTableContext = createContext<ContextType<any, any>>({
    actions: null,
    title: '',
});

const actionsSymbol = Symbol('crudTableActions');

export { useCrud };

// 随便赋一个初始值，组件渲染之后替换掉
export function useCrudTableAction<T, TKey = number>(): CrudActionRef<T, TKey> {
    return proxyMethods(actionsSymbol);
}

export function DeleteWrapper<TKey = number>(props: React.PropsWithChildren<DeleteWrapperProps<TKey>>) {
    const context = useContext(CrudTableContext);
    if (!context) {
        return null;
    }

    const { actions, title } = context;
    const { id, confirm, children } = props;
    const onDelete = props.delete || actions.delete;

    return (
        <Popconfirm
            title={confirm}
            placement="topRight"
            onConfirm={async () => {
                try {
                    await onDelete(id);
                } catch (e) {
                    message.error(e?.message || `删除${title}错误`);
                }
            }}
        >
            {children}
        </Popconfirm>
    );
}

export function CrudTable<T, TKey = number>(props: CrudTableProps<T, TKey>) {
    const {
        service,
        dependencies,
        title,
        actionRef,
        recordInsertAt,
        rowKey,
        simpleSearches,
        toolBarRender,
        fields,
        globalDataHookName,
        columns,
        newVersion,
        serverPagination,
        customFilters,
        frontendFilters,
        ...tableProps
    } = props;

    const newRowKey = rowKey || 'id';

    const {
        loading,
        loadData,
        pagination,
        setPagination,
        setSorter,
        create,
        update,
        delete: remove,
        filterable,
    } = useCrud({
        title,
        service,
        dependencies,
        fields,
        rowKey: newRowKey,
        recordInsertAt,
        globalDataHookName,
        newVersion,
        serverPagination,
        customFilters,
        frontendFilters,
    });

    const actions = useCreation<CrudActionRef<T, TKey>>(
        () => ({
            create,
            update,
            delete: remove,
            refresh: reset => {
                return loadData(reset ? { ...pagination, current: 1 } : null);
            },
            triggerSearch: filterable.triggerSearch,
        }),
        [],
    );

    if (actionRef) {
        actionRef[actionsSymbol] = actions;
    }

    const [columnsState, setColumnsState] = useState<Record<string, ColumnsState>>({});

    const [exporting, setExporting] = useState(false);

    const [headerFilter, setHeaderFilter] = useState<Record<string, any>>({});

    function getExportData() {
        let result = filterable.filtered;
        if (headerFilter != null) {
            for (let [k, v] of Object.entries(headerFilter)) {
                const column = columns.find(x => x.dataIndex == k || x.key == k);
                const currentFilter = column.onFilter;
                if (typeof currentFilter == 'function' && v != null) {
                    result = result.filter(x => {
                        if (!Array.isArray(v)) {
                            return currentFilter(v, x);
                        } else {
                            for (let item of v) {
                                if (currentFilter(item, x)) {
                                    return true;
                                }
                            }
                            return false;
                        }
                    });
                }
            }
        }
        return result;
    }

    return (
        <CrudTableContext.Provider value={{ actions, title }}>
            <filterable.Provider>
                <ProTable<T>
                    headerTitle={`${title}列表`}
                    {...tableProps}
                    columns={filterable.filtered}
                    rowKey={newRowKey}
                    dataSource={filterable.filtered}
                    loading={loading}
                    options={{
                        reload: () => loadData(),
                    }}
                    pagination={pagination}
                    onChange={(pagination, filter, sorter) => {
                        setPagination(pagination);
                        setHeaderFilter(filter);
                        setSorter(Array.isArray(sorter) ? sorter[0] : sorter);
                    }}
                    toolBarRender={
                        toolBarRender === false
                            ? false
                            : (action, rows) => [
                                  <SimpleFilterForm key="search">{simpleSearches}</SimpleFilterForm>,
                                  ...(toolBarRender?.(action, rows) || []),
                                  <Tooltip title="导出" key="reserve__export">
                                      <ExportOutlined
                                          onClick={async () => {
                                              if (!exporting) {
                                                  setExporting(true);
                                                  const { exportXls } = await import('./exportXls');
                                                  exportXls(columns, columnsState, getExportData(), title, true);
                                                  setExporting(false);
                                              }
                                          }}
                                          style={{
                                              fontSize: 17,
                                              cursor: exporting ? 'none' : 'pointer',
                                              padding: '2.5px 0',
                                          }}
                                      />
                                  </Tooltip>,
                              ]
                    }
                    onColumnsStateChange={setColumnsState}
                />
            </filterable.Provider>
        </CrudTableContext.Provider>
    );
}
