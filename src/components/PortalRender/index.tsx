import React, { useEffect, useRef, useState } from 'react';
import { createContainer } from 'unstated-next';
import { createPortal } from 'react-dom';

const portalContext = createContainer(() => {
    const [portalDom, setPortalDom] = useState<HTMLDivElement>(null);
    return { portalDom, setPortalDom };
});

export const PortalProvider = portalContext.Provider;

export function PortalReceiver() {
    const { setPortalDom } = portalContext.useContainer();
    const dom = useRef<HTMLDivElement>();

    useEffect(() => {
        setPortalDom(dom.current);
    }, []);

    return <div ref={dom}></div>;
}

export function PortalSender(props: React.PropsWithChildren<{}>) {
    const { children } = props;
    const { portalDom } = portalContext.useContainer();

    return portalDom ? createPortal(children, portalDom) : null;
}
