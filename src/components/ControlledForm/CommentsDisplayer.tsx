import React, { useState } from 'react';
import { Avatar, Button, Comment, Input, List, message, Popconfirm, Space, Spin } from 'antd';
import { PlusOutlined, UserOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import useAccount from '@/hooks/useAccount';
import { WorkflowComment } from '@/models/common/WorkflowComment';
import service from '@/services/worflow.service';
import styles from './CommentsDisplayer.less';

export interface CommentDisplayerProps {
    taskId: string;
    readonly: boolean;
}

export interface CommentItemProps {
    comment: WorkflowComment;
    userId: number;
    taskId: string;
    readonly: boolean;
    onUpdate: (item: WorkflowComment) => void;
    onDelete: (id: number) => void;
}

function CommentItem(props: CommentItemProps) {
    const { comment, userId, taskId, readonly, onUpdate, onDelete } = props;

    const [deleteChecking, setDeleteChecking] = useState(false);
    const [updating, setUpdating] = useState(false);
    const [editingText, setEditingText] = useState('');
    const [editLoading, setEditLoading] = useState(false);

    const startUpdate = () => {
        setUpdating(true);
        setEditingText(comment.comment);
    };

    const submitUpdate = async () => {
        setEditLoading(true);
        try {
            const result = await service.updateComment(comment.id, editingText);
            onUpdate(result.data);
            message.success(result.message);
        } catch (e) {
            message.error(e.message || '提交失败');
        }
        setEditLoading(false);
        setUpdating(false);
    };

    const submitDelete = async () => {
        setEditLoading(true);
        try {
            const result = await service.deleteComment(comment.id);
            onDelete(comment.id);
            message.success(result.message);
        } catch (e) {
            message.error(e.message || '提交失败');
        }
        setEditLoading(false);
        setDeleteChecking(false);
    };

    return (
        <Comment
            author={comment.commentator}
            avatar={<Avatar icon={<UserOutlined />} />}
            content={
                !updating ? (
                    comment.comment
                ) : (
                    <>
                        <Spin spinning={editLoading}>
                            <Input.TextArea
                                style={{ marginTop: 12 }}
                                value={editingText}
                                onChange={e => setEditingText(e.target.value)}
                            />
                            <Space style={{ marginTop: 8 }}>
                                <Button size="small" onClick={() => setUpdating(false)}>
                                    取消
                                </Button>
                                <Button size="small" type="primary" onClick={submitUpdate}>
                                    确认
                                </Button>
                            </Space>
                        </Spin>
                    </>
                )
            }
            datetime={comment.createTime.format('YYYY-MM-DD HH:mm:ss')}
            actions={
                readonly || userId !== comment.createUserId || updating
                    ? []
                    : [
                          <EditOutlined onClick={startUpdate} key="edit" />,
                          <Popconfirm
                              title="确认要删除吗？"
                              visible={deleteChecking}
                              onConfirm={submitDelete}
                              okButtonProps={{ loading: editLoading }}
                              onCancel={() => setDeleteChecking(false)}
                          >
                              <DeleteOutlined onClick={() => setDeleteChecking(true)} key="delete" />
                          </Popconfirm>,
                      ]
            }
        />
    );
}

export default function CommentDisplayer(props: CommentDisplayerProps) {
    const { taskId, readonly } = props;
    const req = useRequest(
        async () => {
            const response = await service.getCommentsByTask(taskId);
            return response.data;
        },
        { refreshDeps: [taskId] },
    );
    const show = !readonly || req.data?.length;

    const { useCurrentUser } = useAccount.useContainer();
    const currentUser = useCurrentUser();

    const [adding, setAdding] = useState(false);
    const [editingText, setEditingText] = useState('');
    const [editLoading, setEditLoading] = useState(false);

    const submitAdd = async () => {
        if (!editingText) {
            message.warn('评论不能为空！');
        } else {
            setEditLoading(true);
            try {
                const result = await service.addComment(taskId, editingText);
                req.mutate([...req.data, result.data]);
                message.success(result.message);
                setAdding(false);
            } catch (e) {
                message.error(e.message || '提交失败');
            }
            setEditLoading(false);
        }
    };

    return (
        <Spin spinning={req.loading}>
            {show ? (
                <Space className={styles.title}>
                    <div>流程备注</div>
                    {!readonly && !adding ? (
                        <a
                            onClick={() => {
                                setAdding(true);
                                setEditingText('');
                            }}
                        >
                            <PlusOutlined />
                        </a>
                    ) : null}
                </Space>
            ) : null}
            {!req.loading && show ? (
                <div className={styles.commentList}>
                    {req.data.length ? (
                        <List
                            dataSource={req.data}
                            rowKey="id"
                            renderItem={item => (
                                <li>
                                    <CommentItem
                                        comment={item}
                                        userId={currentUser.id}
                                        onUpdate={comment =>
                                            req.mutate(req.data.map(x => (x.id === comment.id ? comment : x)))
                                        }
                                        onDelete={id => req.mutate(req.data.filter(x => x.id !== id))}
                                        {...props}
                                    />
                                </li>
                            )}
                        />
                    ) : null}
                    {adding ? (
                        <Spin spinning={editLoading}>
                            <Input.TextArea
                                style={{ marginTop: 12 }}
                                value={editingText}
                                onChange={e => setEditingText(e.target.value)}
                            />
                            <Space style={{ marginTop: 8 }}>
                                <Button size="small" onClick={() => setAdding(false)}>
                                    取消
                                </Button>
                                <Button size="small" type="primary" onClick={submitAdd}>
                                    确认
                                </Button>
                            </Space>
                        </Spin>
                    ) : null}
                </div>
            ) : null}
        </Spin>
    );
}
