import { defaults } from 'lodash';
import React, { useContext } from 'react';
import { Button, Space } from 'antd';
import useMessages from '@/hooks/useMessages';
import { CustomResponse } from '@/models/common/CustomResponse';
import { WfEntityBase } from '@/models/common/WfEntityBase';
import wfService from '@/services/worflow.service';
import { SubmitButton, useFormData, FormItemGetter } from '../MyForm';
import { SubmitButtonProps } from '../MyForm/SubmitButton';
import subFormContext from '../MyForm/subFormContext';
import fillData from './fillData';
import DiagramDisplayer from './DiagramDisplayer';
import HistoryDrawer from './HistoryDrawer';
import CommentsDisplayer from './CommentsDisplayer';

export interface WfWorkingFormOperationProps<T extends WfEntityBase> {
    onCancel?: () => void;
    // 暂存的接口，若为null表示流程这一步不修改数据。
    onTempSave?: (val: T) => Promise<CustomResponse<T>>;
    // 审批后执行
    afterSubmit?: () => void;
    taskId: string;
    portfolioId?: number;
}

const propsContext = React.createContext<WfWorkingFormOperationProps<any>>(null);

export type WfSubmitButtonProps = Omit<SubmitButtonProps<any>, 'onSubmit' | 'afterSubmit'> & {
    branch?: string;
    requireSaveFirst?: boolean;
    complete?: (formData: any) => Promise<any>;
};

export function WfSubmitButton(props: WfSubmitButtonProps) {
    const context = useContext(propsContext);
    const { onTempSave, afterSubmit, taskId } = context;
    const { refresh: refreshMessage } = useMessages.useContainer();
    const { branch, requireSaveFirst, complete, ...otherProps } = defaults({ ...props }, { requireSaveFirst: true });
    return (
        <SubmitButton
            {...otherProps}
            onSubmit={async (formData, _, oldData) => {
                requireSaveFirst && onTempSave && (await onTempSave(formData));
                const response2 = complete ? await complete(formData) : await wfService.complete(taskId, '', branch);
                refreshMessage();
                return [oldData, response2.message];
            }}
            afterSubmit={afterSubmit}
        />
    );
}

export function singleBranchSubmit() {
    return (
        <WfSubmitButton type="primary" branch="accept">
            提交
        </WfSubmitButton>
    );
}

export function passSubmitButton() {
    return (
        <WfSubmitButton type="primary" branch="accept">
            通过
        </WfSubmitButton>
    );
}

export function rejectSubmitButton() {
    return (
        <WfSubmitButton
            danger
            requireSaveFirst={false}
            validate={false}
            branch="reject"
            confirm={{
                title: '确认驳回？',
            }}
        >
            驳回
        </WfSubmitButton>
    );
}

export function twoWaySubmit() {
    return (
        <>
            {rejectSubmitButton()}
            {passSubmitButton()}
        </>
    );
}

export function WfWorkingFormOperation<T extends WfEntityBase>(
    props: React.PropsWithChildren<WfWorkingFormOperationProps<T>>,
) {
    const { onTempSave, onCancel, taskId, portfolioId, children } = props;

    const formData = useFormData();
    const localData: WfEntityBase = formData.localData;

    const { fields } = useContext(subFormContext);

    const cancelDom = onCancel && <Button onClick={onCancel}>关闭</Button>;

    return (
        <propsContext.Provider value={props}>
            <FormItemGetter>
                {({ readonly }) => (
                    <>
                        <CommentsDisplayer taskId={taskId} readonly={readonly} />
                        <Space>
                            {cancelDom}
                            {localData && (
                                <>
                                    <DiagramDisplayer processId={localData.instanceId} />
                                    <HistoryDrawer processId={localData.instanceId} portfolioId={portfolioId} />
                                </>
                            )}
                            {readonly ? null : (
                                <Space>
                                    {onTempSave && (
                                        <SubmitButton
                                            validate={false}
                                            onSubmit={async (formData, _, oldData) => {
                                                const response = await onTempSave(formData);
                                                return [fillData(oldData, fields, response.data), response.message];
                                            }}
                                        >
                                            暂存
                                        </SubmitButton>
                                    )}
                                    {children}
                                </Space>
                            )}
                        </Space>
                    </>
                )}
            </FormItemGetter>
        </propsContext.Provider>
    );
}
