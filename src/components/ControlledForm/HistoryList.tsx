import React from 'react';
import { groupBy } from 'lodash';
import { Comment, Spin, Timeline } from 'antd';
import { blue } from '@ant-design/colors';
import { MessageOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import service from '@/services/worflow.service';
import styles from './HistoryList.less';
import { WorkflowTask } from '@/models/common/WorkflowTask';
import portfolioService from '@/pages/manager/operation/services/portfolio.service';

export interface HistoryListProps {
    processId: string;
    portfolioId?: number;
    __reportApprovalSpecial?: number;
    processComplete?: boolean;
    extraItems?: React.ReactNode[];
    currentTask?: WorkflowTask;
}

export default function(props: HistoryListProps) {
    const { processId, portfolioId, __reportApprovalSpecial, extraItems, currentTask } = props;

    const req = useRequest(() => service.getOperations(processId), { refreshDeps: [processId] });
    const commentsReq = useRequest(
        async () => {
            const response = await service.getCommentByInstance(processId);
            const grouped = groupBy(response.data, 'taskId');
            for (let k in grouped) {
                grouped[k].sort((a, b) => a.id - b.id);
            }
            return grouped;
        },
        { refreshDeps: [processId], initialData: {} },
    );

    const modifyReq = useRequest(
        async () => {
            if (!portfolioId) {
                return null;
            }
            const response = await portfolioService.getModify(portfolioId);
            return response.data;
        },
        { refreshDeps: [portfolioId] },
    );

    return (
        <Spin spinning={req.loading || commentsReq.loading}>
            {modifyReq.data?.changeName && <p style={{ color: blue[5] }}>曾用名：{modifyReq.data.oldName}</p>}
            <Timeline>
                {req.data?.data?.map((x, idx) => (
                    <Timeline.Item key={x.id}>
                        <p>
                            {__reportApprovalSpecial
                                ? x.updateTime.subtract(__reportApprovalSpecial, 'minute').format('YYYY-MM-DD HH:mm:ss')
                                : x.updateTime.format('YYYY-MM-DD HH:mm:ss')}
                        </p>
                        <p>{x.description}</p>
                        {x.type == 'TASK_COMPLETE' && commentsReq.data[x.taskId] ? (
                            <div className={styles.comments}>
                                {commentsReq.data[x.taskId].map(comment => (
                                    <Comment
                                        avatar={<MessageOutlined />}
                                        author={comment.commentator}
                                        content={comment.comment}
                                        datetime={comment.createTime.format('YYYY-MM-DD HH:mm:ss')}
                                    />
                                ))}
                            </div>
                        ) : null}
                    </Timeline.Item>
                ))}
                {currentTask && (
                    <Timeline.Item key={currentTask.taskId} color="red">
                        <p>当前流程：{currentTask.taskName}</p>
                        <p>经办人：{currentTask.assigneeNames.join('，')}</p>
                    </Timeline.Item>
                )}
                {extraItems}
            </Timeline>
        </Spin>
    );
}
