import { set } from 'lodash';
import React, { useContext } from 'react';
import { Button, Space } from 'antd';
import { SubmitButton, useParentForm } from '../MyForm';
import subFormContext from '../MyForm/subFormContext';
import { CustomResponse } from '@/models/common/CustomResponse';

// 这里T代表子表单的类型
export interface NormalFormProps<T> {
    onCancel?: () => void;
    onCreate?: (data: any) => Promise<CustomResponse<T>>;
    onUpdate?: (id: number, data: any) => Promise<CustomResponse<T>>;
    doNotClose?: boolean;
}

export function NormalFormOperation<T>(props: NormalFormProps<T>) {
    const { onCancel, onCreate, onUpdate, doNotClose } = props;
    const form = useParentForm<T>();
    const { fields } = useContext(subFormContext);

    return (
        <Space>
            {onCancel && <Button onClick={onCancel}>返回</Button>}
            <Button onClick={() => form.resetAllFields()}>重置</Button>
            <SubmitButton<any>
                doNotClose={doNotClose}
                type="primary"
                onSubmit={async (formData, id, oldData) => {
                    const result = await Promise.resolve(id == null ? onCreate?.(formData) : onUpdate?.(id, formData));
                    const message = result?.message || (id == null ? '创建成功' : '修改成功');
                    return [
                        result.data == null
                            ? oldData
                            : fields.length == 0
                            ? result.data
                            : set(Object.assign({}, oldData), fields, result.data),
                        message,
                    ];
                }}
            >
                提交
            </SubmitButton>
        </Space>
    );
}
