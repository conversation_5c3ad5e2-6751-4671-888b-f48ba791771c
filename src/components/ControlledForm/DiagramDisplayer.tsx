import React from 'react';
import { But<PERSON>, Popover, Image } from 'antd';
import service from '@/services/worflow.service';

export interface DiagramDisplayerProps {
    processId: string;
}

export default function(props: DiagramDisplayerProps) {
    const { processId } = props;

    return (
        <Popover
            trigger="click"
            content={
                <Image src={service.getDiagramUrl(processId)} preview={false} />
            }
        >
            <Button type="link">流程图</Button>
        </Popover>
    );
}
