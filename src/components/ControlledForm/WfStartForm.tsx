import React, { useContext, useEffect, useState } from 'react';
import { Button, Space } from 'antd';
import useAccount from '@/hooks/useAccount';
import useMessages from '@/hooks/useMessages';
import { CustomResponse } from '@/models/common/CustomResponse';
import { WfEntityBase } from '@/models/common/WfEntityBase';
import wfService from '@/services/worflow.service';
import { FormItemSetter, SubmitButton, useFormData } from '../MyForm';
import subFormContext from '../MyForm/subFormContext';
import fillData from './fillData';
import DiagramDisplayer from './DiagramDisplayer';
import HistoryDrawer from './HistoryDrawer';

const context = React.createContext({
    isEditing: true,
    setIsEditing: (_: boolean) => {},
});

export interface WfStartFormProps {
    children: (isEditing: boolean) => JSX.Element;
}

export function WfStartForm(props: WfStartFormProps) {
    const { children } = props;
    const [isEditing, setIsEditing] = useState(true);
    const formData = useFormData();
    const id = formData.id;
    const localData: WfEntityBase = formData.localData;

    useEffect(() => {
        if (localData?.processComplete) {
            setIsEditing(false);
        } else {
            setIsEditing(true);
        }
    }, [id, localData]);

    return (
        <context.Provider value={{ isEditing, setIsEditing }}>
            <FormItemSetter readonly={!isEditing || localData?.open}>{children(isEditing)}</FormItemSetter>
        </context.Provider>
    );
}

// 这里T代表子表单的类型
export interface WfStartFormOperationProps<T extends WfEntityBase, U> {
    onCancel?: () => void;
    onTempSave?: (formData: any) => Promise<CustomResponse<T>>;
    onStart?: () => Promise<CustomResponse<T>>;
    // 开始后，修改父表单的项目
    afterStart?: (data: U) => U;
    // 终止流程后，修改父表单的项目
    afterTerminate?: (data: U) => U;
    // 禁止重新编辑
    disableReediting?: boolean;
    // 显示取消流程按钮
    showCancel?: boolean;
    // 取消流程时的操作
    onTerminate?: (taskId: string) => Promise<CustomResponse<any>>;
    // 开始流程时额外内容
    startProcessExtraEl?: JSX.Element;
}

export function WfStartFormOperation<T extends WfEntityBase, U = never>(props: WfStartFormOperationProps<T, U>) {
    const {
        onCancel,
        onTempSave,
        onStart,
        afterStart,
        afterTerminate,
        disableReediting,
        showCancel,
        onTerminate,
        startProcessExtraEl,
    } = props;
    const formData = useFormData();
    const localData: T = formData.localData;
    const { fields } = useContext(subFormContext);
    const { isEditing, setIsEditing } = useContext(context);
    const currentUser = useAccount.useContainer().useCurrentUser();
    const { refresh: refreshMessage } = useMessages.useContainer();

    const cancelDom = onCancel && <Button onClick={onCancel}>返回</Button>;

    if (!isEditing) {
        return (
            <Space>
                {cancelDom}
                <HistoryDrawer processId={localData?.instanceId} />
                {!disableReediting && <Button onClick={() => setIsEditing(true)}>重新编辑</Button>}
            </Space>
        );
    } else {
        if (localData?.open) {
            if (localData.initiatorId === currentUser?.id || showCancel) {
                return (
                    <Space>
                        {cancelDom}
                        <DiagramDisplayer processId={localData.instanceId} />
                        <HistoryDrawer processId={localData.instanceId} />
                        <SubmitButton
                            validate={false}
                            confirm={{ title: '确认取消流程吗？' }}
                            onSubmit={async (formData, _, oldData: any) => {
                                const response = await (onTerminate
                                    ? onTerminate(localData.instanceId)
                                    : wfService.terminate(localData.instanceId));
                                const newData = fillData(oldData, fields, {
                                    ...localData,
                                    ...formData,
                                    open: false,
                                });
                                refreshMessage();
                                return [afterTerminate ? afterTerminate(newData) : newData, response.message];
                            }}
                        >
                            取消流程
                        </SubmitButton>
                    </Space>
                );
            } else {
                return (
                    <Space>
                        {cancelDom}
                        <DiagramDisplayer processId={localData.instanceId} />
                        <HistoryDrawer processId={localData.instanceId} />
                    </Space>
                );
            }
        } else {
            return (
                <Space>
                    {cancelDom}
                    {onTempSave && (
                        <SubmitButton
                            validate={false}
                            onSubmit={async (formData, _, oldData) => {
                                const response = await onTempSave(formData);
                                return [fillData(oldData, fields, response.data), response.message];
                            }}
                        >
                            暂存
                        </SubmitButton>
                    )}
                    {onStart && (
                        <SubmitButton
                            type="primary"
                            onSubmit={async (formData, _, oldData) => {
                                onTempSave && (await onTempSave(formData));
                                const response2 = await onStart();
                                const newData = fillData(oldData, fields, response2.data);
                                refreshMessage();
                                return [afterStart ? afterStart(newData) : newData, response2.message];
                            }}
                        >
                            提交
                        </SubmitButton>
                    )}
                    {startProcessExtraEl}
                </Space>
            );
        }
    }
}
