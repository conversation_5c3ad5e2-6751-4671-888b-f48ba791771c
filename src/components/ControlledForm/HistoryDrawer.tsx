import { Button, ButtonProps, Drawer } from 'antd';
import React, { cloneElement, useState } from 'react';
import HistoryList from './HistoryList';
import { WorkflowTask } from '@/models/common/WorkflowTask';

export interface HistoryDrawerProps {
    processId: string;
    portfolioId?: number;
    buttonProps?: ButtonProps;
    extraItems?: React.ReactNode[];
    trigger?: JSX.Element;
    currentTask?: WorkflowTask;
}

export default function(props: HistoryDrawerProps) {
    const { processId, portfolioId, buttonProps, extraItems, currentTask } = props;
    const [visible, setVisible] = useState(false);

    const trigger = props.trigger ? (
        cloneElement(props.trigger, { onClick: () => setVisible(true) })
    ) : (
        <Button type="link" {...(buttonProps || {})} onClick={() => setVisible(true)}>
            流程记录
        </Button>
    );
    return (
        <>
            {trigger}
            <Drawer visible={visible} onClose={() => setVisible(false)} title="流程记录" width={360}>
                <HistoryList
                    processId={processId}
                    portfolioId={portfolioId}
                    extraItems={extraItems}
                    currentTask={currentTask}
                />
            </Drawer>
        </>
    );
}
