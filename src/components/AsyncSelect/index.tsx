import React from 'react';
import { message, Select } from 'antd';
import { SelectProps } from 'antd/lib/select';
import { useRequest } from 'ahooks';
import { CustomResponse } from '@/models/common/CustomResponse';

type ValueType = string | number | string[] | number[];

export type AsyncSelectProps<TData, TVal extends ValueType> = SelectProps<TVal> & {
    service: () => Promise<CustomResponse<TData[]>>;
    valueRow?: string;
    labelRow: string;
};

export default function AsyncSelect<TData, TVal extends ValueType>(
    props: React.PropsWithChildren<AsyncSelectProps<TData, TVal>>,
) {
    const { service, valueRow, labelRow, ...otherProps } = props;

    const req = useRequest(
        async () => {
            try {
                const response = await service();
                return response.data;
            } catch (e) {
                message.error(e?.message || '获取选项数据失败');
                return [];
            }
        },
        { initialData: [] },
    );

    return (
        <Select
            {...otherProps}
            loading={req.loading}
            options={req.data.map(x => ({ label: x[labelRow], value: x[valueRow || 'id'] }))}
        />
    );
}
