import React, { PropsWithChildren } from 'react';
import useAccount from './useAccount';
import useJira from './useJira';
import useMessages from './useMessages';
import useNotification from './useNotification';
import useUserList from './useUserList';

const GlobalDataProvider: React.FC<PropsWithChildren> = props => {
    const { children } = props;

    return (
        <useAccount.Provider>
            <useUserList.Provider>
                <useNotification.Provider>
                    <useMessages.Provider>
                        <useJira.Provider>{children}</useJira.Provider>
                    </useMessages.Provider>
                </useNotification.Provider>
            </useUserList.Provider>
        </useAccount.Provider>
    );
};

export default GlobalDataProvider;
