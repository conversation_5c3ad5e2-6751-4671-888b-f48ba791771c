import { useRef } from 'react';
import { createContainer } from 'unstated-next';
import { useRequest } from 'ahooks';
import { User } from '@/models/account/User';
import service from '@/services/account.service';

export default createContainer(() => {
    const promiseDict = useRef<Record<string, Promise<User[]>>>({});

    const useUserList = (perms: string[]) => {
        const sorted = [...(perms || [])].sort();
        const key = sorted.join(';');
        if (!promiseDict.current[key]) {
            promiseDict.current[key] = service.getList(sorted).then(x => x.data);
        }
        return useRequest(() => promiseDict.current[key], { refreshDeps: [key] });
    };

    return { useUserList };
});
