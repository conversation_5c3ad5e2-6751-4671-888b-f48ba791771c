import { useEffect, useState } from 'react';
import { useRequest } from 'ahooks';
import { notification as notify } from 'antd';
import { history } from 'umi';
import { createContainer } from 'unstated-next';
import { Client } from '@stomp/stompjs';
import useAccount from './useAccount';
import conf from '@/conf';

export default createContainer(() => {
    const [client, setClient] = useState<Client>(null);
    const [notification, setNotification] = useState(null);

    const { useCurrentUser } = useAccount.useContainer();
    const currentUser = useCurrentUser();

    const deactivate = useRequest(
        async () => {
            await client.deactivate();
            setClient(null);
            setNotification(null);
        },
        { manual: true },
    );

    const prefix = '/topic';

    useEffect(() => {
        if (!currentUser && client) {
            deactivate.run();
        } else if (currentUser && !client) {
            const client = new Client();
            client.brokerURL = conf.wsUrl;

            client.onConnect = () => {
                // subscribe

                console.log('Websocket connected.');

                client.subscribe(`${prefix}/jira-user${currentUser.id}`, n => {
                    const { title, description, messageId, requireHandle } = JSON.parse(n.body);

                    notify.open({
                        message: title,
                        description,
                        onClick: requireHandle ? () => history.replace(`/message_center/${messageId}`) : undefined,
                    });

                    setNotification(n);
                });

                client.subscribe(`${prefix}/open-day${currentUser.id}`, n => {
                    const { title, description, messageId, requireHandle } = JSON.parse(n.body);

                    notify.open({
                        message: title,
                        description,
                        onClick: requireHandle ? () => history.replace(`/message_center/${messageId}`) : undefined,
                    });

                    setNotification(n);
                });

                client.subscribe(`${prefix}/workflow`, setNotification);
            };

            client.activate();
            setClient(client);
        }
    }, [currentUser]);

    return { notification };
});
