import { useState, useEffect } from 'react';
import { createContainer } from 'unstated-next';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import { history } from 'umi';
import service from '@/services/jira.service';
import { JiraUser } from '@/models/jira/JiraUser';
import useMessages from './useMessages';

export default createContainer(() => {
    const [currentUserJira, setCurrentUserJira] = useState<JiraUser>(null);
    const { refresh } = useMessages.useContainer();

    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('oauth_token');
        const verifier = urlParams.get('oauth_verifier');

        const fetchData = async () => {
            if (token && verifier) {
                history.replace('/');
                if (verifier == 'denied') {
                    message.error('绑定失败');
                    unbindJira.run();
                } else {
                    try {
                        await service.getJiraAccessToken(token, verifier);
                    } catch {
                        message.error('获取Jira账户信息失败');
                    }
                }
            }

            if (!currentUserJira) {
                try {
                    const response = await service.getMySelf();
                    setCurrentUserJira(response.data);
                } catch {}
            }
        };

        fetchData();
    }, []);

    const bindJira = useRequest(
        async () => {
            try {
                await service.getJiraAuthUrl();
            } catch {
                message.error('获取数据失败');
            }
        },
        { manual: true },
    );

    const unbindJira = useRequest(
        async () => {
            await service.unbindJira();
            setCurrentUserJira(null);
            refresh();
        },
        { manual: true },
    );

    return {
        currentUserJira,
        bindJira: bindJira.run,
        unbindJira: unbindJira.run,
    };
});
