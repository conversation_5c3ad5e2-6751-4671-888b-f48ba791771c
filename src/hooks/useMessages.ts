import { useEffect, useState } from 'react';
import { useRequest } from 'ahooks';
import { createContainer } from 'unstated-next';
import service from '@/services/message.service';
import { Message } from '@/models/utils/Message';
import useAccount from './useAccount';
import useNotification from './useNotification';

export default createContainer(() => {
    const { useCurrentUser } = useAccount.useContainer();
    const currentUser = useCurrentUser();
    const msgReq = useRequest(() => service.getList(false), {
        ready: !!currentUser,
        refreshOnWindowFocus: true,
        pollingInterval: 30000,
    });

    const [readIds, setReadIds] = useState<number[]>([]);
    const [markedAmount, setMarkedAmount] = useState(0);
    const [msg, setMsg] = useState<{ messages: Message[]; todos: Message[] }>({
        messages: [],
        todos: [],
    });

    useEffect(() => {
        if (msgReq.data?.data) {
            const filterKeywords = ['加仓申请', '减仓申请', '代销申购申请', '代销赎回申请'];
            const shouldFilter = (message: Message) =>
                !filterKeywords.some(keyword => message.title?.includes(keyword));
            setMsg({
                messages: msgReq.data.data.filter(x => !x.requireHandle).filter(shouldFilter),
                todos: msgReq.data.data.filter(x => x.requireHandle).filter(shouldFilter),
            });
            setMarkedAmount(0);
            setReadIds([]);
        }
    }, [msgReq.data]);

    const markRead = (id: number) => {
        if (!readIds.includes(id)) {
            setReadIds([...readIds, id]);
            setMarkedAmount(markedAmount + 1);
            service.setRead(id);
        }
    };

    const markAllRead = () => {
        setMsg({ messages: [], todos: msg.todos });
        setMarkedAmount(0);
        setReadIds([]);
        service.setReadAll();
    };

    const setProcessed = (id: number) => {
        setMsg({
            messages: msg.messages,
            todos: msg.todos.filter(x => x.id !== id),
        });
    };

    const { notification } = useNotification.useContainer();

    useEffect(() => {
        if (notification) {
            msgReq.refresh();
        }
    }, [notification]);

    return {
        messages: msg.messages,
        todos: msg.todos,
        amount: msg.messages.length + msg.todos.length - markedAmount,
        refresh: msgReq.refresh,
        readIds,
        markRead,
        markAllRead,
        setProcessed,
    };
});
