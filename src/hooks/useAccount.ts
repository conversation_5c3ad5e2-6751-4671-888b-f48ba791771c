import { DetailedUser } from '@/models/account/User';
import service from '@/services/account.service';
import { useRequest } from 'ahooks';
import { notification } from 'antd';
import { mapValues } from 'lodash';
import { useEffect, useState } from 'react';
import { history } from 'umi';
import { createContainer } from 'unstated-next';

export default createContainer(() => {
    const [currentUser, setCurrentUser] = useState<DetailedUser>(null);

    const getCurrentUser = useRequest(service.getInfo, { manual: true });

    useEffect(() => {
        if (getCurrentUser.data?.data) {
            setCurrentUser(getCurrentUser.data.data);
        }
    }, [getCurrentUser.data]);

    // 获取当前用户，仅第一次调用时调用后端接口
    const useCurrentUser = () => {
        if (!currentUser && !getCurrentUser.loading && !getCurrentUser.error) {
            getCurrentUser.run();
        }
        return currentUser;
    };

    const login = useRequest(
        async (username: string, password: string) => {
            try {
                const response = await service.login(username, password);
                setCurrentUser(response.data);
                if (response.data.pwdResetRequired) {
                    notification.success({
                        message: '登录成功，请修改密码',
                    });
                    location.href = `${location.origin}/account/changePassword`;
                } else {
                    notification.success({
                        message: '登录成功',
                    });
                    location.href = location.origin;
                }
            } catch (e) {
                notification.error({
                    message: '登录失败',
                    description: e.message,
                });
            }
        },
        { manual: true },
    );

    const loginFeishu = useRequest(
        async (code: string) => {
            try {
                const response = await service.loginFeishu(code);
                setCurrentUser(response.data);
                notification.success({ message: '登录成功' });
                location.href = location.origin;
            } catch (e) {
                notification.error({
                    message: '登录失败',
                    description: e.message,
                });
                history.replace('/account/login_feishu?stale=1');
            }
        },
        { manual: true },
    );

    const changePassword = useRequest(
        async (oldPwd: string, newPwd: string) => {
            try {
                await service.changePassword(oldPwd, newPwd);
                setCurrentUser({ ...currentUser, pwdResetRequired: false });
                history.replace('/');
            } catch (e) {
                notification.error({
                    message: '修改密码失败',
                    description: e.message,
                });
            }
        },
        { manual: true },
    );

    const logout = useRequest(
        async () => {
            await service.logout();
            setCurrentUser(null);
            history.replace('/account/login_feishu?stale=1');
        },
        { manual: true },
    );

    return {
        useCurrentUser,
        login: login.run,
        changePassword: changePassword.run,
        logout: logout.run,
        loginFeishu: loginFeishu.run,
        loading: mapValues({ login, changePassword, logout, loginFeishu }, x => x.loading),
    };
});
