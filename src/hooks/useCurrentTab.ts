import { useState, useEffect, useContext, useCallback } from 'react';
const TabsContext = React.createContext();
useContext;
function useCurrentTab({ title = '' } = {}) {
    const { tabs, setTabs } = useContext(TabsContext);
    const [tab, setTab] = useState({ url: '', messages: [] });

    useEffect(() => {
        setTab({ ...tab, title });
    }, [title]);

    const openTab = useCallback((url, { onMessage } = {}) => {
        setTabs(tabs => [...tabs, { url, onMessage }]);
    }, []);

    const refresh = useCallback(() => {
        setTabs(tabs => tabs.map(t => (t.url === tab.url ? { ...tab, refreshed: true } : t)));
    }, [tab]);

    const close = useCallback(() => {
        setTabs(tabs => tabs.filter(t => t.url !== tab.url));
    }, [tab]);

    const sendMessageToParent = useCallback(
        message => {
            setTabs(tabs => tabs.map(t => (t.url === tab.url ? { ...t, messages: [...t.messages, message] } : t)));
        },
        [tab],
    );

    return { openTab, refresh, close, sendMessageToParent };
}

export default useCurrentTab;
